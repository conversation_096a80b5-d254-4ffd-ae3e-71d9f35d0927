# 动态提示词生成系统升级

## 概述

本次升级将原本硬编码的"台海危机"提示词系统改造为动态生成的通用提示词系统，使战略推演系统能够适应任何场景，而不仅限于台海危机。

## 主要修改

### 1. 智能体提示词动态化

**修改位置**: `demo_app.py` 第1451-1547行

**原来**: 硬编码的智能体提示词，固定提到"台海危机"
**现在**: 动态生成函数 `generate_agent_prompt(agent_id, scenario_title, scenario_context)`

```python
def generate_agent_prompt(agent_id, scenario_title, scenario_context):
    """动态生成智能体提示词"""
    # 根据场景标题和背景动态生成提示词
```

**改进效果**:
- 智能体提示词现在包含具体的场景标题和背景
- 支持任意场景，不再局限于台海危机
- 保持每个智能体的个性特征和专业特点

### 2. 递进式研讨提示动态化

**修改位置**: `demo_app.py` 第1430行

**原来**: 
```python
return f"""你是{profile['identity']}，正在参与台海危机应对策略的高级智囊团研讨。
```

**现在**:
```python
return f"""你是{profile['identity']}，正在参与{scenario.get('title', '战略决策')}的高级智囊团研讨。
```

### 3. 讨论总结动态化

**修改位置**: `demo_app.py` 第1857-1870行

**原来**: 硬编码"台海危机讨论"
**现在**: 动态使用场景标题和背景

```python
summary_prompt = f"""请作为推演主持人，对以下关于"{scenario['title']}"的讨论进行总结：

场景背景：{scenario['context']}
```

### 4. 最终决策问题动态生成

**修改位置**: `demo_app.py` 第1911-1962行

**新增功能**: `generate_decision_questions(scenario_title, scenario_context)`

根据场景类型自动生成相应的决策问题：

- **台海/台湾场景**: 军事干预、盟友协调等问题
- **贸易场景**: 制裁措施、经济影响等问题  
- **中东/军事干预场景**: 军事行动、人道主义等问题
- **通用场景**: 行动方案、资源需求等问题

### 5. 错误处理消息动态化

**修改位置**: 
- 第1887-1890行: 总结生成失败时的默认消息
- 第1974-1977行: 决策生成失败时的默认消息

**改进**: 所有错误处理消息现在都使用动态的场景标题

## 测试验证

创建了 `test_dynamic_prompts.py` 测试脚本，验证了以下场景：

1. ✅ 台海危机应对策略
2. ✅ 中美贸易战升级  
3. ✅ 中东军事干预决策
4. ✅ 网络安全威胁应对

每个场景都能正确生成：
- 个性化的智能体提示词
- 场景相关的决策问题
- 适当的背景信息整合

## 兼容性

- ✅ 保持与现有功能的完全兼容
- ✅ 原有的台海危机场景仍然正常工作
- ✅ 新增场景能够无缝集成
- ✅ 不影响其他模块功能

## 使用方法

### 创建新场景

现在可以轻松创建任何类型的战略推演场景：

```python
new_scenario = {
    "title": "网络安全威胁应对",
    "description": "应对大规模网络攻击的战略决策",
    "context": "发现大规模网络攻击，疑似来自敌对国家，美国应如何应对？",
    "considerations": ["技术应对", "外交抗议", "经济制裁", "军事选项"]
}
```

### 智能体自动适应

所有智能体会自动适应新场景：
- 特朗普: 从美国利益角度分析网络安全威胁
- 拜登: 强调国际合作应对网络攻击
- 军事专家: 评估网络战争的军事选项
- 政治专家: 分析网络攻击的政治影响

## 技术细节

### 动态提示词生成流程

1. **场景识别**: 根据场景标题识别类型
2. **模板选择**: 选择对应的提示词模板
3. **内容注入**: 将场景信息注入模板
4. **个性化**: 保持智能体个性特征
5. **上下文整合**: 结合讨论历史

### 决策问题生成逻辑

```python
if "台海" in scenario_title or "台湾" in scenario_title:
    # 生成军事干预相关问题
elif "贸易" in scenario_title:
    # 生成贸易政策相关问题
elif "中东" in scenario_title or "军事干预" in scenario_title:
    # 生成军事行动相关问题
else:
    # 生成通用战略问题
```

## 未来扩展

这个动态系统为未来扩展奠定了基础：

1. **更多场景类型**: 可以轻松添加新的场景识别规则
2. **智能体专业化**: 可以为特定场景添加专业智能体
3. **多语言支持**: 可以扩展为多语言提示词生成
4. **AI辅助生成**: 可以集成AI来自动生成场景相关的提示词

## 总结

通过这次升级，战略推演系统从一个专门针对台海危机的工具，转变为一个通用的战略决策仿真平台，能够处理各种类型的战略场景，大大提升了系统的实用性和扩展性。
