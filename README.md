# Strategic Simulation System

A comprehensive military strategic simulation system based on the TinyTroupe framework, supporting multi-agent collaboration, discussion, decision analysis, and simulation review.

## 🎯 Overview

This system enables strategic military simulations with AI agents representing different roles and perspectives. The system facilitates structured discussions, decision-making processes, and comprehensive analysis of strategic scenarios.

### Key Features

- **🤖 Multi-Agent System**: Trump agent, moderator, domain experts, and custom agents
- **💬 Group Discussions**: Customizable turn orders, round control, and user participation
- **🧠 Memory Management**: Long-term memory, context management, and conversation history
- **📚 Knowledge Enhancement**: RAG retrieval, dedicated knowledge bases, and real-time updates
- **⚙️ Simulation Control**: Process management, decision analysis, and after-action reviews
- **🖥️ User Interfaces**: Web interface and CLI tools for easy interaction

## 🏗️ Architecture

```
strategic_simulation/
├── README.md                    # Project documentation
├── requirements.txt             # Project dependencies
├── main.py                      # Main entry point
├── config/                      # Configuration files
│   ├── agents_config.json       # Agent configurations
│   ├── simulation_config.json   # Simulation settings
│   └── knowledge_base/          # Knowledge base files
├── src/                         # Source code
│   ├── agents/                  # Agent implementations
│   │   ├── base_agent.py        # Base agent class
│   │   ├── trump_agent.py       # Trump agent
│   │   ├── moderator_agent.py   # Moderator agent
│   │   ├── expert_agents.py     # Domain expert agents
│   │   └── custom_agent.py      # Custom agent system
│   ├── simulation/              # Simulation control
│   │   ├── discussion_manager.py # Discussion management
│   │   ├── simulation_controller.py # Main controller
│   │   └── decision_analyzer.py  # Decision analysis
│   ├── memory/                  # Memory management
│   │   ├── memory_manager.py    # Memory system
│   │   └── conversation_store.py # Conversation storage
│   ├── knowledge/               # Knowledge management
│   │   ├── rag_system.py        # RAG retrieval system
│   │   └── knowledge_manager.py # Knowledge management
│   └── ui/                      # User interfaces
│       ├── web_interface.py     # Streamlit web UI
│       └── cli_interface.py     # Command-line interface
├── tests/                       # Test files
├── examples/                    # Examples and demos
└── data/                        # Data storage
    ├── conversations/           # Conversation records
    ├── decisions/               # Decision records
    └── reports/                 # Simulation reports
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- OpenAI API key (recommended) or other LLM access
- Git (for cloning the repository)

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd strategic_simulation
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables:**
```bash
# Copy the environment template
cp .env.example .env

# Edit .env with your API keys
# OPENAI_API_KEY=your_openai_api_key_here
```

4. **Initialize the system:**
```bash
python main.py cli init
```

### Running Simulations

#### Web Interface (Recommended)
```bash
python main.py web
```
Then open your browser to `http://localhost:8501`

#### Command Line Interface
```bash
# Run an interactive simulation
python main.py cli run

# List available agents
python main.py cli agents

# Create a custom agent
python main.py cli create-agent

# View help
python main.py cli --help
```

## 🤖 Agent System

### Core Agents

#### 🇺🇸 Trump Agent
- **Role**: Primary decision maker
- **Characteristics**: America First perspective, business-oriented thinking
- **Based on**: 30 political figure cloning dimensions
- **Capabilities**: Strategic analysis, final decision making, negotiation-focused approach

#### 🎯 Moderator Agent
- **Role**: Simulation director and facilitator
- **Capabilities**: Discussion management, synthesis, after-action reviews
- **Functions**: Process control, objective analysis, comprehensive reporting

#### 👨‍💼 Expert Agents
- **Political Expert**: International relations, policy analysis, political feasibility
- **Military Expert**: Strategic planning, operational analysis, force assessment
- **Economic Expert**: Trade policy, economic impact, financial analysis

#### 👤 Custom Agents
- **User-defined**: Create agents with custom backgrounds and perspectives
- **Templates**: Business, academic, or general advisor templates
- **Interactive**: Support real-time user input during discussions

### Agent Capabilities

- **Strategic Analysis**: Comprehensive situation assessment
- **Knowledge Integration**: RAG-enhanced responses with domain expertise
- **Memory Management**: Persistent memory across sessions
- **Discussion Participation**: Structured contributions and responses
- **Decision Support**: Evidence-based recommendations

## 💬 Discussion System

### Discussion Phases

1. **Opening**: Moderator introduces scenario and participants
2. **Initial Positions**: Each agent states their initial perspective
3. **Open Discussion**: Multi-round structured discussion
4. **Decision Phase**: Trump agent makes final decision
5. **Debrief**: Moderator conducts after-action review

### Features

- **Turn Management**: Configurable turn orders and time limits
- **User Participation**: Real-time user input and guidance
- **Context Awareness**: Agents build on previous contributions
- **Flow Control**: Dynamic discussion management

## 🧠 Memory & Knowledge

### Memory System

- **Conversation Memory**: Short-term discussion context
- **Long-term Memory**: Persistent agent memories across sessions
- **Episodic Memory**: Specific event and decision records
- **Semantic Memory**: General knowledge and insights

### Knowledge Base (RAG)

- **Agent-specific**: Dedicated knowledge bases for each agent type
- **Vector Search**: Semantic similarity search with embeddings
- **Multiple Backends**: ChromaDB, FAISS, or simple text search
- **Dynamic Updates**: Real-time knowledge base updates

## 📊 Analysis & Reporting

### Decision Analysis

- **Quality Assessment**: Multi-criteria decision evaluation
- **Process Analysis**: Discussion effectiveness metrics
- **Participation Analysis**: Agent contribution patterns
- **Content Analysis**: Topic and theme identification

### Metrics

- **Decision Quality**: Strategic alignment, feasibility, risk assessment
- **Process Effectiveness**: Participation balance, information quality
- **Overall Scores**: Comprehensive simulation evaluation

## 🛠️ Configuration

### Agent Configuration (`config/agents_config.json`)

```json
{
  "agents": {
    "trump": {
      "name": "Donald Trump",
      "type": "political_leader",
      "role": "Former US President",
      "persona": {
        "personality": {
          "traits": ["confident", "decisive", "nationalist"],
          "communication_style": "direct, simple, America-first"
        }
      }
    }
  }
}
```

### Simulation Configuration (`config/simulation_config.json`)

```json
{
  "simulation_settings": {
    "max_discussion_rounds": 10,
    "default_round_time_limit": 300,
    "enable_user_participation": true
  },
  "discussion_flow": {
    "phases": [
      {"name": "opening", "duration": 60},
      {"name": "discussion", "duration": 600},
      {"name": "decision", "duration": 300}
    ]
  }
}
```

## 📝 Examples

### Basic Simulation

```python
from src.simulation.simulation_controller import SimulationController

# Initialize controller
controller = SimulationController("config/simulation_config.json")

# Load agents
controller.initialize_agents(agents_config, "config/knowledge_base")

# Define scenario
scenario = {
    "title": "Taiwan Strait Crisis",
    "description": "Strategic response to escalating tensions",
    "initial_context": "Intelligence reports increased military activity..."
}

# Run simulation
result = controller.run_simulation(scenario)
```

### Custom Agent Creation

```python
from src.agents.custom_agent import CustomAgent

# Create custom agent
custom_config = {
    "name": "Strategic Advisor",
    "role": "Senior Consultant",
    "persona": {
        "expertise": ["Strategic planning", "Risk assessment"],
        "values": ["National security", "Careful analysis"]
    }
}

agent = CustomAgent("Strategic Advisor", custom_config)
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Run all tests
python -m pytest tests/

# Run specific test
python tests/test_basic_functionality.py

# Run with coverage
python -m pytest tests/ --cov=src
```

## 🔧 Development

### Adding New Agents

1. Create agent class inheriting from `BaseStrategicAgent`
2. Implement required methods (`analyze_strategic_situation`, `contribute_to_discussion`)
3. Add agent configuration to `agents_config.json`
4. Register agent type in `SimulationController`

### Extending Knowledge Bases

1. Add knowledge files to `config/knowledge_base/`
2. Update agent configurations to reference knowledge files
3. Use RAG system for enhanced agent responses

### Custom Analysis

1. Extend `DecisionAnalyzer` with new metrics
2. Add analysis frameworks in `decision_analyzer.py`
3. Update reporting templates for new metrics

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork the repository** and create a feature branch
2. **Write tests** for new functionality
3. **Follow code style** guidelines (PEP 8 for Python)
4. **Update documentation** for new features
5. **Submit a pull request** with clear description

### Development Setup

```bash
# Clone your fork
git clone <your-fork-url>
cd strategic_simulation

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # If available

# Run tests
python -m pytest tests/
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **TinyTroupe Framework**: Foundation for multi-agent simulations
- **OpenAI**: GPT models for agent intelligence
- **Streamlit**: Web interface framework
- **ChromaDB**: Vector database for knowledge retrieval
- **mem0**: Advanced memory management capabilities

## 📞 Support

- **Documentation**: Check the `docs/` directory for detailed guides
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions in GitHub Discussions
- **Email**: Contact the development team at [email]

## 🗺️ Roadmap

### Version 1.1 (Planned)
- [ ] Enhanced web interface with real-time updates
- [ ] Voice input/output capabilities
- [ ] Advanced visualization of discussion flows
- [ ] Integration with more LLM providers

### Version 1.2 (Future)
- [ ] Multi-language support
- [ ] Advanced analytics dashboard
- [ ] API for external integrations
- [ ] Mobile-responsive interface

### Version 2.0 (Vision)
- [ ] VR/AR simulation environments
- [ ] Advanced AI reasoning capabilities
- [ ] Integration with real-world data sources
- [ ] Collaborative multi-organization simulations

## 📊 Performance

### System Requirements

- **Minimum**: 4GB RAM, 2 CPU cores, 10GB storage
- **Recommended**: 8GB RAM, 4 CPU cores, 20GB storage
- **Optimal**: 16GB RAM, 8 CPU cores, 50GB storage

### Scalability

- **Agents**: Supports up to 20 concurrent agents
- **Discussions**: Handles 100+ discussion rounds
- **Memory**: Manages millions of conversation records
- **Knowledge**: Indexes gigabytes of knowledge content

## 🔒 Security

### Data Protection

- **Local Storage**: All data stored locally by default
- **Encryption**: Optional encryption for sensitive data
- **API Keys**: Secure handling of external API credentials
- **Privacy**: No data sharing without explicit consent

### Best Practices

- Use environment variables for API keys
- Regularly backup simulation data
- Review agent configurations for sensitive information
- Monitor system logs for unusual activity

---

**Strategic Simulation System** - Empowering strategic decision-making through AI-powered simulations.

*Built with ❤️ for strategic planning and decision analysis.*
