#!/usr/bin/env python3
"""
智能体本地存储管理
"""

import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

class AgentStorage:
    """智能体本地存储管理器"""
    
    def __init__(self, storage_file: str = "custom_agents.json"):
        """
        初始化存储管理器
        
        Args:
            storage_file: 存储文件路径
        """
        self.storage_file = Path(storage_file)
        self.agents = {}
        self.load_agents()
    
    def load_agents(self):
        """从文件加载智能体数据"""
        try:
            if self.storage_file.exists():
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.agents = data.get('agents', {})
                    print(f"✅ 已加载 {len(self.agents)} 个自定义智能体")
            else:
                self.agents = {}
                print("📁 智能体存储文件不存在，将创建新文件")
        except Exception as e:
            print(f"❌ 加载智能体数据失败: {e}")
            self.agents = {}
    
    def save_agents(self):
        """保存智能体数据到文件"""
        try:
            data = {
                "agents": self.agents,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已保存 {len(self.agents)} 个智能体到 {self.storage_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存智能体数据失败: {e}")
            return False
    
    def add_agent(self, agent_id: str, agent_data: Dict) -> bool:
        """
        添加新智能体
        
        Args:
            agent_id: 智能体ID
            agent_data: 智能体数据
            
        Returns:
            是否添加成功
        """
        try:
            # 添加元数据
            agent_data['created_at'] = datetime.now().isoformat()
            agent_data['updated_at'] = datetime.now().isoformat()
            agent_data['agent_id'] = agent_id
            
            self.agents[agent_id] = agent_data
            return self.save_agents()
            
        except Exception as e:
            print(f"❌ 添加智能体失败: {e}")
            return False
    
    def update_agent(self, agent_id: str, agent_data: Dict) -> bool:
        """
        更新智能体
        
        Args:
            agent_id: 智能体ID
            agent_data: 新的智能体数据
            
        Returns:
            是否更新成功
        """
        try:
            if agent_id in self.agents:
                # 保留创建时间，更新修改时间
                agent_data['created_at'] = self.agents[agent_id].get('created_at', datetime.now().isoformat())
                agent_data['updated_at'] = datetime.now().isoformat()
                agent_data['agent_id'] = agent_id
                
                self.agents[agent_id] = agent_data
                return self.save_agents()
            else:
                print(f"❌ 智能体 {agent_id} 不存在")
                return False
                
        except Exception as e:
            print(f"❌ 更新智能体失败: {e}")
            return False
    
    def delete_agent(self, agent_id: str) -> bool:
        """
        删除智能体
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            是否删除成功
        """
        try:
            if agent_id in self.agents:
                del self.agents[agent_id]
                return self.save_agents()
            else:
                print(f"❌ 智能体 {agent_id} 不存在")
                return False
                
        except Exception as e:
            print(f"❌ 删除智能体失败: {e}")
            return False
    
    def get_agent(self, agent_id: str) -> Optional[Dict]:
        """
        获取智能体数据
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            智能体数据或None
        """
        return self.agents.get(agent_id, None)
    
    def get_all_agents(self) -> Dict:
        """获取所有智能体数据"""
        return self.agents.copy()
    
    def agent_exists(self, agent_id: str) -> bool:
        """检查智能体是否存在"""
        return agent_id in self.agents
    
    def get_agent_count(self) -> int:
        """获取智能体数量"""
        return len(self.agents)
    
    def search_agents(self, keyword: str) -> Dict:
        """
        搜索智能体
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的智能体字典
        """
        results = {}
        keyword_lower = keyword.lower()
        
        for agent_id, agent_data in self.agents.items():
            # 在名称、角色、专业领域中搜索
            searchable_text = f"{agent_data.get('name', '')} {agent_data.get('role', '')} {' '.join(agent_data.get('expertise', []))}"
            
            if keyword_lower in searchable_text.lower():
                results[agent_id] = agent_data
        
        return results
    
    def export_agents(self, export_file: str) -> bool:
        """
        导出智能体数据
        
        Args:
            export_file: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            export_data = {
                "exported_at": datetime.now().isoformat(),
                "agent_count": len(self.agents),
                "agents": self.agents
            }
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已导出 {len(self.agents)} 个智能体到 {export_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出智能体失败: {e}")
            return False
    
    def import_agents(self, import_file: str, overwrite: bool = False) -> int:
        """
        导入智能体数据
        
        Args:
            import_file: 导入文件路径
            overwrite: 是否覆盖现有智能体
            
        Returns:
            成功导入的智能体数量
        """
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            imported_agents = data.get('agents', {})
            imported_count = 0
            
            for agent_id, agent_data in imported_agents.items():
                if agent_id not in self.agents or overwrite:
                    self.agents[agent_id] = agent_data
                    imported_count += 1
            
            if imported_count > 0:
                self.save_agents()
            
            print(f"✅ 已导入 {imported_count} 个智能体")
            return imported_count
            
        except Exception as e:
            print(f"❌ 导入智能体失败: {e}")
            return 0
    
    def backup_agents(self, backup_dir: str = "backups") -> str:
        """
        备份智能体数据
        
        Args:
            backup_dir: 备份目录
            
        Returns:
            备份文件路径
        """
        try:
            # 创建备份目录
            backup_path = Path(backup_dir)
            backup_path.mkdir(exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_path / f"agents_backup_{timestamp}.json"
            
            # 执行备份
            if self.export_agents(str(backup_file)):
                return str(backup_file)
            else:
                return ""
                
        except Exception as e:
            print(f"❌ 备份智能体失败: {e}")
            return ""
    
    def get_storage_info(self) -> Dict:
        """获取存储信息"""
        try:
            file_size = self.storage_file.stat().st_size if self.storage_file.exists() else 0
            file_modified = datetime.fromtimestamp(self.storage_file.stat().st_mtime).isoformat() if self.storage_file.exists() else "未创建"
            
            return {
                "file_path": str(self.storage_file),
                "file_exists": self.storage_file.exists(),
                "file_size": file_size,
                "file_modified": file_modified,
                "agent_count": len(self.agents),
                "agents": list(self.agents.keys())
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "agent_count": len(self.agents)
            }

# 全局存储实例
agent_storage = AgentStorage()

def get_agent_storage() -> AgentStorage:
    """获取智能体存储实例"""
    return agent_storage

def load_custom_agents() -> Dict:
    """加载自定义智能体（兼容现有代码）"""
    return agent_storage.get_all_agents()

def save_custom_agent(agent_id: str, agent_data: Dict) -> bool:
    """保存自定义智能体（兼容现有代码）"""
    return agent_storage.add_agent(agent_id, agent_data)

def delete_custom_agent(agent_id: str) -> bool:
    """删除自定义智能体（兼容现有代码）"""
    return agent_storage.delete_agent(agent_id)
