#!/usr/bin/env python3
"""
自动轮流发言演示 - 简化版本
"""

import streamlit as st
import time
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 设置页面配置
st.set_page_config(
    page_title="自动轮流发言测试",
    page_icon="🔄",
    layout="wide"
)

# 导入AI功能
try:
    from utils.llm_config import generate_response
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

def generate_ai_response(agent_name: str, scenario: str) -> str:
    """生成AI回应"""
    
    if AI_AVAILABLE and generate_response:
        agent_prompts = {
            "特朗普": "你是唐纳德·特朗普。特点：直接、自信、强调美国优先。",
            "拜登": "你是乔·拜登。特点：温和、重视合作、经验丰富。",
            "军事专家": "你是军事专家。特点：专业、客观、注重实用性。",
            "政治专家": "你是政治专家。特点：深入分析、考虑多方面因素。"
        }
        
        system_prompt = agent_prompts.get(agent_name, f"你是{agent_name}。")
        
        messages = [
            {"role": "system", "content": system_prompt + "请用中文回应，保持角色特色，控制在100字以内。"},
            {"role": "user", "content": f"场景：{scenario}\n请分析并提出建议。"}
        ]
        
        try:
            response = generate_response(messages, max_tokens=150, temperature=0.8)
            return response
        except Exception as e:
            return f"我是{agent_name}，针对{scenario}，我认为需要综合考虑各方面因素。"
    else:
        responses = {
            "特朗普": "我们必须展现美国的实力！但是，我们不能让其他国家占我们便宜。",
            "拜登": "我们需要与盟友合作，通过外交手段解决问题，同时保持军事准备。",
            "军事专家": "从军事角度分析，我们需要评估风险和成本，建议采取渐进式策略。",
            "政治专家": "这涉及复杂的地缘政治因素，需要考虑国际法和长期战略影响。"
        }
        return responses.get(agent_name, f"我是{agent_name}，针对当前情况需要仔细分析。")

def stream_display(content: str, agent_name: str):
    """流式显示"""
    placeholder = st.empty()
    displayed_text = ""
    
    for char in content:
        displayed_text += char
        placeholder.markdown(f"{displayed_text}<span style='animation: blink 1s infinite; color: #666;'>|</span>", unsafe_allow_html=True)
        time.sleep(0.03)
    
    placeholder.markdown(displayed_text)
    return displayed_text

def main():
    """主函数"""
    
    st.title("🔄 自动轮流发言测试")
    
    # 添加CSS
    st.markdown("""
    <style>
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 初始化
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'agents' not in st.session_state:
        st.session_state.agents = ["特朗普", "拜登", "军事专家", "政治专家"]
    
    if 'current_agent_index' not in st.session_state:
        st.session_state.current_agent_index = 0
    
    if 'auto_mode' not in st.session_state:
        st.session_state.auto_mode = False
    
    if 'last_message_time' not in st.session_state:
        st.session_state.last_message_time = 0
    
    # 侧边栏设置
    with st.sidebar:
        st.header("🎮 设置")
        
        scenario = "台海危机应对策略"
        st.info(f"场景: {scenario}")
        
        enable_stream = st.checkbox("🌊 流式输出", value=True)
        auto_continue = st.checkbox("🔄 自动轮流发言", value=False)
        auto_interval = st.slider("自动间隔(秒)", 1, 10, 3)
        
        st.markdown("**参与智能体:**")
        for i, agent in enumerate(st.session_state.agents):
            if i == st.session_state.current_agent_index:
                st.write(f"👉 **{agent}** (当前)")
            else:
                st.write(f"• {agent}")
        
        if st.button("🎬 开始推演"):
            if not st.session_state.messages:
                # 导调开场
                opening_msg = {
                    "speaker": "推演导调",
                    "content": f"欢迎参加{scenario}推演。请各位专家依次发言分析。",
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(opening_msg)
                st.session_state.current_agent_index = 0
                st.rerun()
        
        if st.button("➡️ 手动下一位"):
            if st.session_state.agents:
                current_agent = st.session_state.agents[st.session_state.current_agent_index]
                
                # 生成回应
                if enable_stream:
                    with st.chat_message("assistant"):
                        response = generate_ai_response(current_agent, scenario)
                        displayed_response = stream_display(response, current_agent)
                else:
                    response = generate_ai_response(current_agent, scenario)
                    displayed_response = response
                
                # 添加到消息
                new_msg = {
                    "speaker": current_agent,
                    "content": displayed_response,
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(new_msg)
                
                # 移动到下一个智能体
                st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
                st.session_state.last_message_time = time.time()
                st.rerun()
        
        if st.button("🔄 重置"):
            st.session_state.messages = []
            st.session_state.current_agent_index = 0
            st.session_state.auto_mode = False
            st.rerun()
    
    # 自动轮流发言逻辑
    if auto_continue and st.session_state.messages:
        st.session_state.auto_mode = True
        current_time = time.time()
        
        # 检查是否到了自动发言时间
        if current_time - st.session_state.last_message_time >= auto_interval:
            current_agent = st.session_state.agents[st.session_state.current_agent_index]
            
            st.info(f"🔄 自动模式：{current_agent} 正在发言...")
            
            # 生成回应
            response = generate_ai_response(current_agent, scenario)
            
            # 显示回应
            with st.chat_message("assistant"):
                if enable_stream:
                    displayed_response = stream_display(response, current_agent)
                else:
                    st.write(f"**{current_agent}** - {datetime.now().strftime('%H:%M:%S')}")
                    st.write(response)
                    displayed_response = response
            
            # 添加到消息
            new_msg = {
                "speaker": current_agent,
                "content": displayed_response,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(new_msg)
            
            # 移动到下一个智能体
            st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
            st.session_state.last_message_time = current_time
            
            time.sleep(1)  # 短暂延迟
            st.rerun()
        
        # 显示倒计时
        remaining = auto_interval - (current_time - st.session_state.last_message_time)
        if remaining > 0:
            st.info(f"⏱️ 下一位发言倒计时: {remaining:.1f}秒")
            time.sleep(0.1)
            st.rerun()
    
    # 显示消息历史
    st.markdown("### 💬 推演记录")
    
    for msg in st.session_state.messages:
        with st.chat_message("assistant" if msg["speaker"] != "用户" else "user"):
            st.write(f"**{msg['speaker']}** - {msg['timestamp']}")
            st.write(msg['content'])
    
    # 状态显示
    if st.session_state.auto_mode:
        st.success("🔄 自动轮流发言模式已启用")
    else:
        st.info("⏸️ 手动模式")

if __name__ == "__main__":
    main()
