#!/usr/bin/env python3
"""
自动轮流发言测试 - AI自动发言，用户智能体暂停
"""

import streamlit as st
import time
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

st.set_page_config(page_title="自动轮流发言测试", page_icon="🔄", layout="wide")

# 导入AI功能
try:
    from utils.llm_config import generate_response
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

def generate_ai_response(agent_name: str, scenario: str, context_messages: list = None) -> str:
    """生成AI回应 - 智囊团辩论模式"""

    if AI_AVAILABLE and generate_response:
        # 更详细的智能体人格和立场设定
        agent_profiles = {
            "特朗普": {
                "role": "美国前总统，决策者",
                "style": "直接、强硬、商人思维、美国优先",
                "stance": "倾向于强硬立场，重视实力展示，但也考虑成本效益",
                "interaction": "会直接回应其他人的观点，支持或反驳，做出明确表态"
            },
            "拜登": {
                "role": "美国现任总统，决策者",
                "style": "温和、外交导向、重视盟友合作",
                "stance": "倾向于多边合作，通过外交手段解决，但保持军事威慑",
                "interaction": "会寻求平衡各方观点，提出折中方案，强调团结"
            },
            "军事专家": {
                "role": "军事战略专家，技术顾问",
                "style": "专业、客观、数据导向、风险评估",
                "stance": "从军事可行性角度分析，关注战略风险和作战能力",
                "interaction": "会对其他人的军事判断进行专业评估，提供技术性建议"
            },
            "政治专家": {
                "role": "政治学专家，战略顾问",
                "style": "深度分析、长远视角、多维思考",
                "stance": "从地缘政治和国际关系角度分析长期影响",
                "interaction": "会分析其他观点的政治后果，提出战略层面的考量"
            }
        }

        profile = agent_profiles.get(agent_name, {
            "role": f"{agent_name}",
            "style": "客观分析",
            "stance": "中立立场",
            "interaction": "参与讨论"
        })

        # 构建智囊团辩论提示
        system_prompt = f"""你是{profile['role']}，参与台海危机应对策略的智囊团讨论。

你的特点：{profile['style']}
你的立场：{profile['stance']}
互动方式：{profile['interaction']}

这是一个智囊团辩论，你需要：
1. 仔细分析其他参与者的观点
2. 明确表达赞同或反对，并说明理由
3. 提出自己的独特见解和建议
4. 与其他观点进行建设性的辩论
5. 推动讨论向更深层次发展

请用中文回应，保持角色特色，积极参与辩论，控制在120字以内。"""

        # 构建包含完整上下文的讨论内容
        if context_messages and len(context_messages) > 0:
            # 分析最近的发言，找出关键观点
            recent_messages = context_messages[-3:]  # 最近3条消息
            discussion_summary = "当前讨论焦点：\n"

            for msg in recent_messages:
                discussion_summary += f"• {msg['speaker']}: {msg['content']}\n"

            user_content = f"""场景：{scenario}

{discussion_summary}

作为{agent_name}，请：
1. 回应上述观点（赞同/反对/补充）
2. 提出你的独特见解
3. 推动讨论深入

请直接参与辩论，不要只是重复自己的立场。"""
        else:
            user_content = f"""场景：{scenario}

作为智囊团的{agent_name}，请分析当前形势并提出你的战略建议。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_content}
        ]
        
        try:
            response = generate_response(messages, max_tokens=120, temperature=0.8)
            return response
        except Exception as e:
            return f"我是{agent_name}，针对{scenario}，我认为需要综合考虑各方面因素。"
    else:
        responses = {
            "特朗普": "我们必须展现美国的实力！但是，我们不能让其他国家占我们便宜。",
            "拜登": "我们需要与盟友合作，通过外交手段解决问题，同时保持军事准备。",
            "军事专家": "从军事角度分析，我们需要评估风险和成本，建议采取渐进式策略。",
            "政治专家": "这涉及复杂的地缘政治因素，需要考虑国际法和长期战略影响。"
        }
        return responses.get(agent_name, f"我是{agent_name}，针对当前情况需要仔细分析。")

def stream_display(content: str, agent_name: str):
    """流式显示"""
    placeholder = st.empty()
    displayed_text = ""
    
    for char in content:
        displayed_text += char
        placeholder.markdown(f"{displayed_text}<span style='animation: blink 1s infinite; color: #666;'>|</span>", unsafe_allow_html=True)
        time.sleep(0.03)
    
    placeholder.markdown(displayed_text)
    return displayed_text

def main():
    """主函数"""
    
    st.title("🔄 自动轮流发言测试")
    st.markdown("**AI智能体自动发言，用户智能体暂停等待输入**")
    
    # 添加CSS
    st.markdown("""
    <style>
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 初始化
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'agents' not in st.session_state:
        st.session_state.agents = ["特朗普", "军事专家", "张三", "政治专家", "李四"]  # 混合AI和用户智能体
    
    if 'current_agent_index' not in st.session_state:
        st.session_state.current_agent_index = 0
    
    if 'auto_mode' not in st.session_state:
        st.session_state.auto_mode = False
    
    if 'last_auto_time' not in st.session_state:
        st.session_state.last_auto_time = 0
    
    if 'user_agents' not in st.session_state:
        st.session_state.user_agents = ["张三", "李四"]  # 用户控制的智能体

    if 'total_rounds' not in st.session_state:
        st.session_state.total_rounds = 0  # 总发言轮数

    if 'max_rounds' not in st.session_state:
        st.session_state.max_rounds = 15  # 最大发言轮数
    
    # 侧边栏设置
    with st.sidebar:
        st.header("🎮 设置")
        
        scenario = "台海危机应对策略"
        st.info(f"场景: {scenario}")
        
        enable_stream = st.checkbox("🌊 流式输出", value=True)
        auto_continue = st.checkbox("🔄 自动轮流发言", value=False)
        auto_interval = st.slider("AI自动间隔(秒)", 1, 10, 3)

        # 轮数设置
        st.session_state.max_rounds = st.slider("最大讨论轮数", 5, 30, st.session_state.max_rounds)

        # 进度显示
        if st.session_state.total_rounds > 0:
            progress = min(st.session_state.total_rounds / st.session_state.max_rounds, 1.0)
            st.progress(progress, text=f"讨论进度: {st.session_state.total_rounds}/{st.session_state.max_rounds}")

            if st.session_state.total_rounds >= st.session_state.max_rounds:
                st.success("🎉 讨论已完成！")
        
        st.markdown("**参与智能体:**")
        for i, agent in enumerate(st.session_state.agents):
            is_user_agent = agent in st.session_state.user_agents
            agent_type = "👤 用户" if is_user_agent else "🤖 AI"
            
            if i == st.session_state.current_agent_index:
                st.write(f"👉 **{agent}** {agent_type} (当前)")
            else:
                st.write(f"• {agent} {agent_type}")
        
        if st.button("🎬 开始推演"):
            if not st.session_state.messages:
                # 导调开场
                opening_msg = {
                    "speaker": "推演导调",
                    "content": f"欢迎参加{scenario}推演。请各位专家和参与者依次发言分析。",
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(opening_msg)
                st.session_state.current_agent_index = 0
                st.session_state.auto_mode = auto_continue
                st.rerun()
        
        if st.button("➡️ 手动下一位"):
            handle_next_speaker(scenario, enable_stream)
        
        if st.button("🔄 重置"):
            st.session_state.messages = []
            st.session_state.current_agent_index = 0
            st.session_state.auto_mode = False
            st.session_state.last_auto_time = 0
            st.session_state.total_rounds = 0
            st.rerun()
    
    # 自动轮流发言逻辑
    if auto_continue and st.session_state.messages and st.session_state.total_rounds < st.session_state.max_rounds:
        st.session_state.auto_mode = True
        current_time = time.time()

        if st.session_state.agents:
            current_agent = st.session_state.agents[st.session_state.current_agent_index]
            is_user_agent = current_agent in st.session_state.user_agents

            if is_user_agent:
                # 用户智能体，暂停自动模式
                st.warning(f"⏸️ 轮到用户智能体 **{current_agent}**，自动模式暂停")
                st.info("请在下方输入发言内容，然后点击发送继续")
            else:
                # AI智能体，检查是否到了自动发言时间
                if current_time - st.session_state.last_auto_time >= auto_interval:
                    st.info(f"🤖 AI智能体 **{current_agent}** 正在自动发言...")

                    # 自动发言
                    handle_next_speaker(scenario, enable_stream)
                    st.session_state.last_auto_time = current_time
                    st.rerun()
                else:
                    # 显示倒计时
                    remaining = auto_interval - (current_time - st.session_state.last_auto_time)
                    st.info(f"⏱️ AI智能体 **{current_agent}** 将在 {remaining:.1f}秒后自动发言")
                    time.sleep(0.5)
                    st.rerun()
    elif auto_continue and st.session_state.total_rounds >= st.session_state.max_rounds:
        st.success("🎉 讨论已达到设定轮数，自动模式结束")
    
    # 显示消息历史
    st.markdown("### 💬 推演记录")
    
    for msg in st.session_state.messages:
        is_user_msg = msg["speaker"] in st.session_state.user_agents
        with st.chat_message("user" if is_user_msg else "assistant"):
            st.write(f"**{msg['speaker']}** - {msg['timestamp']}")
            st.write(msg['content'])
    
    # 用户输入区域 - 始终显示，但根据当前发言者调整
    st.markdown("---")

    if st.session_state.agents:
        current_agent = st.session_state.agents[st.session_state.current_agent_index]
        is_user_agent = current_agent in st.session_state.user_agents

        if is_user_agent:
            # 用户智能体输入区域
            st.markdown(f"### 💬 {current_agent} 的发言")
            st.info(f"轮到用户智能体 **{current_agent}**，请输入发言内容")

            user_input = st.text_area(
                f"请以 {current_agent} 的身份发言:",
                height=120,
                key=f"user_input_{current_agent}_{st.session_state.current_agent_index}",
                placeholder=f"请输入 {current_agent} 对台海危机的看法和建议..."
            )

            col1, col2 = st.columns(2)
            with col1:
                if st.button("📤 发送", type="primary", use_container_width=True):
                    if user_input and user_input.strip():
                        # 添加用户发言
                        user_msg = {
                            "speaker": current_agent,
                            "content": user_input.strip(),
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        }
                        st.session_state.messages.append(user_msg)

                        # 增加总轮数
                        st.session_state.total_rounds += 1

                        # 移动到下一个智能体
                        st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
                        st.session_state.last_auto_time = time.time()

                        # 清空输入框（通过重新生成key）
                        st.success(f"✅ {current_agent} 发言已提交，自动轮流继续")
                        time.sleep(0.5)  # 短暂延迟
                        st.rerun()
                    else:
                        st.warning("⚠️ 请输入发言内容")

            with col2:
                if st.button("🤖 AI代发", use_container_width=True):
                    # AI代为发言
                    with st.spinner(f"AI正在为 {current_agent} 生成发言..."):
                        response = generate_ai_response(current_agent, scenario, st.session_state.messages)

                    user_msg = {
                        "speaker": current_agent,
                        "content": f"[AI代发] {response}",
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    }
                    st.session_state.messages.append(user_msg)

                    # 增加总轮数
                    st.session_state.total_rounds += 1

                    # 移动到下一个智能体
                    st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
                    st.session_state.last_auto_time = time.time()
                    st.success(f"✅ AI已为 {current_agent} 代发，自动轮流继续")
                    time.sleep(0.5)
                    st.rerun()
        else:
            # AI智能体，显示状态
            st.markdown(f"### 🤖 AI智能体状态")
            if auto_continue:
                st.info(f"AI智能体 **{current_agent}** 将自动发言，无需手动输入")
            else:
                st.info(f"当前轮到 **{current_agent}**，点击'手动下一位'让其发言")

    # 通用用户输入（用于自由发言）
    st.markdown("### 💬 自由发言")
    general_input = st.text_area("作为观察者发言:", height=80, placeholder="您可以随时发表观点...")

    if st.button("📤 发送观点", type="secondary"):
        if general_input and general_input.strip():
            observer_msg = {
                "speaker": "观察者",
                "content": general_input.strip(),
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(observer_msg)
            st.success("✅ 观察者发言已添加")
            st.rerun()
        else:
            st.warning("⚠️ 请输入发言内容")

def handle_next_speaker(scenario, enable_stream):
    """处理下一位发言者"""
    if st.session_state.agents:
        current_agent = st.session_state.agents[st.session_state.current_agent_index]
        is_user_agent = current_agent in st.session_state.user_agents

        if not is_user_agent:
            # AI智能体自动发言，传递上下文
            response = generate_ai_response(current_agent, scenario, st.session_state.messages)

            # 显示回应
            with st.chat_message("assistant"):
                if enable_stream:
                    displayed_response = stream_display(response, current_agent)
                else:
                    st.write(f"**{current_agent}** - {datetime.now().strftime('%H:%M:%S')}")
                    st.write(response)
                    displayed_response = response

            # 添加到消息
            new_msg = {
                "speaker": current_agent,
                "content": displayed_response,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(new_msg)

            # 增加总轮数
            st.session_state.total_rounds += 1

            # 移动到下一个智能体
            st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)

if __name__ == "__main__":
    main()
