#!/usr/bin/env python3
"""
微信风格的聊天室推演界面
"""

import streamlit as st
import sys
import time
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 设置页面配置
st.set_page_config(
    page_title="战略推演聊天室",
    page_icon="💬",
    layout="wide"
)

# 导入AI功能
try:
    from utils.llm_config import llm_config, generate_response
    from ui.chat_interface import (
        render_chat_message, stream_message_display, 
        render_user_input_area, render_chat_controls
    )
    AI_AVAILABLE = True
except ImportError as e:
    print(f"AI模块导入失败: {e}")
    AI_AVAILABLE = False


def initialize_demo_data():
    """初始化演示数据"""
    
    # 演示智能体
    demo_agents = {
        "trump": {
            "name": "唐纳德·特朗普",
            "role": "美国前总统",
            "type": "political_leader"
        },
        "biden": {
            "name": "乔·拜登",
            "role": "美国现任总统", 
            "type": "political_leader"
        },
        "military_expert": {
            "name": "军事专家",
            "role": "军事战略专家",
            "type": "domain_expert"
        },
        "political_expert": {
            "name": "政治专家",
            "role": "政治学专家",
            "type": "domain_expert"
        },
        "moderator": {
            "name": "推演导调",
            "role": "推演主持人",
            "type": "simulation_controller"
        }
    }
    
    # 演示场景
    demo_scenario = {
        "title": "中东军事干预决策",
        "description": "是否对地区冲突进行军事干预的决策",
        "context": "盟友国家请求美国在地区冲突中提供军事支持，美国应如何回应？"
    }
    
    return demo_agents, demo_scenario


def generate_ai_response(agent_id: str, agent_info: dict, scenario: dict, context: list):
    """生成AI回应"""
    
    if not AI_AVAILABLE or not generate_response:
        return f"[演示模式] 我是{agent_info['name']}，针对{scenario['title']}，我认为需要谨慎分析当前形势。"
    
    # 构建智能体人格提示
    agent_prompts = {
        "trump": "你是唐纳德·特朗普，美国前总统。特点：直接、自信、强调美国优先、商人思维。",
        "biden": "你是乔·拜登，美国现任总统。特点：强调团结、重视盟友合作、温和但坚定。",
        "military_expert": "你是军事战略专家。特点：专业军事分析、关注战略风险、实用主义建议。",
        "political_expert": "你是政治学专家。特点：深入政治分析、考虑多方利益、学术性表达。",
        "moderator": "你是推演主持人。特点：保持客观中立、引导讨论、总结关键观点。"
    }
    
    system_prompt = agent_prompts.get(agent_id, f"你是{agent_info['name']}，{agent_info['role']}。")
    
    # 构建消息
    messages = [
        {"role": "system", "content": system_prompt + "请用中文回应，保持角色特色，回应长度控制在100-200字。"},
        {"role": "user", "content": f"""
场景：{scenario['title']}
背景：{scenario['context']}

之前的讨论：
{chr(10).join([f"{msg['speaker']}: {msg['content']}" for msg in context[-3:]])}

请基于当前讨论，从你的角度分析情况并提出建议。
"""}
    ]
    
    try:
        response = generate_response(messages, max_tokens=300, temperature=0.8)
        return response
    except Exception as e:
        return f"AI生成失败: {str(e)}"


def main():
    """主函数"""
    
    st.title("💬 战略推演聊天室")
    
    # 初始化数据
    demo_agents, demo_scenario = initialize_demo_data()
    
    # 初始化会话状态
    if 'chat_messages' not in st.session_state:
        st.session_state.chat_messages = []
    
    if 'selected_agents' not in st.session_state:
        st.session_state.selected_agents = ["trump", "military_expert", "political_expert"]
    
    if 'user_agents' not in st.session_state:
        st.session_state.user_agents = ["张三", "李四"]
    
    if 'current_round' not in st.session_state:
        st.session_state.current_round = 0
    
    if 'show_thinking' not in st.session_state:
        st.session_state.show_thinking = False
    
    if 'enable_stream' not in st.session_state:
        st.session_state.enable_stream = True
    
    if 'auto_mode' not in st.session_state:
        st.session_state.auto_mode = False
    
    # 侧边栏配置
    with st.sidebar:
        st.header("🎮 聊天室设置")
        
        # 场景信息
        st.subheader("📋 当前场景")
        st.info(f"**{demo_scenario['title']}**\n\n{demo_scenario['description']}")
        
        # 参与者选择
        st.subheader("👥 参与者")
        
        # AI智能体
        st.write("**AI智能体:**")
        for agent_id, agent in demo_agents.items():
            selected = st.checkbox(
                f"{agent['name']} ({agent['role']})",
                value=agent_id in st.session_state.selected_agents,
                key=f"select_{agent_id}"
            )
            
            if selected and agent_id not in st.session_state.selected_agents:
                st.session_state.selected_agents.append(agent_id)
            elif not selected and agent_id in st.session_state.selected_agents:
                st.session_state.selected_agents.remove(agent_id)
        
        # 用户智能体
        st.write("**用户智能体:**")
        for user_agent in st.session_state.user_agents:
            agent_id = f"user_{user_agent.replace(' ', '_')}"
            selected = st.checkbox(
                f"👤 {user_agent} (用户控制)",
                value=agent_id in st.session_state.selected_agents,
                key=f"select_{agent_id}"
            )
            
            if selected and agent_id not in st.session_state.selected_agents:
                st.session_state.selected_agents.append(agent_id)
            elif not selected and agent_id in st.session_state.selected_agents:
                st.session_state.selected_agents.remove(agent_id)
        
        # 添加新用户智能体
        new_user = st.text_input("添加用户智能体:", placeholder="例如: 王五")
        if st.button("➕ 添加"):
            if new_user and new_user not in st.session_state.user_agents:
                st.session_state.user_agents.append(new_user)
                st.rerun()
        
        # 聊天设置
        st.subheader("⚙️ 聊天设置")
        st.session_state.show_thinking = st.checkbox("🧠 显示思考过程", value=st.session_state.show_thinking)
        st.session_state.enable_stream = st.checkbox("🌊 流式输出", value=st.session_state.enable_stream)
        st.session_state.auto_mode = st.checkbox("🔄 自动模式", value=st.session_state.auto_mode)
        
        # 控制按钮
        st.subheader("🎮 控制面板")
        
        if st.button("🎬 开始推演"):
            if not st.session_state.chat_messages:
                # 导调开场
                moderator_msg = {
                    "speaker": "moderator",
                    "content": f"欢迎参加{demo_scenario['title']}推演。请各位基于{demo_scenario['context']}展开讨论。",
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.chat_messages.append(moderator_msg)
                st.rerun()
        
        if st.button("➡️ 下一轮"):
            if st.session_state.selected_agents:
                next_speaker = st.session_state.selected_agents[st.session_state.current_round % len(st.session_state.selected_agents)]
                
                if next_speaker.startswith("user_"):
                    st.info(f"轮到用户智能体: {next_speaker}")
                else:
                    # AI自动发言
                    if st.session_state.enable_stream:
                        with st.spinner("AI正在生成回应..."):
                            response = generate_ai_response(
                                next_speaker,
                                demo_agents[next_speaker],
                                demo_scenario,
                                st.session_state.chat_messages
                            )
                        
                        # 添加到消息列表
                        ai_msg = {
                            "speaker": next_speaker,
                            "content": response,
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        }
                        st.session_state.chat_messages.append(ai_msg)
                    
                    st.session_state.current_round += 1
                    st.rerun()
        
        if st.button("🔄 重置"):
            st.session_state.chat_messages = []
            st.session_state.current_round = 0
            st.rerun()
    
    # 主聊天区域
    st.markdown("### 💬 讨论区")
    
    # 聊天消息容器
    chat_container = st.container()
    
    with chat_container:
        # 显示所有消息
        for message in st.session_state.chat_messages:
            # 合并所有智能体信息
            all_agents = {**demo_agents}
            for user_agent in st.session_state.user_agents:
                agent_id = f"user_{user_agent.replace(' ', '_')}"
                all_agents[agent_id] = {
                    "name": user_agent,
                    "role": f"用户控制 - {user_agent}",
                    "type": "user_controlled"
                }
            
            render_chat_message(message, all_agents)
    
    # 用户输入区域
    st.markdown("---")
    
    # 检查是否轮到用户智能体
    current_speaker = None
    if st.session_state.selected_agents:
        current_speaker = st.session_state.selected_agents[st.session_state.current_round % len(st.session_state.selected_agents)]
    
    if current_speaker and current_speaker.startswith("user_"):
        # 特定用户智能体输入
        user_input, send_btn, ai_assist_btn = render_user_input_area(st.session_state.selected_agents, current_speaker)
        
        if send_btn and user_input:
            # 用户发言
            user_msg = {
                "speaker": current_speaker,
                "content": user_input,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.chat_messages.append(user_msg)
            st.session_state.current_round += 1
            st.rerun()
        
        if ai_assist_btn:
            # AI代为发言
            agent_name = current_speaker.replace("user_", "").replace("_", " ")
            temp_agent = {"name": agent_name, "role": f"用户智能体 - {agent_name}"}
            
            response = generate_ai_response(current_speaker, temp_agent, demo_scenario, st.session_state.chat_messages)
            
            ai_msg = {
                "speaker": current_speaker,
                "content": response,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.chat_messages.append(ai_msg)
            st.session_state.current_round += 1
            st.rerun()
    
    else:
        # 普通用户输入
        user_input, send_btn, _ = render_user_input_area(st.session_state.selected_agents)
        
        if send_btn and user_input:
            # 用户发言
            user_msg = {
                "speaker": "user",
                "content": user_input,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.chat_messages.append(user_msg)
            st.rerun()


if __name__ == "__main__":
    main()
