#!/usr/bin/env python3
"""
完整推演流程演示：讨论 → 总结 → 特朗普决策
"""

import streamlit as st
import time
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

st.set_page_config(page_title="完整推演流程", page_icon="⚖️", layout="wide")

# 导入AI功能
try:
    from utils.llm_config import generate_response
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

def generate_ai_response(agent_name: str, scenario: str, prompt: str = None) -> str:
    """生成AI回应"""
    
    if AI_AVAILABLE and generate_response:
        agent_prompts = {
            "特朗普": "你是唐纳德·特朗普。特点：直接、自信、强调美国优先、商人思维。",
            "拜登": "你是乔·拜登。特点：温和、重视合作、经验丰富、外交导向。",
            "军事专家": "你是军事专家。特点：专业、客观、注重实用性、风险评估。",
            "政治专家": "你是政治专家。特点：深入分析、考虑多方面因素、长期视角。",
            "推演导调": "你是推演主持人。特点：中立、客观、引导讨论、总结要点。"
        }
        
        system_prompt = agent_prompts.get(agent_name, f"你是{agent_name}。")
        
        if prompt:
            user_content = prompt
        else:
            user_content = f"场景：{scenario}\n请分析并提出建议。"
        
        messages = [
            {"role": "system", "content": system_prompt + "请用中文回应，保持角色特色。"},
            {"role": "user", "content": user_content}
        ]
        
        try:
            response = generate_response(messages, max_tokens=300, temperature=0.8)
            return response
        except Exception as e:
            return f"我是{agent_name}，针对{scenario}，我认为需要综合考虑各方面因素。"
    else:
        return f"[演示] 我是{agent_name}，针对{scenario}，我认为需要综合考虑各方面因素。"

def stream_display(content: str, agent_name: str):
    """流式显示"""
    placeholder = st.empty()
    displayed_text = ""
    
    for char in content:
        displayed_text += char
        placeholder.markdown(f"{displayed_text}<span style='animation: blink 1s infinite; color: #666;'>|</span>", unsafe_allow_html=True)
        time.sleep(0.03)
    
    placeholder.markdown(displayed_text)
    return displayed_text

def main():
    """主函数"""
    
    st.title("⚖️ 完整推演流程：讨论 → 总结 → 特朗普决策")
    
    # 添加CSS
    st.markdown("""
    <style>
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 初始化
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'phase' not in st.session_state:
        st.session_state.phase = "discussion"  # discussion, summary, decision, completed
    
    if 'agents' not in st.session_state:
        st.session_state.agents = ["特朗普", "拜登", "军事专家", "政治专家"]
    
    if 'current_agent_index' not in st.session_state:
        st.session_state.current_agent_index = 0
    
    if 'discussion_rounds' not in st.session_state:
        st.session_state.discussion_rounds = 0
    
    if 'max_rounds' not in st.session_state:
        st.session_state.max_rounds = 6  # 每个智能体发言1-2轮
    
    # 侧边栏设置
    with st.sidebar:
        st.header("🎮 推演控制")
        
        scenario = "台海危机应对策略"
        st.info(f"场景: {scenario}")
        
        # 阶段显示
        phase_names = {
            "discussion": "🗣️ 讨论阶段",
            "summary": "📋 总结阶段",
            "decision": "⚖️ 决策阶段",
            "completed": "✅ 推演完成"
        }
        current_phase = phase_names.get(st.session_state.phase, "🗣️ 讨论阶段")
        st.success(f"当前阶段: {current_phase}")
        
        # 进度显示
        if st.session_state.phase == "discussion":
            progress = min(st.session_state.discussion_rounds / st.session_state.max_rounds, 1.0)
            st.progress(progress, text=f"讨论进度: {st.session_state.discussion_rounds}/{st.session_state.max_rounds}")
        
        enable_stream = st.checkbox("🌊 流式输出", value=True)
        
        # 控制按钮
        if st.session_state.phase == "discussion":
            if st.button("🎬 开始推演", disabled=len(st.session_state.messages) > 0):
                # 导调开场
                opening_msg = {
                    "speaker": "推演导调",
                    "content": generate_ai_response("推演导调", scenario, f"作为推演主持人，请介绍{scenario}推演，说明讨论重点。"),
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(opening_msg)
                st.rerun()
            
            if st.button("➡️ 下一位发言", disabled=len(st.session_state.messages) == 0):
                if st.session_state.discussion_rounds >= st.session_state.max_rounds:
                    st.session_state.phase = "summary"
                    st.rerun()
                else:
                    current_agent = st.session_state.agents[st.session_state.current_agent_index]
                    
                    # 生成回应
                    response = generate_ai_response(current_agent, scenario)
                    
                    # 显示回应
                    with st.chat_message("assistant"):
                        if enable_stream:
                            displayed_response = stream_display(response, current_agent)
                        else:
                            st.write(f"**{current_agent}** - {datetime.now().strftime('%H:%M:%S')}")
                            st.write(response)
                            displayed_response = response
                    
                    # 添加到消息
                    new_msg = {
                        "speaker": current_agent,
                        "content": displayed_response,
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    }
                    st.session_state.messages.append(new_msg)
                    
                    # 更新状态
                    st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
                    st.session_state.discussion_rounds += 1
                    st.rerun()
        
        elif st.session_state.phase == "summary":
            if st.button("📋 生成总结"):
                # 生成讨论总结
                discussion_content = "\n\n".join([f"{msg['speaker']}: {msg['content']}" for msg in st.session_state.messages])
                summary_prompt = f"请总结以下台海危机讨论，为特朗普决策提供参考：\n\n{discussion_content}"
                
                summary = generate_ai_response("推演导调", scenario, summary_prompt)
                
                with st.chat_message("assistant"):
                    if enable_stream:
                        displayed_summary = stream_display(summary, "推演导调")
                    else:
                        st.write(f"**推演导调** - {datetime.now().strftime('%H:%M:%S')}")
                        st.write(summary)
                        displayed_summary = summary
                
                summary_msg = {
                    "speaker": "推演导调",
                    "content": f"📋 **讨论总结**\n\n{displayed_summary}",
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(summary_msg)
                st.session_state.phase = "decision"
                st.rerun()
        
        elif st.session_state.phase == "decision":
            if st.button("⚖️ 特朗普最终决策"):
                # 生成特朗普的最终决策
                all_content = "\n\n".join([f"{msg['speaker']}: {msg['content']}" for msg in st.session_state.messages])
                decision_prompt = f"基于以下完整讨论，作为特朗普做出台海危机的最终决策：\n\n{all_content}"
                
                decision = generate_ai_response("特朗普", scenario, decision_prompt)
                
                with st.chat_message("assistant"):
                    if enable_stream:
                        displayed_decision = stream_display(decision, "特朗普")
                    else:
                        st.write(f"**特朗普** - {datetime.now().strftime('%H:%M:%S')}")
                        st.write(decision)
                        displayed_decision = decision
                
                decision_msg = {
                    "speaker": "特朗普",
                    "content": f"⚖️ **最终决策**\n\n{displayed_decision}",
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(decision_msg)
                st.session_state.phase = "completed"
                st.success("🎉 推演完成！特朗普的最终决策已生成。")
                st.rerun()
        
        if st.button("🔄 重新开始"):
            st.session_state.messages = []
            st.session_state.phase = "discussion"
            st.session_state.current_agent_index = 0
            st.session_state.discussion_rounds = 0
            st.rerun()
    
    # 显示消息历史
    st.markdown("### 💬 推演记录")
    
    for msg in st.session_state.messages:
        with st.chat_message("assistant" if msg["speaker"] != "用户" else "user"):
            st.write(f"**{msg['speaker']}** - {msg['timestamp']}")
            st.write(msg['content'])
    
    # 状态提示
    if st.session_state.phase == "completed":
        st.balloons()
        st.success("🎉 推演已完成！特朗普已做出最终决策。")

if __name__ == "__main__":
    main()
