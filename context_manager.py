#!/usr/bin/env python3
"""
智能上下文管理系统
解决长对话中的上下文长度限制问题
"""

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False

import json
from typing import List, Dict, Any, Tu<PERSON>
from datetime import datetime

class ContextManager:
    """智能上下文管理器"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo", max_tokens: int = 4000):
        """
        初始化上下文管理器
        
        Args:
            model_name: 模型名称，用于token计算
            max_tokens: 最大token限制
        """
        self.model_name = model_name
        self.max_tokens = max_tokens
        self.max_context_tokens = int(max_tokens * 0.7)  # 为回复预留30%空间
        
        if TIKTOKEN_AVAILABLE:
            try:
                self.encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")  # 使用通用编码
            except:
                try:
                    self.encoding = tiktoken.get_encoding("cl100k_base")  # 备用编码
                except:
                    self.encoding = None
        else:
            self.encoding = None
    
    def count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        if self.encoding is not None:
            try:
                return len(self.encoding.encode(text))
            except:
                pass

        # 备用方案：改进的token估算
        if not text:
            return 0

        # 中文字符通常占用更多token
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        english_chars = len(text) - chinese_chars

        # 中文字符约1.5 token，英文字符约0.25 token
        estimated_tokens = int(chinese_chars * 1.5 + english_chars * 0.25)

        # 最小值保护
        return max(estimated_tokens, len(text) // 6)
    
    def count_messages_tokens(self, messages: List[Dict]) -> int:
        """计算消息列表的总token数"""
        total_tokens = 0
        for message in messages:
            # 计算角色和内容的token
            total_tokens += self.count_tokens(message.get("role", ""))
            total_tokens += self.count_tokens(message.get("content", ""))
            total_tokens += 4  # 每条消息的格式开销
        return total_tokens
    
    def summarize_messages(self, messages: List[Dict], target_length: int = 500) -> str:
        """
        将消息列表压缩为摘要
        
        Args:
            messages: 消息列表
            target_length: 目标摘要长度（字符数）
            
        Returns:
            压缩后的摘要文本
        """
        if not messages:
            return ""
        
        # 按发言者分组
        speaker_points = {}
        for msg in messages:
            speaker = msg.get("speaker", "未知")
            content = msg.get("content", "")
            
            if speaker not in speaker_points:
                speaker_points[speaker] = []
            speaker_points[speaker].append(content)
        
        # 生成摘要
        summary_parts = []
        for speaker, points in speaker_points.items():
            # 合并该发言者的所有观点
            combined_content = " ".join(points)
            
            # 提取关键观点（简单版本）
            key_points = self._extract_key_points(combined_content)
            if key_points:
                summary_parts.append(f"{speaker}: {key_points}")
        
        summary = "【历史讨论摘要】\n" + "\n".join(summary_parts)
        
        # 如果摘要仍然太长，进一步压缩
        if len(summary) > target_length:
            summary = summary[:target_length] + "..."
        
        return summary
    
    def _extract_key_points(self, text: str, max_points: int = 3) -> str:
        """提取文本的关键观点（简化版本）"""
        if not text:
            return ""
        
        # 简单的关键词提取
        sentences = text.split('。')
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        # 选择最重要的句子（这里简化为前几句）
        key_sentences = sentences[:max_points]
        
        return "；".join(key_sentences)
    
    def manage_context(self, 
                      system_prompt: str,
                      conversation_history: List[Dict],
                      current_prompt: str) -> Tuple[List[Dict], Dict]:
        """
        智能管理上下文，确保不超过token限制
        
        Args:
            system_prompt: 系统提示
            conversation_history: 对话历史
            current_prompt: 当前用户提示
            
        Returns:
            (优化后的消息列表, 管理统计信息)
        """
        
        # 构建基础消息
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": current_prompt}
        ]
        
        base_tokens = self.count_messages_tokens(messages)
        available_tokens = self.max_context_tokens - base_tokens
        
        stats = {
            "total_history_messages": len(conversation_history),
            "base_tokens": base_tokens,
            "available_tokens": available_tokens,
            "strategy": "none"
        }
        
        if not conversation_history:
            return messages, stats
        
        # 计算历史对话的token数
        history_tokens = self.count_messages_tokens([
            {"role": "assistant", "content": msg.get("content", "")} 
            for msg in conversation_history
        ])
        
        if history_tokens <= available_tokens:
            # 历史对话可以完全包含
            context_content = self._format_full_history(conversation_history)
            messages[1]["content"] = f"{current_prompt}\n\n{context_content}"
            stats["strategy"] = "full_history"
            stats["used_messages"] = len(conversation_history)
            
        else:
            # 需要压缩历史对话
            if len(conversation_history) <= 10:
                # 消息较少，使用滑动窗口
                context_content = self._format_sliding_window(conversation_history, available_tokens)
                stats["strategy"] = "sliding_window"
            else:
                # 消息较多，使用摘要+最近消息
                context_content = self._format_summary_recent(conversation_history, available_tokens)
                stats["strategy"] = "summary_recent"
            
            messages[1]["content"] = f"{current_prompt}\n\n{context_content}"
        
        # 最终检查
        final_tokens = self.count_messages_tokens(messages)
        stats["final_tokens"] = final_tokens
        stats["token_usage_ratio"] = final_tokens / self.max_context_tokens
        
        return messages, stats
    
    def _format_full_history(self, history: List[Dict]) -> str:
        """格式化完整历史"""
        formatted = "【完整讨论历史】\n"
        for i, msg in enumerate(history):
            speaker = msg.get("speaker", "未知")
            content = msg.get("content", "")
            timestamp = msg.get("timestamp", "")
            formatted += f"{i+1}. {speaker}({timestamp}): {content}\n"
        return formatted
    
    def _format_sliding_window(self, history: List[Dict], available_tokens: int) -> str:
        """使用滑动窗口格式化历史"""
        # 从最新消息开始，逐步添加直到接近token限制
        selected_messages = []
        current_tokens = 0
        
        for msg in reversed(history):
            msg_tokens = self.count_tokens(msg.get("content", ""))
            if current_tokens + msg_tokens < available_tokens * 0.8:  # 留20%缓冲
                selected_messages.insert(0, msg)
                current_tokens += msg_tokens
            else:
                break
        
        formatted = f"【最近{len(selected_messages)}条讨论】\n"
        for msg in selected_messages:
            speaker = msg.get("speaker", "未知")
            content = msg.get("content", "")
            formatted += f"• {speaker}: {content}\n"
        
        return formatted
    
    def _format_summary_recent(self, history: List[Dict], available_tokens: int) -> str:
        """使用摘要+最近消息格式化历史"""
        # 分割历史：早期消息用于摘要，最近消息保持完整
        split_point = max(len(history) - 5, len(history) // 2)
        
        early_messages = history[:split_point]
        recent_messages = history[split_point:]
        
        # 生成早期消息摘要
        summary = self.summarize_messages(early_messages, target_length=300)
        
        # 格式化最近消息
        recent_formatted = "【最近讨论详情】\n"
        for msg in recent_messages:
            speaker = msg.get("speaker", "未知")
            content = msg.get("content", "")
            recent_formatted += f"• {speaker}: {content}\n"
        
        return f"{summary}\n\n{recent_formatted}"
    
    def get_context_stats(self, conversation_history: List[Dict]) -> Dict:
        """获取上下文统计信息"""
        if not conversation_history:
            return {"total_messages": 0, "total_tokens": 0}
        
        total_tokens = sum(
            self.count_tokens(msg.get("content", "")) 
            for msg in conversation_history
        )
        
        speaker_stats = {}
        for msg in conversation_history:
            speaker = msg.get("speaker", "未知")
            if speaker not in speaker_stats:
                speaker_stats[speaker] = {"count": 0, "tokens": 0}
            
            speaker_stats[speaker]["count"] += 1
            speaker_stats[speaker]["tokens"] += self.count_tokens(msg.get("content", ""))
        
        return {
            "total_messages": len(conversation_history),
            "total_tokens": total_tokens,
            "speaker_stats": speaker_stats,
            "estimated_context_ratio": total_tokens / self.max_context_tokens
        }

# 全局上下文管理器实例
context_manager = ContextManager(max_tokens=4000)

def get_managed_context(system_prompt: str, 
                       conversation_history: List[Dict], 
                       current_prompt: str) -> Tuple[List[Dict], Dict]:
    """
    获取智能管理的上下文
    
    Args:
        system_prompt: 系统提示
        conversation_history: 对话历史
        current_prompt: 当前提示
        
    Returns:
        (优化后的消息列表, 统计信息)
    """
    return context_manager.manage_context(system_prompt, conversation_history, current_prompt)

def get_context_statistics(conversation_history: List[Dict]) -> Dict:
    """获取上下文统计信息"""
    return context_manager.get_context_stats(conversation_history)
