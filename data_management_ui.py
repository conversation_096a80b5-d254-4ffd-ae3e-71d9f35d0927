#!/usr/bin/env python3
"""
数据管理界面
"""

import streamlit as st
from datetime import datetime
import json

def show_data_management_page():
    """显示数据管理页面"""
    
    st.title("💾 数据管理中心")
    st.markdown("**管理所有本地存储的数据，包括场景、推演记录、配置等**")
    
    # 导入数据存储模块
    try:
        from data_storage import get_data_storage
        storage = get_data_storage()
        st.success("✅ 数据存储模块加载成功")
    except ImportError as e:
        st.error(f"❌ 数据存储模块加载失败: {e}")
        return
    
    # 存储概览
    st.markdown("### 📊 存储概览")
    
    storage_info = storage.get_storage_info()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        scenarios_count = storage_info["files"]["scenarios"]["items"]
        st.metric("自定义场景", scenarios_count)
    
    with col2:
        simulations_count = storage_info["files"]["simulations"]["items"]
        st.metric("推演记录", simulations_count)
    
    with col3:
        configs_count = storage_info["files"]["configs"]["items"]
        st.metric("推演配置", configs_count)
    
    with col4:
        total_size = sum(f["size"] for f in storage_info["files"].values())
        st.metric("总存储", f"{total_size} 字节")
    
    # 详细管理
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["🎬 推演记录", "📋 自定义场景", "⚙️ 推演配置", "🔧 系统管理", "📤 导入导出"])
    
    with tab1:
        show_simulation_records_management(storage)
    
    with tab2:
        show_scenarios_management(storage)
    
    with tab3:
        show_configs_management(storage)
    
    with tab4:
        show_system_management(storage)
    
    with tab5:
        show_import_export_management(storage)

def show_simulation_records_management(storage):
    """推演记录管理"""
    st.markdown("#### 🎬 推演记录管理")
    
    simulations = storage.get_simulations()
    
    if not simulations:
        st.info("📝 暂无推演记录")
        return
    
    # 搜索和筛选
    col1, col2 = st.columns([2, 1])
    
    with col1:
        search_term = st.text_input("🔍 搜索推演记录", placeholder="输入场景名称或关键词")
    
    with col2:
        sort_by = st.selectbox("排序方式", ["时间降序", "时间升序", "消息数量"])
    
    # 筛选和排序
    filtered_sims = simulations.copy()
    
    if search_term:
        filtered_sims = {
            k: v for k, v in simulations.items()
            if search_term.lower() in v.get('scenario', {}).get('title', '').lower()
        }
    
    # 排序
    if sort_by == "时间降序":
        sorted_sims = sorted(filtered_sims.items(), key=lambda x: x[1].get('saved_at', ''), reverse=True)
    elif sort_by == "时间升序":
        sorted_sims = sorted(filtered_sims.items(), key=lambda x: x[1].get('saved_at', ''))
    else:  # 消息数量
        sorted_sims = sorted(filtered_sims.items(), key=lambda x: len(x[1].get('messages', [])), reverse=True)
    
    # 显示推演记录
    for sim_id, sim_data in sorted_sims:
        with st.container():
            col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
            
            with col1:
                scenario_title = sim_data.get('scenario', {}).get('title', '未知场景')
                st.write(f"**{scenario_title}**")
                
                saved_at = sim_data.get('saved_at', '')
                if saved_at:
                    st.caption(f"保存时间: {saved_at[:19]}")
                
                message_count = len(sim_data.get('messages', []))
                agent_count = len(sim_data.get('agents', []))
                st.caption(f"消息: {message_count} 条 | 智能体: {agent_count} 个")
            
            with col2:
                if st.button("👁️", key=f"view_sim_{sim_id}", help="查看详情"):
                    st.session_state[f"show_sim_{sim_id}"] = not st.session_state.get(f"show_sim_{sim_id}", False)
            
            with col3:
                if st.button("📥", key=f"load_sim_{sim_id}", help="加载到当前会话"):
                    # 加载推演记录到当前会话
                    st.session_state.simulation_messages = sim_data.get('messages', [])
                    st.session_state.simulation_config = sim_data.get('config', {})
                    st.session_state.current_round = sim_data.get('current_round', 0)
                    st.success(f"✅ 已加载推演记录: {scenario_title}")
                    st.rerun()
            
            with col4:
                if st.button("🗑️", key=f"delete_sim_{sim_id}", help="删除"):
                    if storage.delete_simulation(sim_id):
                        st.success(f"✅ 已删除推演记录")
                        st.rerun()
                    else:
                        st.error("❌ 删除失败")
            
            # 显示详情
            if st.session_state.get(f"show_sim_{sim_id}", False):
                with st.expander(f"📄 {scenario_title} 详情", expanded=True):
                    
                    # 基本信息
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write("**场景信息**:")
                        scenario = sim_data.get('scenario', {})
                        st.write(f"- 标题: {scenario.get('title', '未知')}")
                        st.write(f"- 类型: {scenario.get('type', '未知')}")
                        st.write(f"- 难度: {scenario.get('difficulty', '未知')}")
                    
                    with col2:
                        st.write("**推演信息**:")
                        st.write(f"- 当前轮次: {sim_data.get('current_round', 0)}")
                        st.write(f"- 阶段: {sim_data.get('phase', '未知')}")
                        st.write(f"- 消息数: {len(sim_data.get('messages', []))}")
                    
                    # 消息历史
                    messages = sim_data.get('messages', [])
                    if messages:
                        st.write("**消息历史** (最近10条):")
                        for msg in messages[-10:]:
                            st.write(f"**{msg['speaker']}** ({msg['timestamp']}): {msg['content'][:100]}...")
            
            st.divider()

def show_scenarios_management(storage):
    """自定义场景管理"""
    st.markdown("#### 📋 自定义场景管理")
    
    scenarios = storage.get_scenarios()
    
    if not scenarios:
        st.info("📝 暂无自定义场景")
        return
    
    for scenario_id, scenario_data in scenarios.items():
        with st.container():
            col1, col2, col3 = st.columns([3, 1, 1])
            
            with col1:
                st.write(f"**{scenario_data['title']}**")
                st.caption(f"类型: {scenario_data.get('type', '未知')} | 难度: {scenario_data.get('difficulty', '未知')}")
                st.caption(f"创建时间: {scenario_data.get('created_at', '未知')[:19]}")
            
            with col2:
                if st.button("👁️", key=f"view_scenario_{scenario_id}", help="查看详情"):
                    st.session_state[f"show_scenario_{scenario_id}"] = not st.session_state.get(f"show_scenario_{scenario_id}", False)
            
            with col3:
                if st.button("🗑️", key=f"delete_scenario_{scenario_id}", help="删除"):
                    if storage.delete_scenario(scenario_id):
                        st.success(f"✅ 已删除场景")
                        st.rerun()
                    else:
                        st.error("❌ 删除失败")
            
            # 显示详情
            if st.session_state.get(f"show_scenario_{scenario_id}", False):
                with st.expander(f"📄 {scenario_data['title']} 详情", expanded=True):
                    st.json(scenario_data)
            
            st.divider()

def show_configs_management(storage):
    """推演配置管理"""
    st.markdown("#### ⚙️ 推演配置管理")
    
    configs = storage.get_configs()
    
    if not configs:
        st.info("📝 暂无推演配置")
        return
    
    for config_id, config_data in configs.items():
        with st.container():
            col1, col2, col3 = st.columns([3, 1, 1])
            
            with col1:
                scenario_title = config_data.get('scenario', {}).get('title', '未知场景')
                st.write(f"**{scenario_title} 配置**")
                
                agents = config_data.get('selected_agents', [])
                st.caption(f"智能体: {len(agents)} 个 | 轮数: {config_data.get('max_rounds', 0)}")
                st.caption(f"保存时间: {config_data.get('saved_at', '未知')[:19]}")
            
            with col2:
                if st.button("👁️", key=f"view_config_{config_id}", help="查看详情"):
                    st.session_state[f"show_config_{config_id}"] = not st.session_state.get(f"show_config_{config_id}", False)
            
            with col3:
                if st.button("🗑️", key=f"delete_config_{config_id}", help="删除"):
                    if storage.delete_config(config_id):
                        st.success(f"✅ 已删除配置")
                        st.rerun()
                    else:
                        st.error("❌ 删除失败")
            
            # 显示详情
            if st.session_state.get(f"show_config_{config_id}", False):
                with st.expander(f"📄 配置详情", expanded=True):
                    st.json(config_data)
            
            st.divider()

def show_system_management(storage):
    """系统管理"""
    st.markdown("#### 🔧 系统管理")
    
    # 存储详情
    storage_info = storage.get_storage_info()
    
    with st.expander("📋 存储详情", expanded=False):
        st.json(storage_info)
    
    # 清理功能
    st.markdown("**🧹 数据清理**")
    
    col1, col2 = st.columns(2)
    
    with col1:
        days = st.number_input("清理多少天前的数据", min_value=1, max_value=365, value=30)
        
        if st.button("🧹 清理旧数据"):
            cleaned_count = storage.cleanup_old_data(days)
            if cleaned_count > 0:
                st.success(f"✅ 已清理 {cleaned_count} 条旧记录")
            else:
                st.info("ℹ️ 没有找到需要清理的数据")
    
    with col2:
        st.warning("⚠️ 危险操作")
        
        if st.button("💥 清空所有数据", type="secondary"):
            st.error("⚠️ 这将删除所有本地数据！")
            
            if st.button("确认清空所有数据", key="confirm_clear_all_data"):
                # 清空所有数据
                for data_type in ["scenarios", "simulations", "configs", "user_agents", "custom_orders"]:
                    storage.data[data_type] = {}
                    storage._save_file(data_type, {})
                
                st.success("✅ 已清空所有数据")
                st.rerun()

def show_import_export_management(storage):
    """导入导出管理"""
    st.markdown("#### 📤 导入导出管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**📤 导出数据**")
        
        export_filename = st.text_input(
            "导出文件名", 
            value=f"data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        if st.button("📤 导出所有数据"):
            if storage.export_all_data(export_filename):
                st.success(f"✅ 已导出到 {export_filename}")
            else:
                st.error("❌ 导出失败")
    
    with col2:
        st.markdown("**📥 导入数据**")
        
        uploaded_file = st.file_uploader("选择导入文件", type=['json'])
        
        if uploaded_file is not None:
            overwrite = st.checkbox("覆盖现有数据", value=False)
            
            if st.button("📥 导入数据"):
                try:
                    # 保存上传的文件
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
                        tmp_file.write(uploaded_file.getvalue().decode('utf-8'))
                        tmp_filename = tmp_file.name
                    
                    # 执行导入
                    if storage.import_all_data(tmp_filename, overwrite):
                        st.success("✅ 数据导入成功")
                        st.rerun()
                    else:
                        st.error("❌ 数据导入失败")
                    
                    # 清理临时文件
                    import os
                    os.unlink(tmp_filename)
                    
                except Exception as e:
                    st.error(f"❌ 导入失败: {e}")

if __name__ == "__main__":
    show_data_management_page()
