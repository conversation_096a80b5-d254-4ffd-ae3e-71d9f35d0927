#!/usr/bin/env python3
"""
通用数据本地存储管理系统
"""

import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

class DataStorage:
    """通用数据存储管理器"""
    
    def __init__(self, storage_dir: str = "data_storage"):
        """
        初始化存储管理器
        
        Args:
            storage_dir: 存储目录
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        
        # 各类数据的存储文件
        self.files = {
            "scenarios": self.storage_dir / "custom_scenarios.json",
            "simulations": self.storage_dir / "simulation_records.json", 
            "configs": self.storage_dir / "simulation_configs.json",
            "user_agents": self.storage_dir / "user_agents.json",
            "custom_orders": self.storage_dir / "custom_orders.json",
            "settings": self.storage_dir / "user_settings.json"
        }
        
        # 数据缓存
        self.data = {}
        self.load_all_data()
    
    def load_all_data(self):
        """加载所有数据"""
        for data_type, file_path in self.files.items():
            self.data[data_type] = self._load_file(file_path)
            print(f"✅ 已加载 {data_type}: {len(self.data[data_type])} 项")
    
    def _load_file(self, file_path: Path) -> Dict:
        """加载单个文件"""
        try:
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('data', {})
            return {}
        except Exception as e:
            print(f"❌ 加载文件失败 {file_path}: {e}")
            return {}
    
    def _save_file(self, data_type: str, data: Dict) -> bool:
        """保存单个文件"""
        try:
            file_path = self.files[data_type]
            save_data = {
                "data": data,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0",
                "count": len(data)
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已保存 {data_type}: {len(data)} 项")
            return True
            
        except Exception as e:
            print(f"❌ 保存文件失败 {data_type}: {e}")
            return False
    
    # 自定义场景管理
    def add_scenario(self, scenario_id: str, scenario_data: Dict) -> bool:
        """添加自定义场景"""
        scenario_data['created_at'] = datetime.now().isoformat()
        scenario_data['updated_at'] = datetime.now().isoformat()
        
        self.data['scenarios'][scenario_id] = scenario_data
        return self._save_file('scenarios', self.data['scenarios'])
    
    def get_scenarios(self) -> Dict:
        """获取所有自定义场景"""
        return self.data['scenarios'].copy()
    
    def delete_scenario(self, scenario_id: str) -> bool:
        """删除自定义场景"""
        if scenario_id in self.data['scenarios']:
            del self.data['scenarios'][scenario_id]
            return self._save_file('scenarios', self.data['scenarios'])
        return False
    
    # 推演记录管理
    def save_simulation(self, simulation_id: str, simulation_data: Dict) -> bool:
        """保存推演记录"""
        simulation_data['saved_at'] = datetime.now().isoformat()
        
        self.data['simulations'][simulation_id] = simulation_data
        return self._save_file('simulations', self.data['simulations'])
    
    def get_simulations(self) -> Dict:
        """获取所有推演记录"""
        return self.data['simulations'].copy()
    
    def get_simulation(self, simulation_id: str) -> Optional[Dict]:
        """获取特定推演记录"""
        return self.data['simulations'].get(simulation_id, None)
    
    def delete_simulation(self, simulation_id: str) -> bool:
        """删除推演记录"""
        if simulation_id in self.data['simulations']:
            del self.data['simulations'][simulation_id]
            return self._save_file('simulations', self.data['simulations'])
        return False
    
    # 推演配置管理
    def save_config(self, config_id: str, config_data: Dict) -> bool:
        """保存推演配置"""
        config_data['saved_at'] = datetime.now().isoformat()
        
        self.data['configs'][config_id] = config_data
        return self._save_file('configs', self.data['configs'])
    
    def get_configs(self) -> Dict:
        """获取所有推演配置"""
        return self.data['configs'].copy()
    
    def get_config(self, config_id: str) -> Optional[Dict]:
        """获取特定推演配置"""
        return self.data['configs'].get(config_id, None)
    
    def delete_config(self, config_id: str) -> bool:
        """删除推演配置"""
        if config_id in self.data['configs']:
            del self.data['configs'][config_id]
            return self._save_file('configs', self.data['configs'])
        return False
    
    # 用户智能体管理
    def save_user_agents(self, user_agents: List[str]) -> bool:
        """保存用户智能体列表"""
        data = {
            "user_agents": user_agents,
            "updated_at": datetime.now().isoformat()
        }
        self.data['user_agents'] = data
        return self._save_file('user_agents', self.data['user_agents'])
    
    def get_user_agents(self) -> List[str]:
        """获取用户智能体列表"""
        data = self.data['user_agents']
        if isinstance(data, dict):
            return data.get('user_agents', [])
        return []
    
    # 自定义发言顺序管理
    def save_custom_order(self, order_id: str, order_data: List[str]) -> bool:
        """保存自定义发言顺序"""
        data = {
            "order": order_data,
            "saved_at": datetime.now().isoformat()
        }
        self.data['custom_orders'][order_id] = data
        return self._save_file('custom_orders', self.data['custom_orders'])
    
    def get_custom_order(self, order_id: str) -> Optional[List[str]]:
        """获取自定义发言顺序"""
        order_data = self.data['custom_orders'].get(order_id, None)
        if order_data:
            return order_data.get('order', [])
        return None
    
    def get_custom_orders(self) -> Dict:
        """获取所有自定义发言顺序"""
        return self.data['custom_orders'].copy()
    
    # 用户设置管理
    def save_setting(self, key: str, value: Any) -> bool:
        """保存用户设置"""
        self.data['settings'][key] = {
            "value": value,
            "updated_at": datetime.now().isoformat()
        }
        return self._save_file('settings', self.data['settings'])
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """获取用户设置"""
        setting_data = self.data['settings'].get(key, None)
        if setting_data:
            return setting_data.get('value', default)
        return default
    
    def get_settings(self) -> Dict:
        """获取所有用户设置"""
        return self.data['settings'].copy()
    
    # 通用方法
    def export_all_data(self, export_file: str) -> bool:
        """导出所有数据"""
        try:
            export_data = {
                "exported_at": datetime.now().isoformat(),
                "data": self.data
            }
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已导出所有数据到 {export_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出数据失败: {e}")
            return False
    
    def import_all_data(self, import_file: str, overwrite: bool = False) -> bool:
        """导入所有数据"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            imported_data = import_data.get('data', {})
            
            for data_type, data in imported_data.items():
                if data_type in self.data:
                    if overwrite:
                        self.data[data_type] = data
                    else:
                        # 合并数据
                        self.data[data_type].update(data)
                    
                    self._save_file(data_type, self.data[data_type])
            
            print(f"✅ 已导入数据")
            return True
            
        except Exception as e:
            print(f"❌ 导入数据失败: {e}")
            return False
    
    def get_storage_info(self) -> Dict:
        """获取存储信息"""
        info = {
            "storage_dir": str(self.storage_dir),
            "files": {},
            "total_items": 0
        }
        
        for data_type, file_path in self.files.items():
            file_info = {
                "file_path": str(file_path),
                "exists": file_path.exists(),
                "size": file_path.stat().st_size if file_path.exists() else 0,
                "items": len(self.data.get(data_type, {}))
            }
            info["files"][data_type] = file_info
            info["total_items"] += file_info["items"]
        
        return info
    
    def cleanup_old_data(self, days: int = 30) -> int:
        """清理旧数据"""
        from datetime import timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days)
        cleaned_count = 0
        
        # 清理旧的推演记录
        simulations_to_remove = []
        for sim_id, sim_data in self.data['simulations'].items():
            saved_at = sim_data.get('saved_at', '')
            if saved_at:
                try:
                    saved_date = datetime.fromisoformat(saved_at)
                    if saved_date < cutoff_date:
                        simulations_to_remove.append(sim_id)
                except:
                    pass
        
        for sim_id in simulations_to_remove:
            del self.data['simulations'][sim_id]
            cleaned_count += 1
        
        if simulations_to_remove:
            self._save_file('simulations', self.data['simulations'])
        
        return cleaned_count

# 全局存储实例
data_storage = DataStorage()

def get_data_storage() -> DataStorage:
    """获取数据存储实例"""
    return data_storage

# 兼容性函数
def load_custom_scenarios() -> Dict:
    """加载自定义场景"""
    return data_storage.get_scenarios()

def save_custom_scenario(scenario_id: str, scenario_data: Dict) -> bool:
    """保存自定义场景"""
    return data_storage.add_scenario(scenario_id, scenario_data)

def delete_custom_scenario(scenario_id: str) -> bool:
    """删除自定义场景"""
    return data_storage.delete_scenario(scenario_id)
