#!/usr/bin/env python3
"""
智囊团辩论模拟 - 真正的互动讨论和辩论
"""

import streamlit as st
import time
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

st.set_page_config(page_title="智囊团辩论", page_icon="🎯", layout="wide")

# 导入AI功能
try:
    from utils.llm_config import generate_response
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

def generate_debate_response(agent_name: str, scenario: str, all_messages: list, current_round: int) -> str:
    """生成辩论式回应"""
    
    if AI_AVAILABLE and generate_response:
        # 智囊团成员详细设定
        debate_profiles = {
            "特朗普": {
                "identity": "美国前总统，最高决策者",
                "personality": "直接、强势、商人思维、重视实力",
                "position": "倾向强硬回应，但考虑成本效益，美国利益优先",
                "debate_style": "会直接挑战其他观点，提出反驳，做出明确决策倾向"
            },
            "拜登": {
                "identity": "美国现任总统，最高决策者",
                "personality": "温和、外交经验丰富、重视盟友关系",
                "position": "倾向多边合作和外交解决，但保持军事选项",
                "debate_style": "会寻求各方平衡，提出妥协方案，强调国际合作"
            },
            "军事专家": {
                "identity": "五角大楼高级军事顾问",
                "personality": "专业、客观、数据驱动、风险意识强",
                "position": "从军事可行性和战略风险角度分析",
                "debate_style": "会用专业数据反驳不现实的建议，提供军事选项评估"
            },
            "政治专家": {
                "identity": "国际关系和地缘政治专家",
                "personality": "深度思考、长远视角、理论功底深厚",
                "position": "从国际法、地缘政治、长期战略影响角度分析",
                "debate_style": "会分析其他观点的深层政治后果，提出战略性考量"
            }
        }
        
        profile = debate_profiles.get(agent_name, {})
        
        # 分析讨论历史，找出争议点和立场
        if len(all_messages) > 1:
            recent_context = all_messages[-4:]  # 最近4条消息
            
            # 提取关键观点和分歧
            context_analysis = "当前讨论中的主要观点和分歧：\n"
            for msg in recent_context:
                context_analysis += f"• {msg['speaker']}: {msg['content'][:100]}...\n"
            
            # 构建辩论提示
            debate_prompt = f"""你是{profile['identity']}，正在参与台海危机应对策略的高级智囊团会议。

你的特点：{profile['personality']}
你的立场：{profile['position']}
辩论风格：{profile['debate_style']}

{context_analysis}

现在轮到你发言。请：
1. 明确回应前面发言者的具体观点（赞同/质疑/反驳）
2. 指出你认为的问题或盲点
3. 提出你的独特见解和建议
4. 如果有分歧，要进行有理有据的辩论
5. 推动讨论向更深层次发展

这是第{current_round}轮讨论，请积极参与辩论，不要只是重复立场。用中文回应，控制在150字以内。"""
        else:
            debate_prompt = f"""你是{profile['identity']}，参与台海危机应对策略讨论。

场景：{scenario}

作为智囊团成员，请分析形势并提出你的战略建议。这是开场发言，要为后续辩论奠定基础。"""
        
        messages = [
            {"role": "system", "content": f"你是{agent_name}，{profile['identity']}。请积极参与智囊团辩论，与其他成员进行建设性的争论和讨论。"},
            {"role": "user", "content": debate_prompt}
        ]
        
        try:
            response = generate_response(messages, max_tokens=200, temperature=0.8)
            return response
        except Exception as e:
            return f"[{agent_name}] 基于当前讨论，我认为需要从{profile['position']}的角度进一步分析。"
    else:
        # 模拟辩论回应
        mock_responses = {
            "特朗普": "我不同意刚才的温和建议！我们必须展现实力，让对手知道美国的决心。但我们也要聪明，不能陷入无谓的冲突。",
            "拜登": "我理解强硬立场的必要性，但我们不能忽视外交途径。与盟友协调一致的行动比单边行动更有效。",
            "军事专家": "从军事角度看，刚才提到的方案存在风险。我们需要评估敌方的反制能力和我们的作战准备程度。",
            "政治专家": "各位的观点都有道理，但我们必须考虑长期的地缘政治后果。短期的军事胜利可能带来长期的政治代价。"
        }
        return mock_responses.get(agent_name, f"作为{agent_name}，我认为需要综合考虑各方观点。")

def stream_display(content: str, agent_name: str):
    """流式显示"""
    placeholder = st.empty()
    displayed_text = ""
    
    for char in content:
        displayed_text += char
        placeholder.markdown(f"{displayed_text}<span style='animation: blink 1s infinite; color: #666;'>|</span>", unsafe_allow_html=True)
        time.sleep(0.03)
    
    placeholder.markdown(displayed_text)
    return displayed_text

def main():
    """主函数"""
    
    st.title("🎯 智囊团辩论模拟")
    st.markdown("**真正的互动讨论、辩论和协作决策**")
    
    # 添加CSS
    st.markdown("""
    <style>
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    .debate-highlight {
        background: linear-gradient(90deg, #ff6b6b, #feca57);
        padding: 10px;
        border-radius: 8px;
        margin: 10px 0;
        color: white;
        font-weight: bold;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 初始化
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'debate_agents' not in st.session_state:
        st.session_state.debate_agents = ["特朗普", "军事专家", "张三", "政治专家", "拜登", "李四"]
    
    if 'current_speaker_index' not in st.session_state:
        st.session_state.current_speaker_index = 0
    
    if 'user_agents' not in st.session_state:
        st.session_state.user_agents = ["张三", "李四"]
    
    if 'debate_round' not in st.session_state:
        st.session_state.debate_round = 0
    
    if 'max_rounds' not in st.session_state:
        st.session_state.max_rounds = 12
    
    if 'last_auto_time' not in st.session_state:
        st.session_state.last_auto_time = 0
    
    # 侧边栏
    with st.sidebar:
        st.header("🎯 辩论控制")
        
        scenario = "台海危机应对策略"
        st.info(f"**辩论主题**: {scenario}")
        
        # 辩论设置
        enable_stream = st.checkbox("🌊 流式输出", value=True)
        auto_debate = st.checkbox("🔄 自动辩论", value=False)
        debate_interval = st.slider("发言间隔(秒)", 2, 10, 4)
        st.session_state.max_rounds = st.slider("辩论轮数", 8, 20, st.session_state.max_rounds)
        
        # 进度显示
        if st.session_state.debate_round > 0:
            progress = min(st.session_state.debate_round / st.session_state.max_rounds, 1.0)
            st.progress(progress, text=f"辩论进度: {st.session_state.debate_round}/{st.session_state.max_rounds}")
        
        # 参与者状态
        st.markdown("**🎭 辩论参与者**")
        for i, agent in enumerate(st.session_state.debate_agents):
            is_user = agent in st.session_state.user_agents
            agent_type = "👤 用户" if is_user else "🤖 AI"
            
            if i == st.session_state.current_speaker_index:
                st.markdown(f"👉 **{agent}** {agent_type} (发言中)")
            else:
                st.write(f"• {agent} {agent_type}")
        
        # 控制按钮
        if st.button("🎬 开始辩论"):
            if not st.session_state.messages:
                # 主持人开场
                opening = "各位智囊团成员，欢迎参加台海危机应对策略辩论。请各位充分表达观点，进行深入讨论和辩论，最终形成决策建议。"
                
                opening_msg = {
                    "speaker": "辩论主持人",
                    "content": opening,
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(opening_msg)
                st.session_state.current_speaker_index = 0
                st.rerun()
        
        if st.button("➡️ 下一位发言"):
            handle_next_speaker(scenario, enable_stream)
        
        if st.button("🔄 重置辩论"):
            st.session_state.messages = []
            st.session_state.current_speaker_index = 0
            st.session_state.debate_round = 0
            st.session_state.last_auto_time = 0
            st.rerun()
    
    # 自动辩论逻辑
    if auto_debate and st.session_state.messages and st.session_state.debate_round < st.session_state.max_rounds:
        current_time = time.time()
        current_agent = st.session_state.debate_agents[st.session_state.current_speaker_index]
        is_user_agent = current_agent in st.session_state.user_agents
        
        if not is_user_agent:
            # AI智能体自动发言
            if current_time - st.session_state.last_auto_time >= debate_interval:
                st.info(f"🤖 {current_agent} 正在发言...")
                handle_next_speaker(scenario, enable_stream)
                st.session_state.last_auto_time = current_time
                st.rerun()
            else:
                remaining = debate_interval - (current_time - st.session_state.last_auto_time)
                st.info(f"⏱️ {current_agent} 将在 {remaining:.1f}秒后发言")
                time.sleep(0.5)
                st.rerun()
        else:
            st.warning(f"⏸️ 轮到用户 **{current_agent}**，请在下方发言")
    
    # 显示辩论记录
    st.markdown("### 🎯 智囊团辩论记录")
    
    for i, msg in enumerate(st.session_state.messages):
        is_user_msg = msg["speaker"] in st.session_state.user_agents
        
        # 特殊标记重要发言
        if i > 0 and any(keyword in msg["content"] for keyword in ["不同意", "反对", "质疑", "但是", "然而"]):
            st.markdown('<div class="debate-highlight">🔥 激烈辩论中</div>', unsafe_allow_html=True)
        
        with st.chat_message("user" if is_user_msg else "assistant"):
            st.write(f"**{msg['speaker']}** - {msg['timestamp']}")
            st.write(msg['content'])
    
    # 用户发言区域
    if st.session_state.debate_agents and st.session_state.debate_round < st.session_state.max_rounds:
        current_agent = st.session_state.debate_agents[st.session_state.current_speaker_index]
        is_user_agent = current_agent in st.session_state.user_agents
        
        if is_user_agent:
            st.markdown("---")
            st.markdown(f"### 💬 {current_agent} 的发言")
            st.info("请积极参与辩论，回应其他成员的观点，提出你的见解")
            
            user_input = st.text_area(
                f"以 {current_agent} 身份发言:",
                height=120,
                key=f"debate_input_{current_agent}_{st.session_state.debate_round}",
                placeholder="请回应其他成员的观点，提出你的分析和建议..."
            )
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🎤 发言", type="primary", use_container_width=True):
                    if user_input and user_input.strip():
                        new_msg = {
                            "speaker": current_agent,
                            "content": user_input.strip(),
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        }
                        st.session_state.messages.append(new_msg)
                        st.session_state.debate_round += 1
                        st.session_state.current_speaker_index = (st.session_state.current_speaker_index + 1) % len(st.session_state.debate_agents)
                        st.session_state.last_auto_time = time.time()
                        st.success(f"✅ {current_agent} 发言完成")
                        st.rerun()
                    else:
                        st.error("请输入发言内容")
            
            with col2:
                if st.button("🤖 AI代发", use_container_width=True):
                    response = generate_debate_response(current_agent, scenario, st.session_state.messages, st.session_state.debate_round)
                    
                    new_msg = {
                        "speaker": current_agent,
                        "content": f"[AI代发] {response}",
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    }
                    st.session_state.messages.append(new_msg)
                    st.session_state.debate_round += 1
                    st.session_state.current_speaker_index = (st.session_state.current_speaker_index + 1) % len(st.session_state.debate_agents)
                    st.session_state.last_auto_time = time.time()
                    st.success(f"✅ AI为 {current_agent} 代发完成")
                    st.rerun()
    
    # 辩论结束
    if st.session_state.debate_round >= st.session_state.max_rounds:
        st.success("🎉 智囊团辩论结束！")
        st.balloons()

def handle_next_speaker(scenario, enable_stream):
    """处理下一位发言者"""
    if st.session_state.debate_agents:
        current_agent = st.session_state.debate_agents[st.session_state.current_speaker_index]
        is_user_agent = current_agent in st.session_state.user_agents
        
        if not is_user_agent:
            # AI智能体发言
            response = generate_debate_response(current_agent, scenario, st.session_state.messages, st.session_state.debate_round)
            
            with st.chat_message("assistant"):
                if enable_stream:
                    displayed_response = stream_display(response, current_agent)
                else:
                    st.write(f"**{current_agent}** - {datetime.now().strftime('%H:%M:%S')}")
                    st.write(response)
                    displayed_response = response
            
            new_msg = {
                "speaker": current_agent,
                "content": displayed_response,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(new_msg)
            st.session_state.debate_round += 1
            st.session_state.current_speaker_index = (st.session_state.current_speaker_index + 1) % len(st.session_state.debate_agents)

if __name__ == "__main__":
    main()
