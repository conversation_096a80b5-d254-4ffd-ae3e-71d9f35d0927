#!/usr/bin/env python3
"""
AI连接诊断脚本
"""

import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_ai_connection():
    """测试AI连接"""
    
    print("🔍 AI连接诊断")
    print("=" * 50)
    
    try:
        # 测试导入
        print("1. 测试模块导入...")
        from utils.llm_config import llm_config, test_llm_connection, generate_response
        print("✅ 模块导入成功")
        
        # 检查配置
        print("\n2. 检查配置...")
        print(f"API Key: {llm_config.api_key[:20]}..." if llm_config.api_key else "❌ API Key未配置")
        print(f"Base URL: {llm_config.base_url}")
        print(f"Model: {llm_config.model_name}")
        print(f"配置状态: {'✅ 已配置' if llm_config.is_configured() else '❌ 未配置'}")
        
        # 测试连接
        print("\n3. 测试连接...")
        result = test_llm_connection()
        
        if result['success']:
            print("✅ 连接成功!")
            print(f"响应: {result['response']}")
            print(f"模型: {result['model']}")
        else:
            print("❌ 连接失败!")
            print(f"错误: {result['error']}")
            return False
        
        # 测试生成
        print("\n4. 测试对话生成...")
        messages = [
            {"role": "system", "content": "你是一个测试助手，请简短回复。"},
            {"role": "user", "content": "请说'测试成功'"}
        ]
        
        response = generate_response(messages)
        print(f"生成响应: {response}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_demo_functions():
    """测试演示应用中的函数"""
    
    print("\n🎭 测试演示应用函数")
    print("=" * 50)
    
    try:
        # 模拟智能体信息
        demo_agent = {
            "name": "测试智能体",
            "role": "测试角色",
            "type": "test"
        }
        
        demo_scenario = {
            "title": "测试场景",
            "context": "这是一个测试场景"
        }
        
        # 测试生成函数
        print("测试智能体响应生成...")
        
        # 这里需要导入demo_app中的函数
        import importlib.util
        spec = importlib.util.spec_from_file_location("demo_app", "demo_app.py")
        demo_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(demo_module)
        
        # 测试生成响应
        response = demo_module.generate_agent_response(
            "test_agent",
            demo_agent,
            demo_scenario,
            "请说'测试成功'",
            []
        )
        
        print(f"智能体响应: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示应用测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始AI诊断...")
    
    # 测试基础连接
    basic_ok = test_ai_connection()
    
    if basic_ok:
        # 测试演示应用
        demo_ok = test_demo_functions()
        
        if demo_ok:
            print("\n🎉 所有测试通过！AI功能正常")
        else:
            print("\n⚠️ 基础连接正常，但演示应用有问题")
    else:
        print("\n❌ 基础连接失败，请检查配置")
    
    input("\n按回车键退出...")
