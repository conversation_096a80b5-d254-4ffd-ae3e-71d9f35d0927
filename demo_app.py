#!/usr/bin/env python3
"""
Strategic Simulation System - 演示应用

简化版本，用于快速演示功能
"""

import streamlit as st
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from utils.llm_config import llm_config, test_llm_connection, generate_response
    print("✅ AI模块导入成功")
except ImportError as e:
    print(f"⚠️ AI模块导入失败: {e}")
    # 创建备用AI功能
    import os
    from dotenv import load_dotenv
    load_dotenv()

    class FallbackLLMConfig:
        def __init__(self):
            self.api_key = os.getenv('OPENAI_API_KEY')
            self.base_url = os.getenv('OPENAI_BASE_URL')
            self.model_name = os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B')

        def is_configured(self):
            return bool(self.api_key and self.api_key != 'your_openai_api_key_here')

        def get_provider_info(self):
            return {
                'model': self.model_name,
                'provider': 'siliconflow',
                'base_url': self.base_url or 'https://api.siliconflow.cn/v1',
                'configured': str(self.is_configured())
            }

    def fallback_test_connection():
        try:
            import openai
            client = openai.OpenAI(
                api_key=os.getenv('OPENAI_API_KEY'),
                base_url=os.getenv('OPENAI_BASE_URL')
            )

            response = client.chat.completions.create(
                model=os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B'),
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=50
            )

            return {
                'success': True,
                'response': response.choices[0].message.content,
                'model': response.model
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def fallback_generate_response(messages, **kwargs):
        try:
            import openai
            client = openai.OpenAI(
                api_key=os.getenv('OPENAI_API_KEY'),
                base_url=os.getenv('OPENAI_BASE_URL')
            )

            # 检查是否需要流式输出
            if kwargs.get('stream', False):
                return client.chat.completions.create(
                    model=os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B'),
                    messages=messages,
                    max_tokens=kwargs.get('max_tokens', 300),
                    temperature=kwargs.get('temperature', 0.7),
                    stream=True
                )
            else:
                response = client.chat.completions.create(
                    model=os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B'),
                    messages=messages,
                    max_tokens=kwargs.get('max_tokens', 300),
                    temperature=kwargs.get('temperature', 0.7)
                )
                return response.choices[0].message.content
        except Exception as e:
            return f"AI生成失败: {str(e)}"

    llm_config = FallbackLLMConfig()
    test_llm_connection = fallback_test_connection
    generate_response = fallback_generate_response
    print("✅ 使用备用AI功能")

# 设置页面配置
st.set_page_config(
    page_title="战略推演系统",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

def load_demo_data():
    """加载演示数据"""
    
    # 演示智能体
    demo_agents = {
        "trump": {
            "name": "唐纳德·特朗普",
            "role": "美国现任总统",
            "type": "political_leader",
            "expertise": ["商业谈判", "美国优先政策", "国际贸易"]
        },
        "biden": {
            "name": "乔·拜登",
            "role": "美国前任总统",
            "type": "political_leader",
            "expertise": ["外交政策", "两党合作", "医疗保健"]
        },
        "moderator": {
            "name": "推演导调",
            "role": "推演主持人",
            "type": "simulation_controller",
            "expertise": ["战略分析", "讨论管理", "流程控制"]
        },
        "military_expert": {
            "name": "军事专家",
            "role": "军事战略专家",
            "type": "domain_expert",
            "expertise": ["军事战略", "国防规划", "作战分析"]
        },
        "political_expert": {
            "name": "政治专家",
            "role": "政治学专家",
            "type": "domain_expert",
            "expertise": ["美国政治", "国际关系", "政策分析"]
        }
    }
    
    # 演示场景
    demo_scenarios = {
        "taiwan_crisis": {
            "title": "台海危机应对策略",
            "description": "针对台海地区紧张局势升级的战略应对",
            "context": "情报显示台海周边军事活动增加，美国应如何应对？",
            "considerations": ["军事选项", "经济影响", "盟友协调", "升级风险"]
        },
        "trade_war": {
            "title": "中美贸易战升级",
            "description": "应对中国新贸易限制措施的策略",
            "context": "中国对美国农产品征收新关税，美国应如何回应？",
            "considerations": ["经济影响", "农民支持", "WTO影响", "谈判策略"]
        },
        "middle_east": {
            "title": "中东军事干预决策",
            "description": "是否对地区冲突进行军事干预的决策",
            "context": "盟友国家请求美国在地区冲突中提供军事支持，美国是否应该干预？",
            "considerations": ["军事能力", "地区稳定", "国内支持", "国际法"]
        }
    }
    
    return demo_agents, demo_scenarios

def main():
    """主应用"""

    # 设置页面配置
    st.set_page_config(
        page_title="军事战略推演系统",
        page_icon="⚔️",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # 注入军事主题CSS
    from demo_app_military import MILITARY_CSS
    st.markdown(MILITARY_CSS, unsafe_allow_html=True)

    # 军事风格标题
    st.markdown("""
    <div class="command-center" style="text-align: center; padding: 30px; margin: 20px 0;">
        <div class="radar-container" style="margin-bottom: 20px;">
            <div class="radar-sweep"></div>
        </div>
        <h1 style="color: #90EE90; margin: 20px 0;">
            ⚔️ 军事战略推演指挥中心 ⚔️
        </h1>
        <h3 style="color: #32CD32; margin: 15px 0;">
            🎖️ AI驱动的战术决策仿真平台 🎖️
        </h3>
        <div class="mission-status" style="margin-top: 20px;">
            <div class="data-stream">
                >>> 系统状态: 在线 | 安全等级: 最高 | 准备状态: 就绪 <<<
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # 加载演示数据
    demo_agents, demo_scenarios = load_demo_data()
    
    # 军事风格侧边栏
    with st.sidebar:
        st.markdown("""
        <div class="tactical-panel" style="text-align: center; padding: 15px; margin-bottom: 20px;">
            <div class="radar-container" style="margin-bottom: 10px;">
                <div class="radar-sweep"></div>
            </div>
            <h3 style="color: #90EE90; margin: 0;">🎖️ 指挥控制台</h3>
            <div class="status-indicator status-online"></div>
            <span style="color: #32CD32; font-size: 12px;">系统在线</span>
        </div>
        """, unsafe_allow_html=True)

        # 页面选择
        page = st.selectbox(
            "📋 选择作战模块",
            ["🏠 指挥中心", "⚔️ 作战单位", "📚 情报库", "🗺️ 战场态势", "🚀 开始推演", "💾 数据管理", "📊 战果分析"]
        )
    
    # 主页面路由
    if page == "🏠 指挥中心":
        show_home_page(demo_agents, demo_scenarios)
    elif page == "⚔️ 作战单位":
        show_agents_page(demo_agents)
    elif page == "📚 情报库":
        # 显示知识库管理页面
        try:
            from knowledge_ui import show_knowledge_management_page
            show_knowledge_management_page()
        except ImportError:
            st.error("❌ 情报库功能未加载")
            st.info("💡 请确保 knowledge_base.py 和 knowledge_ui.py 文件存在")
    elif page == "🗺️ 战场态势":
        show_scenarios_page(demo_scenarios)
    elif page == "🚀 开始推演":
        show_simulation_page(demo_agents, demo_scenarios)
    elif page == "💾 数据管理":
        # 显示数据管理页面
        try:
            from data_management_ui import show_data_management_page
            show_data_management_page()
        except ImportError:
            st.error("❌ 数据管理功能未加载")
            st.info("💡 请确保 data_storage.py 和 data_management_ui.py 文件存在")
    elif page == "📊 战果分析":
        show_results_page()

def show_home_page(demo_agents, demo_scenarios):
    """显示军事风格主页"""

    st.markdown("""
    <div class="tactical-panel">
        <h2 style="color: #90EE90; text-align: center;">🎖️ 欢迎来到军事战略推演指挥中心</h2>
        <p style="text-align: center; color: #e8e8e8; font-size: 18px;">
            这是一个基于人工智能的多智能体军事战略决策仿真平台，为军事指挥官、战略分析师和决策者提供强大的推演工具。
        </p>
    </div>
    """, unsafe_allow_html=True)

    # 核心功能展示
    st.markdown("""
    <div class="tactical-panel">
        <h3 style="color: #90EE90;">⚔️ 核心作战能力</h3>
    </div>
    """, unsafe_allow_html=True)

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">🤖 多智能体协同作战</h4>
            <p style="color: #e8e8e8;">特朗普、军事专家等各领域智能体协同作战</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">💬 战术讨论与决策</h4>
            <p style="color: #e8e8e8;">可配置的发言顺序、讨论轮数和流程控制</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">🧠 战略分析与情报</h4>
            <p style="color: #e8e8e8;">基于AI的深度情况分析和决策建议</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">📚 军事知识库</h4>
            <p style="color: #e8e8e8;">RAG检索技术增强的专业军事知识库系统</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">👥 指挥官参与</h4>
            <p style="color: #e8e8e8;">实时参与讨论、提供观点和引导决策过程</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">🎭 角色扮演模拟</h4>
            <p style="color: #e8e8e8;">真实还原各政治人物的思维模式和表达风格</p>
        </div>
        """, unsafe_allow_html=True)

    # 系统状态仪表板
    st.markdown("""
    <div class="tactical-panel">
        <h3 style="color: #90EE90; text-align: center;">📊 作战系统状态仪表板</h3>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="stMetric">
            <h4 style="color: #90EE90;">⚔️ 可用作战单位</h4>
            <h2 style="color: #32CD32;">{len(demo_agents)}</h2>
            <p style="color: #e8e8e8;">个智能体</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="stMetric">
            <h4 style="color: #90EE90;">🗺️ 预设战场态势</h4>
            <h2 style="color: #32CD32;">{len(demo_scenarios)}</h2>
            <p style="color: #e8e8e8;">个场景</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        # Check LLM status
        if llm_config and llm_config.is_configured():
            status_color = "#32CD32"
            status_text = "🟢 已连接"
        else:
            status_color = "#DC143C"
            status_text = "🔴 未连接"

        st.markdown(f"""
        <div class="stMetric">
            <h4 style="color: #90EE90;">🤖 AI模型状态</h4>
            <h2 style="color: {status_color};">{status_text}</h2>
            <p style="color: #e8e8e8;">作战AI</p>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown("""
        <div class="stMetric">
            <h4 style="color: #90EE90;">⚡ 系统状态</h4>
            <h2 style="color: #32CD32;">🟢 战备就绪</h2>
            <p style="color: #e8e8e8;">指挥系统</p>
        </div>
        """, unsafe_allow_html=True)

    # AI模型配置信息
    if llm_config:
        st.markdown("### 🤖 AI模型配置")

        provider_info = llm_config.get_provider_info()

        col1, col2 = st.columns(2)
        with col1:
            st.info(f"**使用模型**: {provider_info['model']}")
            st.info(f"**服务提供商**: {provider_info['provider']}")

        with col2:
            st.info(f"**API接口**: {provider_info['base_url']}")
            st.info(f"**连接状态**: {'✅ 已连接' if provider_info['configured'] == 'True' else '❌ 未连接'}")

        # 测试连接按钮
        if st.button("🧪 测试AI模型连接"):
            with st.spinner("正在测试连接..."):
                if test_llm_connection:
                    result = test_llm_connection()

                    if result['success']:
                        st.success("✅ AI模型连接成功！")
                        st.write(f"**测试响应**: {result['response']}")
                        st.write(f"**当前模型**: {result['model']}")
                    else:
                        st.error("❌ AI模型连接失败！")
                        st.write(f"**错误信息**: {result['error']}")
                else:
                    st.warning("⚠️ 连接测试功能暂不可用")
    
    st.markdown("### 🚀 快速开始")
    st.info("👈 使用左侧菜单浏览不同功能，或直接进入 '🚀 开始推演' 页面开始您的战略推演")

def show_agents_page(demo_agents):
    """显示智能体页面"""

    st.header("🤖 智能体管理")

    # 初始化自定义智能体（从本地文件加载）
    if 'custom_agents' not in st.session_state:
        try:
            from agent_storage import load_custom_agents
            st.session_state.custom_agents = load_custom_agents()
            if st.session_state.custom_agents:
                print(f"✅ 已从本地加载 {len(st.session_state.custom_agents)} 个自定义智能体")
        except ImportError:
            st.session_state.custom_agents = {}
            print("⚠️ 智能体存储模块未加载，使用临时存储")

    # 合并预设和自定义智能体
    all_agents = {**demo_agents, **st.session_state.custom_agents}

    st.markdown("### 👥 所有智能体")

    for agent_id, agent in all_agents.items():
        is_custom = agent_id in st.session_state.custom_agents
        icon = "🆕" if is_custom else "🤖"

        with st.expander(f"{icon} {agent['name']} - {agent['role']} {'(自定义)' if is_custom else '(预设)'}"):
            col1, col2 = st.columns(2)

            with col1:
                st.write(f"**类型**: {agent['type']}")
                st.write(f"**角色**: {agent['role']}")
                if 'background' in agent:
                    st.write(f"**背景**: {agent['background']}")

            with col2:
                st.write(f"**专业领域**: {', '.join(agent['expertise'])}")
                if 'personality' in agent:
                    st.write(f"**性格特点**: {', '.join(agent['personality'])}")

            # 自定义智能体可以删除
            if is_custom:
                if st.button(f"🗑️ 删除智能体", key=f"delete_agent_{agent_id}"):
                    # 从本地文件删除
                    try:
                        from agent_storage import delete_custom_agent
                        if delete_custom_agent(agent_id):
                            del st.session_state.custom_agents[agent_id]
                            st.success(f"✅ 智能体 '{agent['name']}' 已从本地删除")
                        else:
                            del st.session_state.custom_agents[agent_id]
                            st.warning(f"⚠️ 智能体 '{agent['name']}' 已从当前会话删除，但本地删除失败")
                    except ImportError:
                        del st.session_state.custom_agents[agent_id]
                        st.warning(f"⚠️ 智能体 '{agent['name']}' 已从当前会话删除（本地存储功能未加载）")

                    st.rerun()

    # 存储管理
    st.markdown("### 💾 存储管理")

    try:
        from agent_storage import get_agent_storage
        storage = get_agent_storage()
        storage_info = storage.get_storage_info()

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("本地智能体", storage_info.get("agent_count", 0))

        with col2:
            file_size = storage_info.get("file_size", 0)
            st.metric("存储大小", f"{file_size} 字节")

        with col3:
            if st.button("📤 导出智能体"):
                export_file = f"agents_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                if storage.export_agents(export_file):
                    st.success(f"✅ 已导出到 {export_file}")
                else:
                    st.error("❌ 导出失败")

        with col4:
            if st.button("🔄 重新加载"):
                storage.load_agents()
                st.session_state.custom_agents = storage.get_all_agents()
                st.success("✅ 已重新加载")
                st.rerun()

        # 显示存储详情
        with st.expander("📋 存储详情", expanded=False):
            st.json(storage_info)

    except ImportError:
        st.warning("⚠️ 存储管理功能未加载")

    st.markdown("### 🆕 创建自定义智能体")

    with st.expander("创建新智能体", expanded=False):
        with st.form("create_agent_form"):
            col1, col2 = st.columns(2)

            with col1:
                name = st.text_input("智能体名称", placeholder="例如：张三")
                role = st.text_input("角色定位", placeholder="例如：经济学专家")
                agent_type = st.selectbox("智能体类型",
                    ["政治领袖", "领域专家", "商业领袖", "学术专家", "军事领袖", "自定义"],
                    format_func=lambda x: {
                        "政治领袖": "政治领袖 (political_leader)",
                        "领域专家": "领域专家 (domain_expert)",
                        "商业领袖": "商业领袖 (business_leader)",
                        "学术专家": "学术专家 (academic)",
                        "军事领袖": "军事领袖 (military_leader)",
                        "自定义": "自定义 (custom)"
                    }.get(x, x)
                )

            with col2:
                nationality = st.text_input("国籍", placeholder="例如：美国")
                background = st.text_area("背景介绍", placeholder="简要介绍智能体的背景和经历")

            expertise = st.text_input("专业领域 (逗号分隔)", placeholder="例如：经济学,金融政策,市场分析")
            personality = st.text_input("性格特点 (逗号分隔)", placeholder="例如：理性,客观,数据驱动")

            # 沟通风格
            communication_style = st.text_area("沟通风格", placeholder="例如：逻辑清晰，善用数据说话，表达简洁明了")

            # 核心观点
            core_values = st.text_area("核心观点/价值观", placeholder="例如：相信市场机制，注重实证分析，追求效率与公平的平衡")

            submitted = st.form_submit_button("🚀 创建智能体", type="primary")

            if submitted:
                if name and role:
                    # 生成智能体ID
                    agent_id = f"custom_{name.lower().replace(' ', '_')}"

                    # 映射中文类型到英文
                    type_mapping = {
                        "政治领袖": "political_leader",
                        "领域专家": "domain_expert",
                        "商业领袖": "business_leader",
                        "学术专家": "academic",
                        "军事领袖": "military_leader",
                        "自定义": "custom"
                    }

                    # 创建智能体
                    new_agent = {
                        "name": name,
                        "role": role,
                        "type": type_mapping.get(agent_type, "custom"),
                        "nationality": nationality,
                        "background": background,
                        "expertise": [e.strip() for e in expertise.split(",") if e.strip()],
                        "personality": [p.strip() for p in personality.split(",") if p.strip()],
                        "communication_style": communication_style,
                        "core_values": core_values,
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    # 保存到本地文件
                    try:
                        from agent_storage import save_custom_agent
                        if save_custom_agent(agent_id, new_agent):
                            st.session_state.custom_agents[agent_id] = new_agent
                            st.success(f"✅ 智能体 '{name}' 创建成功并已保存到本地！")
                        else:
                            st.session_state.custom_agents[agent_id] = new_agent
                            st.error("❌ 保存到本地文件失败，但已添加到当前会话")
                    except ImportError:
                        # 备用方案：只保存到session state
                        st.session_state.custom_agents[agent_id] = new_agent
                        st.warning("⚠️ 本地存储功能未加载，智能体仅保存到当前会话")

                    st.balloons()

                    # 显示创建的智能体信息（不使用嵌套expander）
                    st.markdown(f"### 🆕 新创建的智能体: {name}")

                    col1, col2 = st.columns(2)
                    with col1:
                        st.info(f"**角色**: {role}")
                        st.info(f"**类型**: {agent_type}")
                        st.info(f"**国籍**: {nationality}")

                    with col2:
                        if background:
                            st.info(f"**背景**: {background}")
                        if new_agent['expertise']:
                            st.info(f"**专业领域**: {', '.join(new_agent['expertise'])}")
                        if new_agent['personality']:
                            st.info(f"**性格特点**: {', '.join(new_agent['personality'])}")

                    st.rerun()
                else:
                    st.error("请至少填写智能体名称和角色定位")

def show_scenarios_page(demo_scenarios):
    """显示场景页面"""

    st.header("📋 推演场景")

    # 初始化自定义场景（从本地文件加载）
    if 'custom_scenarios' not in st.session_state:
        try:
            from data_storage import load_custom_scenarios
            st.session_state.custom_scenarios = load_custom_scenarios()
            if st.session_state.custom_scenarios:
                print(f"✅ 已从本地加载 {len(st.session_state.custom_scenarios)} 个自定义场景")
        except ImportError:
            st.session_state.custom_scenarios = {}
            print("⚠️ 数据存储模块未加载，使用临时存储")

    # 合并预设和自定义场景
    all_scenarios = {**demo_scenarios, **st.session_state.custom_scenarios}

    st.markdown("### 📚 所有场景")

    for scenario_id, scenario in all_scenarios.items():
        # 区分预设和自定义场景
        is_custom = scenario_id in st.session_state.custom_scenarios
        icon = "🆕" if is_custom else "📋"

        with st.expander(f"{icon} {scenario['title']} {'(自定义)' if is_custom else '(预设)'}"):
            st.write(f"**描述**: {scenario['description']}")
            st.write(f"**背景**: {scenario['context']}")
            st.write("**关键考虑因素**:")
            for consideration in scenario['considerations']:
                st.write(f"  • {consideration}")

            # 自定义场景可以删除
            if is_custom:
                if st.button(f"🗑️ 删除场景", key=f"delete_{scenario_id}"):
                    del st.session_state.custom_scenarios[scenario_id]
                    st.success(f"✅ 场景 '{scenario['title']}' 已删除")
                    st.rerun()

    st.markdown("### 🆕 创建自定义场景")

    with st.expander("创建新场景", expanded=False):
        with st.form("create_scenario_form"):
            title = st.text_input("场景标题", placeholder="例如：南海争端应对策略")
            description = st.text_area("场景描述", placeholder="简要描述推演场景的核心内容")
            context = st.text_area("背景情况", placeholder="详细描述当前形势和关键信息")

            # 关键考虑因素
            st.write("**关键考虑因素**:")
            considerations = []
            for i in range(4):
                consideration = st.text_input(f"考虑因素 {i+1}", key=f"consideration_{i}",
                                            placeholder=f"例如：{'军事选项' if i==0 else '经济影响' if i==1 else '盟友关系' if i==2 else '国内政治'}")
                if consideration:
                    considerations.append(consideration)

            # 场景类型
            scenario_type = st.selectbox("场景类型", ["军事冲突", "贸易争端", "外交危机", "国内政策", "国际合作", "危机管理", "其他"])

            # 预估时长
            estimated_duration = st.slider("预估讨论时长(分钟)", 15, 120, 60)

            # 难度等级
            difficulty_level = st.selectbox("推演难度", ["简单", "中等", "困难", "极难"])

            # 参与建议
            suggested_agents = st.multiselect("建议参与智能体",
                ["特朗普", "拜登", "军事专家", "政治专家", "经济专家", "导调员"],
                help="选择最适合此场景的智能体")

            submitted = st.form_submit_button("🚀 创建场景", type="primary")

            if submitted:
                if title and description and context:
                    # 生成场景ID
                    scenario_id = f"custom_{len(st.session_state.custom_scenarios) + 1}"

                    # 创建场景
                    new_scenario = {
                        "title": title,
                        "description": description,
                        "context": context,
                        "considerations": considerations,
                        "type": scenario_type,
                        "estimated_duration": estimated_duration,
                        "difficulty_level": difficulty_level,
                        "suggested_agents": suggested_agents,
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    # 保存到本地文件
                    try:
                        from data_storage import save_custom_scenario
                        if save_custom_scenario(scenario_id, new_scenario):
                            st.session_state.custom_scenarios[scenario_id] = new_scenario
                            st.success(f"✅ 场景 '{title}' 创建成功并已保存到本地！")
                        else:
                            st.session_state.custom_scenarios[scenario_id] = new_scenario
                            st.error("❌ 保存到本地文件失败，但已添加到当前会话")
                    except ImportError:
                        st.session_state.custom_scenarios[scenario_id] = new_scenario
                        st.warning("⚠️ 本地存储功能未加载，场景仅保存到当前会话")

                    st.balloons()

                    # 显示创建的场景信息（不使用嵌套expander）
                    st.markdown(f"### 🆕 新创建的场景: {title}")
                    st.info(f"**描述**: {description}")
                    st.info(f"**背景**: {context}")

                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**类型**: {scenario_type}")
                        st.write(f"**预估时长**: {estimated_duration}分钟")

                    with col2:
                        if considerations:
                            st.write("**关键考虑因素**:")
                            for consideration in considerations:
                                st.write(f"  • {consideration}")

                    st.rerun()
                else:
                    st.error("请填写场景标题、描述和背景情况")

def show_simulation_page(demo_agents, demo_scenarios):
    """显示仿真页面"""

    # 首先初始化所有必要的session_state
    if 'simulation_messages' not in st.session_state:
        st.session_state.simulation_messages = []

    if 'current_round' not in st.session_state:
        st.session_state.current_round = 0

    if 'auto_mode' not in st.session_state:
        st.session_state.auto_mode = False

    if 'simulation_phase' not in st.session_state:
        st.session_state.simulation_phase = "setup"

    if 'max_discussion_rounds' not in st.session_state:
        st.session_state.max_discussion_rounds = 20

    if 'last_auto_speak_time' not in st.session_state:
        st.session_state.last_auto_speak_time = 0

    st.header("🚀 开始战略推演")
    
    # 场景选择
    st.subheader("📋 选择推演场景")

    # 合并预设和自定义场景
    if 'custom_scenarios' not in st.session_state:
        st.session_state.custom_scenarios = {}

    all_scenarios = {**demo_scenarios, **st.session_state.custom_scenarios}
    scenario_options = {}

    for k, s in all_scenarios.items():
        is_custom = k in st.session_state.custom_scenarios
        label = f"{s['title']} {'(自定义)' if is_custom else '(预设)'}"
        scenario_options[label] = k

    selected_scenario_label = st.selectbox("推演场景", list(scenario_options.keys()))
    selected_scenario_key = scenario_options[selected_scenario_label]
    
    if selected_scenario_label:
        scenario = all_scenarios[selected_scenario_key]

        st.info(f"**场景**: {scenario['description']}")
        st.write(f"**背景**: {scenario['context']}")

        # 显示场景详细信息
        if 'considerations' in scenario and scenario['considerations']:
            st.markdown("**📋 关键考虑因素:**")
            for consideration in scenario['considerations']:
                st.write(f"• {consideration}")

        if 'estimated_duration' in scenario:
            st.caption(f"⏱️ 预估讨论时长: {scenario['estimated_duration']}分钟")
    
    # 智能体选择
    st.subheader("🤖 选择参与推演的智能体")

    # 合并预设和自定义智能体
    if 'custom_agents' not in st.session_state:
        st.session_state.custom_agents = {}

    all_agents = {**demo_agents, **st.session_state.custom_agents}

    # 智能体选择和知识库配置
    col1, col2 = st.columns([3, 2])

    with col1:
        st.write("**可选智能体**:")
        selected_agents = []

        for agent_id, agent in all_agents.items():
            is_custom = agent_id in st.session_state.custom_agents
            label = f"{agent['name']} ({agent['role']}) {'🆕' if is_custom else ''}"
            if st.checkbox(label, key=f"select_{agent_id}"):
                selected_agents.append(agent_id)

    with col2:
        # 知识库配置面板
        st.write("**📚 智能体知识库**:")

        # 导入知识库UI组件
        try:
            from knowledge_ui import show_agent_knowledge_panel, get_agent_knowledge_context

            # 显示选中智能体的知识库状态
            if selected_agents:
                # 只显示预设智能体的知识库（用户智能体暂不支持）
                preset_agents = [aid for aid in selected_agents if aid in demo_agents]

                if preset_agents:
                    st.info(f"💡 为{len(preset_agents)}个智能体配置专属知识库")

                    # 快速知识库状态
                    from knowledge_base import get_knowledge_base
                    kb = get_knowledge_base()

                    for agent_id in preset_agents[:3]:  # 最多显示3个
                        agent_name = demo_agents[agent_id]['name']
                        files = kb.get_agent_knowledge(agent_id)
                        status_icon = "🟢" if files else "⚪"
                        st.write(f"{status_icon} {agent_name}: {len(files)}个文件")

                    if st.button("🔧 管理知识库", key="manage_kb"):
                        st.session_state.show_knowledge_page = True
                        st.rerun()
                else:
                    st.info("💡 选择预设智能体以配置知识库")
            else:
                st.info("💡 先选择智能体，再配置知识库")

        except ImportError:
            st.warning("⚠️ 知识库功能未加载")

        # 添加用户智能体选项
        st.markdown("**👤 添加用户控制的智能体:**")

        # 初始化用户智能体列表
        if 'user_agents' not in st.session_state:
            st.session_state.user_agents = []

        # 显示已添加的用户智能体
        for user_agent in st.session_state.user_agents:
            agent_id = f"user_{user_agent.replace(' ', '_')}"
            if st.checkbox(f"👤 {user_agent} (用户控制)", key=f"select_{agent_id}"):
                selected_agents.append(agent_id)
                # 添加到all_agents中以便后续使用
                all_agents[agent_id] = {
                    "name": user_agent,
                    "role": f"用户控制 - {user_agent}",
                    "type": "user_controlled"
                }

        # 添加新用户智能体
        new_user_agent = st.text_input("添加新的用户智能体:", placeholder="例如: 张三, 李四, 王五")
        if st.button("➕ 添加用户智能体"):
            if new_user_agent and new_user_agent not in st.session_state.user_agents:
                st.session_state.user_agents.append(new_user_agent)
                st.success(f"✅ 已添加用户智能体: {new_user_agent}")
                st.rerun()
            elif new_user_agent in st.session_state.user_agents:
                st.warning("⚠️ 该智能体已存在")
            else:
                st.warning("⚠️ 请输入智能体名称")
    
    with col2:
        if selected_agents:
            st.write("**发言顺序配置**:")

            turn_order = st.selectbox(
                "发言模式",
                ["轮流发言", "导调控制", "自由讨论", "自定义顺序"],
                help="选择智能体的发言顺序模式"
            )
            
            if turn_order == "自定义顺序":
                st.write("**自定义发言顺序**:")

                # 初始化自定义顺序
                if 'custom_order' not in st.session_state:
                    st.session_state.custom_order = selected_agents.copy()

                # 确保自定义顺序包含所有选中的智能体
                if set(st.session_state.custom_order) != set(selected_agents):
                    # 添加新选中的智能体
                    for agent in selected_agents:
                        if agent not in st.session_state.custom_order:
                            st.session_state.custom_order.append(agent)
                    # 移除未选中的智能体
                    st.session_state.custom_order = [
                        agent for agent in st.session_state.custom_order
                        if agent in selected_agents
                    ]

                # 显示当前顺序并提供调整功能
                st.info("💡 使用下方按钮调整发言顺序")

                # 显示当前顺序
                for i, agent_id in enumerate(st.session_state.custom_order):
                    agent_name = all_agents[agent_id]['name']

                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        st.write(f"**{i+1}. {agent_name}**")

                    with col2:
                        if st.button("⬆️", key=f"up_{agent_id}", help="上移"):
                            if i > 0:
                                # 交换位置
                                st.session_state.custom_order[i], st.session_state.custom_order[i-1] = \
                                    st.session_state.custom_order[i-1], st.session_state.custom_order[i]
                                st.rerun()

                    with col3:
                        if st.button("⬇️", key=f"down_{agent_id}", help="下移"):
                            if i < len(st.session_state.custom_order) - 1:
                                # 交换位置
                                st.session_state.custom_order[i], st.session_state.custom_order[i+1] = \
                                    st.session_state.custom_order[i+1], st.session_state.custom_order[i]
                                st.rerun()

                    with col4:
                        if st.button("🔝", key=f"top_{agent_id}", help="置顶"):
                            # 移动到顶部
                            agent = st.session_state.custom_order.pop(i)
                            st.session_state.custom_order.insert(0, agent)
                            st.rerun()

                # 快速排序选项
                st.markdown("**🚀 快速排序**:")
                col1, col2, col3 = st.columns(3)

                with col1:
                    if st.button("🎯 重要性排序", help="按重要性排序：总统 → 专家 → 其他"):
                        # 按重要性排序
                        priority_order = ["trump", "biden", "moderator", "military_expert", "political_expert"]
                        sorted_agents = []

                        # 先添加优先级高的
                        for priority_agent in priority_order:
                            if priority_agent in st.session_state.custom_order:
                                sorted_agents.append(priority_agent)

                        # 再添加其他的
                        for agent in st.session_state.custom_order:
                            if agent not in sorted_agents:
                                sorted_agents.append(agent)

                        st.session_state.custom_order = sorted_agents
                        st.rerun()

                with col2:
                    if st.button("🔀 随机排序", help="随机打乱发言顺序"):
                        import random
                        random.shuffle(st.session_state.custom_order)
                        st.rerun()

                with col3:
                    if st.button("🔄 重置顺序", help="重置为默认顺序"):
                        st.session_state.custom_order = selected_agents.copy()
                        st.rerun()

                # 更新selected_agents为自定义顺序
                selected_agents = st.session_state.custom_order.copy()
    
    # 推演设置
    st.subheader("⚙️ 推演参数设置")

    col1, col2 = st.columns(2)

    with col1:
        max_rounds = st.slider("每个智能体发言轮数", 1, 10, 4, help="每个智能体发言的轮数（总发言次数 = 智能体数量 × 轮数）")
        enable_user = st.checkbox("允许用户参与讨论", value=True, help="用户可以实时参与推演讨论")

    with col2:
        round_time = st.slider("单轮发言时间限制(分钟)", 1, 15, 5, help="每个智能体单次发言的时间限制")
        auto_save = st.checkbox("自动保存推演记录", value=True, help="自动保存推演过程和结果")

    # 上下文管理监控
    st.markdown("### 🧠 上下文管理")
    col1, col2 = st.columns(2)

    with col1:
        show_context_stats = st.checkbox("📊 显示上下文统计", value=False, help="显示token使用情况")
        st.session_state.show_context_debug = st.checkbox("🔍 调试模式", value=False, help="显示详细的上下文管理信息")

    with col2:
        if show_context_stats and hasattr(st.session_state, 'simulation_messages') and st.session_state.simulation_messages:
            try:
                # 优先尝试完整版
                from context_manager import get_context_statistics
                stats = get_context_statistics(st.session_state.simulation_messages)

            except ImportError:
                try:
                    # 备用简化版
                    from simple_context_manager import get_simple_context_statistics
                    stats = get_simple_context_statistics(st.session_state.simulation_messages)

                except ImportError:
                    st.warning("⚠️ 上下文管理器未加载")
                    stats = None

            if stats:
                st.metric("总消息数", stats.get("total_messages", 0))
                ratio = stats.get("estimated_context_ratio", 0)
                st.metric("上下文使用率", f"{ratio:.1%}")

                if ratio > 0.8:
                    st.warning("⚠️ 上下文接近限制，将启用智能压缩")
                elif ratio > 0.6:
                    st.info("ℹ️ 上下文使用较多")
                else:
                    st.success("✅ 上下文正常")

    # 启动推演
    st.subheader("🎬 启动战略推演")

    # 检查是否已经开始推演
    if 'simulation_started' not in st.session_state:
        st.session_state.simulation_started = False

    if not st.session_state.simulation_started:
        # 显示启动按钮
        if st.button("🚀 开始推演", type="primary", use_container_width=True):
            if not selected_agents:
                st.error("❌ 请至少选择一个智能体参与推演")
            else:
                st.success("✅ 推演配置完成，准备开始！")

                # 保存推演配置到session state
                # 计算实际的最大发言次数
                actual_max_rounds = len(selected_agents) * max_rounds

                # 保存自定义顺序（如果有的话）
                final_agent_order = selected_agents
                if turn_order == "自定义顺序" and 'custom_order' in st.session_state:
                    final_agent_order = st.session_state.custom_order.copy()

                    # 保存自定义顺序到本地
                    try:
                        from data_storage import get_data_storage
                        storage = get_data_storage()
                        order_id = f"order_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        storage.save_custom_order(order_id, final_agent_order)
                    except ImportError:
                        pass

                st.session_state.simulation_config = {
                    "scenario": scenario,
                    "selected_agents": final_agent_order,  # 使用最终的智能体顺序
                    "original_agents": selected_agents,  # 保存原始选择
                    "all_agents": all_agents,
                    "turn_order": turn_order,
                    "max_rounds": max_rounds,  # 每个智能体的轮数
                    "actual_max_rounds": actual_max_rounds,  # 实际总发言次数
                    "round_time": round_time,
                    "enable_user": enable_user
                }

                # 更新session state中的最大轮数
                st.session_state.max_discussion_rounds = actual_max_rounds

                # 显示配置摘要
                with st.expander("📋 推演配置摘要", expanded=True):
                    st.write(f"**推演场景**: {scenario['title']}")
                    st.write(f"**参与智能体**: {', '.join([all_agents[a]['name'] for a in final_agent_order])}")
                    st.write(f"**发言模式**: {turn_order}")

                    # 显示发言顺序
                    if turn_order == "自定义顺序":
                        st.write("**发言顺序**:")
                        for i, agent_id in enumerate(final_agent_order, 1):
                            agent_name = all_agents[agent_id]['name']
                            st.write(f"  {i}. {agent_name}")

                    total_speeches = len(final_agent_order) * max_rounds
                    st.write(f"**每个智能体发言轮数**: {max_rounds} 轮")
                    st.write(f"**预计总发言次数**: {total_speeches} 次")
                    st.write(f"**单轮时间限制**: {round_time} 分钟")
                    st.write(f"**用户参与**: {'是' if enable_user else '否'}")

                # 标记推演已开始
                st.session_state.simulation_started = True
                st.rerun()

    # 如果推演已开始，显示推演界面
    if st.session_state.simulation_started and 'simulation_config' in st.session_state:
        config = st.session_state.simulation_config
        show_demo_simulation(config["selected_agents"], config["all_agents"], config["scenario"])

def show_demo_simulation(selected_agents, demo_agents, scenario):
    """显示AI推演过程"""

    st.markdown("### 🎭 AI战略推演进行中")

    # 添加简化的CSS样式
    st.markdown("""
    <style>
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    .streaming-cursor {
        animation: blink 1s infinite;
        color: #666;
    }
    </style>
    """, unsafe_allow_html=True)

    # 显示调试信息
    with st.expander("🔧 调试信息", expanded=False):
        st.write(f"AI功能状态: {'✅ 可用' if generate_response else '❌ 不可用'}")
        st.write(f"选择的智能体: {selected_agents}")
        st.write(f"可用智能体: {list(demo_agents.keys())}")
        if llm_config:
            st.write(f"API配置: {llm_config.is_configured()}")

    # session_state已在函数开头初始化

    # 更新一些默认值（如果需要）
    if 'decision_maker' not in st.session_state:
        st.session_state.decision_maker = "trump"  # 特朗普做最终决策

    # 显示推演状态和阶段
    phase_names = {
        "discussion": "🗣️ 讨论阶段",
        "summary": "📋 总结阶段",
        "decision": "⚖️ 决策阶段",
        "completed": "✅ 推演完成"
    }

    current_phase_name = phase_names.get(st.session_state.simulation_phase, "🗣️ 讨论阶段")

    if st.session_state.simulation_messages:
        col1, col2 = st.columns(2)
        with col1:
            st.info(f"📊 当前进度：第 {st.session_state.current_round} 轮发言，共 {len(st.session_state.simulation_messages)} 条发言记录")
        with col2:
            st.info(f"🎯 当前阶段：{current_phase_name}")

        # 讨论阶段进度条
        if st.session_state.simulation_phase == "discussion":
            progress = min(st.session_state.current_round / st.session_state.max_discussion_rounds, 1.0)

            # 计算每个智能体的平均发言次数
            if 'simulation_config' in st.session_state:
                config = st.session_state.simulation_config
                agent_count = len(config.get("selected_agents", []))
                rounds_per_agent = config.get("max_rounds", 4)
                current_avg_rounds = st.session_state.current_round / agent_count if agent_count > 0 else 0

                st.progress(progress, text=f"讨论进度: {st.session_state.current_round}/{st.session_state.max_discussion_rounds} 次发言")
                st.info(f"📊 平均每个智能体已发言: {current_avg_rounds:.1f}/{rounds_per_agent} 轮")
            else:
                st.progress(progress, text=f"讨论进度: {st.session_state.current_round}/{st.session_state.max_discussion_rounds}")

            if st.session_state.current_round >= st.session_state.max_discussion_rounds:
                st.warning("🎯 讨论轮次已满，准备进入总结阶段")
    else:
        st.info("🎬 点击'开始推演'按钮开始AI对话")

    # 显示已有消息
    for msg in st.session_state.simulation_messages:
        # 处理不同类型的发言者
        if msg["speaker"] in demo_agents:
            agent_name = demo_agents[msg["speaker"]]["name"]
            avatar = msg["speaker"]
        elif msg["speaker"] == "user":
            agent_name = "用户"
            avatar = "user"
        elif msg["speaker"] == "ai_expert":
            agent_name = "AI专家"
            avatar = "assistant"
        elif msg["speaker"].startswith("user_"):
            # 用户自定义智能体
            agent_name = msg["speaker"].replace("user_", "").replace("_", " ")
            avatar = "user"
        else:
            agent_name = msg["speaker"]
            avatar = "assistant"

        with st.chat_message(avatar):
            st.write(f"**{agent_name}** - {msg['timestamp']}")
            st.write(msg["content"])

    # 推演设置
    col_set1, col_set2 = st.columns(2)
    with col_set1:
        enable_stream = st.checkbox("🌊 启用流式输出", value=True, help="AI回复时逐词显示，更有真实感")
    with col_set2:
        auto_continue = st.checkbox("🔄 自动轮流发言", value=False, help="AI智能体自动轮流发言，用户智能体会暂停等待输入")

    # 流式输出设置
    if enable_stream:
        st.caption("💡 流式输出已启用，AI回复时会逐词显示")

    # 自动轮流发言处理（仅在讨论阶段）
    if auto_continue and len(st.session_state.simulation_messages) > 0 and st.session_state.simulation_phase == "discussion":
        st.info("🔄 自动轮流发言模式已启用")

        # 检查是否到达最大轮数
        if st.session_state.current_round >= st.session_state.max_discussion_rounds:
            st.warning("🎯 讨论轮次已满，将自动进入总结阶段")
            st.session_state.simulation_phase = "summary"
            st.rerun()
        else:
            # 获取正确的智能体顺序（支持自定义顺序）
            final_agents = selected_agents
            if 'simulation_config' in st.session_state:
                config_agents = st.session_state.simulation_config.get("selected_agents", selected_agents)
                if config_agents:
                    final_agents = config_agents
                    # 调试信息
                    if st.session_state.get('show_context_debug', False):
                        st.info(f"🔄 使用自定义顺序: {[demo_agents.get(a, {}).get('name', a) for a in final_agents]}")

            # 确定当前发言者
            if final_agents:
                current_speaker_index = st.session_state.current_round % len(final_agents)
                current_speaker = final_agents[current_speaker_index]

                # 跳过导调（除了第一次）
                if current_speaker == "moderator" and st.session_state.current_round > 0:
                    st.session_state.current_round += 1
                    current_speaker_index = st.session_state.current_round % len(final_agents)
                    current_speaker = final_agents[current_speaker_index]

                if current_speaker in demo_agents:
                    next_speaker_name = demo_agents[current_speaker]["name"]
                elif current_speaker.startswith("user_"):
                    next_speaker_name = current_speaker.replace("user_", "").replace("_", " ")
                else:
                    next_speaker_name = current_speaker

                st.write(f"👉 下一位发言: **{next_speaker_name}**")

                # 检查是否是用户智能体
                if current_speaker.startswith("user_"):
                    # 用户智能体，暂停自动模式，显示输入界面
                    st.warning(f"⏸️ 轮到用户智能体 **{next_speaker_name}**，自动模式暂停")
                    # 调用用户输入处理函数
                    handle_user_agent_input(current_speaker, demo_agents, scenario)
                else:
                    # AI智能体，自动发言
                    st.success(f"🤖 AI智能体 **{next_speaker_name}** 将在3秒后自动发言")

                    # 初始化自动发言时间
                    if 'last_auto_speak_time' not in st.session_state:
                        st.session_state.last_auto_speak_time = 0

                    import time
                    current_time = time.time()

                    # 检查是否到了自动发言时间
                    if current_time - st.session_state.last_auto_speak_time >= 3:
                        st.session_state.last_auto_speak_time = current_time
                        # 自动执行下一轮发言
                        next_round_simulation(selected_agents, demo_agents, scenario, use_stream=enable_stream)
                        st.rerun()
                    else:
                        # 显示倒计时
                        remaining = 3 - (current_time - st.session_state.last_auto_speak_time)
                        st.info(f"⏱️ {remaining:.1f}秒后自动发言...")
                        time.sleep(0.5)
                        st.rerun()

    # 控制按钮区域
    st.markdown("---")

    # 根据阶段显示不同的控制按钮
    if st.session_state.simulation_phase == "discussion":
        # 讨论阶段按钮
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🎬 开始推演", disabled=len(st.session_state.simulation_messages) > 0, use_container_width=True):
                start_simulation(selected_agents, demo_agents, scenario, enable_stream)
                st.rerun()

        with col2:
            if st.button("➡️ 下一轮发言", disabled=len(st.session_state.simulation_messages) == 0, use_container_width=True):
                next_round_simulation(selected_agents, demo_agents, scenario, use_stream=enable_stream)
                st.rerun()

        with col3:
            if st.button("📋 进入总结", disabled=st.session_state.current_round < 3, use_container_width=True):
                st.session_state.simulation_phase = "summary"
                st.rerun()

    elif st.session_state.simulation_phase == "summary":
        # 总结阶段按钮
        col1, col2 = st.columns(2)

        with col1:
            if st.button("📋 生成总结", use_container_width=True):
                generate_discussion_summary(selected_agents, demo_agents, scenario)
                st.rerun()

        with col2:
            if st.button("⚖️ 进入决策", use_container_width=True):
                st.session_state.simulation_phase = "decision"
                st.rerun()

    elif st.session_state.simulation_phase == "decision":
        # 决策阶段按钮
        col1, col2 = st.columns(2)

        with col1:
            if st.button("⚖️ 特朗普最终决策", use_container_width=True):
                generate_final_decision(selected_agents, demo_agents, scenario)
                st.rerun()

        with col2:
            if st.button("🔄 重新开始", use_container_width=True):
                reset_simulation()
                st.rerun()

    else:
        # 完成阶段或重置按钮
        if st.button("🔄 重新开始推演", use_container_width=True):
            reset_simulation()
            st.rerun()

    # 控制按钮区域结束

    # 添加返回配置按钮
    if st.button("⬅️ 返回推演配置"):
        st.session_state.simulation_started = False
        if 'simulation_config' in st.session_state:
            del st.session_state.simulation_config
        st.rerun()

def start_simulation(selected_agents, demo_agents, scenario, use_stream=True):
    """开始推演"""

    print(f"🎬 开始推演: {scenario['title']}")
    print(f"参与智能体: {selected_agents}")
    print(f"流式输出: {use_stream}")

    # 设置推演状态
    st.session_state.simulation_started = True
    st.session_state.current_scenario = scenario
    st.session_state.current_agents = selected_agents

    # 导调开场
    if use_stream:
        # 使用流式输出
        with st.chat_message("moderator"):
            moderator_message = stream_agent_response_simple(
                "moderator",
                demo_agents.get("moderator", demo_agents[list(demo_agents.keys())[0]]),
                scenario,
                f"作为推演主持人，请介绍推演场景：{scenario['title']}，并说明讨论的重点。",
                []
            )
    else:
        # 普通输出
        moderator_message = generate_agent_response(
            "moderator",
            demo_agents.get("moderator", demo_agents[list(demo_agents.keys())[0]]),
            scenario,
            f"作为推演主持人，请介绍推演场景：{scenario['title']}，并说明讨论的重点。",
            [],
            stream=False
        )

        # 使用chat_message显示
        with st.chat_message("moderator"):
            st.write(f"**推演导调** - {datetime.now().strftime('%H:%M:%S')}")
            st.write(moderator_message)

    print(f"导调消息: {moderator_message}")
    add_message("moderator", moderator_message)

def next_round_simulation(selected_agents, demo_agents, scenario, use_stream=True):
    """下一轮发言"""

    # 优先使用配置中的智能体顺序（支持自定义顺序）
    if 'simulation_config' in st.session_state:
        config_agents = st.session_state.simulation_config.get("selected_agents", selected_agents)
        if config_agents:
            selected_agents = config_agents
            print(f"🔄 使用配置中的智能体顺序: {[demo_agents.get(a, {}).get('name', a) for a in selected_agents]}")

    if not selected_agents:
        print("❌ 没有选择的智能体")
        return

    # 检查是否应该进入总结阶段
    if st.session_state.current_round >= st.session_state.max_discussion_rounds:
        st.session_state.simulation_phase = "summary"
        st.info("🎯 讨论轮次已满，自动进入总结阶段")
        return

    # 确定下一个发言者
    current_round = st.session_state.current_round
    speaker_id = selected_agents[current_round % len(selected_agents)]

    print(f"🎤 第{current_round + 1}轮发言: {speaker_id}")

    # 跳过导调（已经在开场发言）
    if speaker_id == "moderator" and current_round > 0:
        st.session_state.current_round += 1
        speaker_id = selected_agents[st.session_state.current_round % len(selected_agents)]
        print(f"跳过导调，改为: {speaker_id}")

    # 检查是否是用户自定义智能体
    if speaker_id.startswith("user_"):
        # 用户自定义智能体，需要用户输入
        handle_user_agent_input(speaker_id, demo_agents, scenario)
        return

    # 检查智能体是否存在
    if speaker_id not in demo_agents:
        print(f"❌ 智能体 {speaker_id} 不存在")
        return

    # 生成发言
    context = get_conversation_context()

    if use_stream:
        # 使用流式输出
        with st.chat_message(speaker_id):
            agent_response = stream_agent_response_simple(
                speaker_id,
                demo_agents[speaker_id],
                scenario,
                f"基于当前讨论，请从{demo_agents[speaker_id]['role']}的角度分析{scenario['title']}情况并提出建议。",
                context
            )
    else:
        # 普通输出
        agent_response = generate_agent_response(
            speaker_id,
            demo_agents[speaker_id],
            scenario,
            f"基于当前讨论，请从{demo_agents[speaker_id]['role']}的角度分析{scenario['title']}情况并提出建议。",
            context,
            stream=False
        )

        # 即使是普通输出也显示在聊天框中
        with st.chat_message(speaker_id):
            st.write(f"**{demo_agents[speaker_id]['name']}** - {datetime.now().strftime('%H:%M:%S')}")
            st.write(agent_response)

    print(f"智能体回应: {agent_response[:100]}...")
    add_message(speaker_id, agent_response)
    st.session_state.current_round += 1

def generate_agent_response(agent_id, agent_info, scenario, prompt, context, stream=False):
    """生成智能体回应"""

    # 调试信息
    print(f"🤖 生成 {agent_info['name']} 的回应...")
    print(f"generate_response 函数状态: {generate_response is not None}")
    print(f"流式输出: {stream}")

    if not generate_response:
        print("⚠️ AI功能不可用，返回演示内容")
        return f"[演示模式] 我是{agent_info['name']}，{agent_info['role']}。针对{scenario['title']}，我认为需要仔细分析当前形势，制定合适的应对策略。"

    # 构建递进式智囊团研讨提示系统
    def get_agent_prompt_by_round(agent_id, round_number, total_rounds):
        """根据轮次获取智能体提示"""

        # 尝试使用深度人物克隆系统
        try:
            from character_cloning import get_deep_character_prompt

            # 为特朗普使用30维度深度克隆
            if agent_id == "trump":
                context = f"当前是第{round_number}轮讨论，共{total_rounds}轮。场景：{scenario.get('title', '未知场景')}"
                return get_deep_character_prompt("trump", context)
        except ImportError:
            pass

        # 备用方案：原有的简化配置
        base_profiles = {
            "trump": {
                "identity": "唐纳德·特朗普，美国第47任总统，现任最高决策者",
                "style": "直接、强硬、商人思维、美国优先",
                "knowledge": "商业谈判、美国政治、国际贸易、军事威慑",
                "perspective": "美国国家利益、实力政治、成本效益分析"
            },
            "biden": {
                "identity": "乔·拜登，美国第46任总统，前任总统",
                "style": "温和、外交导向、重视盟友、经验丰富",
                "knowledge": "外交政策、国际关系、多边合作、危机管理",
                "perspective": "国际合作、外交解决、盟友协调、长期稳定"
            },
            "military_expert": {
                "identity": "五角大楼高级军事顾问，在役将军",
                "style": "专业、客观、数据驱动、风险评估",
                "knowledge": "军事战略、作战计划、武器系统、地缘军事",
                "perspective": "军事可行性、战略风险、作战能力、威慑效果"
            },
            "political_expert": {
                "identity": "国际关系专家，政治学教授",
                "style": "学术、深度、理论结合实践、长远思考",
                "knowledge": "国际法、地缘政治、历史案例、政治理论",
                "perspective": "国际法框架、政治后果、历史借鉴、战略影响"
            }
        }

        profile = base_profiles.get(agent_id, base_profiles["political_expert"])

        # 根据轮次设计不同的研讨重点
        round_focuses = {
            1: {
                "focus": "初步分析和立场阐述",
                "instruction": "请从你的专业角度进行初步分析，阐述基本立场和核心观点"
            },
            2: {
                "focus": "深入分析和方案提出",
                "instruction": "基于第一轮讨论，深入分析具体问题，提出详细的解决方案"
            },
            3: {
                "focus": "风险评估和可行性分析",
                "instruction": "评估各方案的风险和可行性，指出潜在问题和改进建议"
            },
            4: {
                "focus": "综合权衡和最终建议",
                "instruction": "综合考虑所有因素，提出最终建议和实施路径"
            }
        }

        # 确定当前轮次重点
        current_round = min(round_number, 4)
        round_info = round_focuses.get(current_round, round_focuses[4])

        return f"""你是{profile['identity']}，正在参与{scenario.get('title', '战略决策')}的高级智囊团研讨。

【你的专业背景】
- 身份定位：{profile['identity']}
- 表达风格：{profile['style']}
- 知识领域：{profile['knowledge']}
- 分析视角：{profile['perspective']}

【当前轮次】第{round_number}轮/{total_rounds}轮
【本轮重点】{round_info['focus']}
【具体要求】{round_info['instruction']}

【研讨要求】
1. 必须基于完整的讨论历史进行分析
2. 从你的专业角度提供独特见解
3. 避免重复之前已经讨论过的内容
4. 与其他专家的观点进行建设性互动
5. 每轮都要有新的深度和维度

请确保你的发言具有递进性，不要重复之前的观点。"""

    # 动态生成智能体提示词
    def generate_agent_prompt(agent_id, scenario_title, scenario_context):
        """动态生成智能体提示词"""

        agent_prompts = {
            "trump": f"""你是唐纳德·特朗普，美国第47任总统，现任总统，智囊团最高决策者。

你的特点：
- 直接、强硬的表达方式，商人思维
- 强调"美国优先"，重视实力展示
- 会直接质疑或支持其他人的观点
- 倾向于强硬立场，但考虑成本效益

在关于"{scenario_title}"的智囊团讨论中，你要：
1. 明确回应其他成员的观点（赞同/反对）
2. 从美国利益角度质疑不合理建议
3. 提出强硬但实用的解决方案
4. 与其他观点进行直接辩论

背景情况：{scenario_context}

请积极参与辩论，不要只是重复立场。""",

            "biden": f"""你是乔·拜登，美国第46任总统，前任总统，智囊团资深顾问。

你的特点：
- 温和外交导向，重视盟友合作
- 经验丰富，善于平衡各方观点
- 会寻求妥协和共识
- 强调多边主义和国际法

在关于"{scenario_title}"的智囊团讨论中，你要：
1. 分析其他成员观点的优缺点
2. 提出平衡各方利益的方案
3. 强调外交途径和国际合作
4. 对过于激进的建议提出温和反对

背景情况：{scenario_context}

请积极参与辩论，寻求各方平衡。""",

            "moderator": f"""你是推演主持人，专业的战略分析师。

在关于"{scenario_title}"的智囊团辩论中，你的职责：
- 客观总结各方观点和分歧
- 指出讨论中的盲点和遗漏
- 提出深入的战略问题
- 推动讨论向更深层次发展
- 确保所有重要因素都被考虑

背景情况：{scenario_context}

请以专业主持人的角度参与讨论。""",

            "military_expert": f"""你是军事战略专家，五角大楼高级顾问。

你的特点：
- 专业军事分析，数据驱动
- 关注作战可行性和风险评估
- 会用专业知识质疑不现实的军事建议
- 提供具体的军事选项和后果分析

在关于"{scenario_title}"的智囊团讨论中，你要：
1. 从军事角度评估其他人的建议
2. 指出军事行动的风险和代价
3. 提供专业的作战能力分析
4. 对不切实际的军事方案提出质疑

背景情况：{scenario_context}

请用专业军事知识参与辩论。""",

            "political_expert": f"""你是政治学专家，资深分析师。

你的特点：
- 深入的政治分析
- 考虑国内外政治影响
- 平衡多方利益
- 学术性的表达
- 基于历史和理论的分析

在关于"{scenario_title}"的智囊团讨论中，你要：
1. 从政治学角度分析当前形势
2. 评估各种方案的政治可行性
3. 考虑国内外政治反应
4. 提供基于历史经验的建议

背景情况：{scenario_context}

请从政治专家角度分析。"""
        }

        return agent_prompts.get(agent_id, f"""你是{agent_id}，请根据当前讨论的"{scenario_title}"场景进行分析。

背景情况：{scenario_context}

请提供专业的分析和建议。""")

    # 定义标准智能体列表
    standard_agents = ["trump", "biden", "military_expert", "political_expert", "moderator"]

    # 如果是自定义智能体，构建个性化提示
    if agent_id not in standard_agents:
        custom_prompt = f"你是{agent_info['name']}，{agent_info['role']}。"

        if 'nationality' in agent_info and agent_info['nationality']:
            custom_prompt += f"你来自{agent_info['nationality']}。"

        if 'background' in agent_info and agent_info['background']:
            custom_prompt += f"你的背景：{agent_info['background']}。"

        if 'personality' in agent_info and agent_info['personality']:
            custom_prompt += f"你的性格特点：{', '.join(agent_info['personality'])}。"

        if 'communication_style' in agent_info and agent_info['communication_style']:
            custom_prompt += f"你的沟通风格：{agent_info['communication_style']}。"

        if 'core_values' in agent_info and agent_info['core_values']:
            custom_prompt += f"你的核心观点：{agent_info['core_values']}。"

        custom_prompt += "请保持角色特色，用中文回应。"
        system_prompt = custom_prompt
    else:
        # 计算当前智能体的轮次
        agent_round = 1
        total_agent_rounds = 4  # 默认4轮

        if 'simulation_config' in st.session_state:
            config = st.session_state.simulation_config
            selected_agents = config.get("selected_agents", [])
            max_rounds = config.get("max_rounds", 4)
            total_agent_rounds = max_rounds

            if agent_id in selected_agents:
                # 计算该智能体已经发言的次数
                agent_speeches = sum(1 for msg in st.session_state.get("simulation_messages", [])
                                   if msg.get("speaker") == agent_id)
                agent_round = agent_speeches + 1

        # 使用递进式提示
        system_prompt = get_agent_prompt_by_round(agent_id, agent_round, total_agent_rounds)

        # 如果递进式提示不可用，使用动态生成的提示
        if not system_prompt or "台海危机" in system_prompt:
            system_prompt = generate_agent_prompt(agent_id, scenario.get('title', '战略决策'), scenario.get('context', ''))

    # 构建智囊团辩论消息
    if context and len(context) > 0:
        # 构建完整的递进式讨论上下文
        full_discussion = "【完整讨论历史】\n"

        # 按发言顺序展示，但标注轮次
        speaker_rounds = {}
        for msg in context:
            speaker = msg["speaker"]
            if speaker not in speaker_rounds:
                speaker_rounds[speaker] = 0
            speaker_rounds[speaker] += 1

            full_discussion += f"• {speaker}(第{speaker_rounds[speaker]}轮): {msg['content']}\n"

        # 分析讨论进展
        discussion_analysis = "\n【讨论分析】\n"
        if len(context) >= 3:
            discussion_analysis += "• 已有多轮讨论，需要避免重复\n"
            discussion_analysis += "• 寻找新的分析维度和深度\n"
            discussion_analysis += "• 关注其他专家未涉及的角度\n"

        # 获取智能体专属知识库上下文
        knowledge_context = ""
        try:
            from knowledge_ui import get_agent_knowledge_context
            knowledge_context = get_agent_knowledge_context(agent_id, scenario['context'])
        except ImportError:
            pass

        user_content = f"""【智囊团递进式研讨】
场景：{scenario['title']}
背景：{scenario['context']}

{full_discussion}

{discussion_analysis}

{knowledge_context}

【你的任务】
基于完整讨论历史和专属知识库，请：
1. 深度分析已有观点的优缺点
2. 识别讨论中的盲点和遗漏
3. 从你的专业角度提供新维度
4. 避免重复已充分讨论的内容
5. 推动讨论向更深层次发展

请确保发言具有递进性和独特价值，用中文回应，控制在200字以内。"""
    else:
        # 首次发言
        user_content = f"""智囊团辩论场景：{scenario['title']}
背景：{scenario['context']}

作为智囊团成员，请分析当前形势并提出你的战略建议。这是开场发言，要为后续辩论奠定基础。

请求：{prompt}

请用中文回应，保持角色特色，控制在150字以内。"""

    try:
        # 优先尝试完整版上下文管理
        from context_manager import get_managed_context
        managed_messages, context_stats = get_managed_context(
            system_prompt,
            context if context else [],
            user_content
        )

    except ImportError:
        try:
            # 备用：简化版上下文管理
            from simple_context_manager import get_simple_managed_context
            managed_messages, context_stats = get_simple_managed_context(
                system_prompt,
                context if context else [],
                user_content
            )

        except ImportError:
            # 最终备用：原有逻辑
            managed_messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ]
            context_stats = {"strategy": "fallback"}

    # 显示上下文统计（如果启用调试）
    if 'show_context_debug' in st.session_state and st.session_state.show_context_debug:
        with st.sidebar:
            with st.expander("🔍 上下文统计", expanded=False):
                st.json(context_stats)

    try:
        response = generate_response(managed_messages, max_tokens=400, temperature=0.8)
        return response

    except Exception as e:
        return f"[AI生成失败] {agent_info['name']}的观点：{str(e)}"

def stream_agent_response_simple(agent_id, agent_info, scenario, prompt, context):
    """简化版流式显示智能体回应"""

    try:
        # 生成完整响应
        response = generate_agent_response(agent_id, agent_info, scenario, prompt, context, stream=True)

        if isinstance(response, str) and not response.startswith("Error") and not response.startswith("[AI生成失败]"):
            # 显示智能体名称和时间
            st.write(f"**{agent_info['name']}** - {datetime.now().strftime('%H:%M:%S')}")

            # 创建占位符用于流式显示
            placeholder = st.empty()
            displayed_text = ""

            # 按字符逐步显示
            import time
            for char in response:
                displayed_text += char
                # 更新显示
                placeholder.markdown(f"{displayed_text}<span class='streaming-cursor'>|</span>", unsafe_allow_html=True)
                time.sleep(0.03)  # 控制打字速度

            # 完成后显示最终结果（移除光标）
            placeholder.markdown(displayed_text)

            return displayed_text
        else:
            # 如果生成失败，直接显示错误
            st.error(f"{agent_info['name']} 回应生成失败: {response}")
            return response

    except Exception as e:
        error_msg = f"流式显示失败: {str(e)}"
        st.error(error_msg)
        return error_msg


def stream_agent_response(agent_id, agent_info, scenario, prompt, context):
    """流式显示智能体回应 - 兼容版本"""

    # 使用简化版本避免嵌套问题
    return stream_agent_response_simple(agent_id, agent_info, scenario, prompt, context)

def add_message(speaker, content):
    """添加消息到会话"""

    message = {
        "speaker": speaker,
        "content": content,
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }

    print(f"📝 添加消息: {speaker} - {content[:50]}...")
    st.session_state.simulation_messages.append(message)
    print(f"📊 当前消息总数: {len(st.session_state.simulation_messages)}")

    # 自动保存推演记录到本地
    try:
        from data_storage import get_data_storage
        storage = get_data_storage()

        # 生成推演ID（基于当前时间和场景）
        if 'current_simulation_id' not in st.session_state:
            scenario_name = st.session_state.get('simulation_config', {}).get('scenario', {}).get('title', 'unknown')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            st.session_state.current_simulation_id = f"{scenario_name}_{timestamp}".replace(' ', '_')

        # 保存推演数据
        simulation_data = {
            "scenario": st.session_state.get('simulation_config', {}).get('scenario', {}),
            "agents": st.session_state.get('simulation_config', {}).get('selected_agents', []),
            "messages": st.session_state.simulation_messages,
            "config": st.session_state.get('simulation_config', {}),
            "current_round": st.session_state.get('current_round', 0),
            "phase": st.session_state.get('simulation_phase', 'discussion')
        }

        storage.save_simulation(st.session_state.current_simulation_id, simulation_data)

    except ImportError:
        pass  # 静默失败，不影响主要功能

def handle_user_agent_input(speaker_id, demo_agents, scenario):
    """处理用户自定义智能体的输入"""

    agent_name = speaker_id.replace("user_", "").replace("_", " ")

    st.info(f"🎤 轮到 **{agent_name}** 发言")

    # 显示当前场景和上下文
    with st.expander("📋 当前讨论背景", expanded=False):
        st.write(f"**场景**: {scenario['title']}")
        st.write(f"**背景**: {scenario['context']}")

        recent_messages = get_conversation_context()
        if recent_messages:
            st.write("**最近的讨论**:")
            for msg in recent_messages[-3:]:
                # 直接使用speaker名称
                speaker_name = msg["speaker"]
                st.write(f"- **{speaker_name}**: {msg['content'][:100]}...")

    # 用户输入区域
    user_input_key = f"user_input_{speaker_id}_{st.session_state.current_round}"
    user_input = st.text_area(
        f"请以 {agent_name} 的身份发言:",
        placeholder=f"请输入 {agent_name} 对当前情况的分析和建议...",
        height=120,
        key=user_input_key
    )

    col1, col2 = st.columns(2)

    with col1:
        if st.button(f"📝 提交 {agent_name} 的发言", type="primary", key=f"submit_{speaker_id}_{st.session_state.current_round}"):
            if user_input.strip():
                # 添加用户发言
                add_message(speaker_id, user_input.strip())
                st.session_state.current_round += 1
                # 重置自动发言时间，让自动轮流继续
                st.session_state.last_auto_speak_time = time.time()
                st.success(f"✅ {agent_name} 的发言已提交，自动轮流继续")
                st.rerun()
            else:
                st.warning("⚠️ 请输入发言内容")

    with col2:
        if st.button(f"🤖 AI代为发言", key=f"ai_assist_{speaker_id}_{st.session_state.current_round}"):
            # AI代为生成发言
            context = get_conversation_context()

            # 创建临时智能体信息
            temp_agent_info = {
                "name": agent_name,
                "role": f"用户自定义角色 - {agent_name}",
                "type": "user_defined"
            }

            with st.chat_message("user"):
                ai_response = stream_agent_response(
                    speaker_id,
                    temp_agent_info,
                    scenario,
                    f"请以{agent_name}的身份，基于当前讨论分析{scenario['title']}情况并提出建议。",
                    context
                )

            add_message(speaker_id, ai_response)
            st.session_state.current_round += 1
            # 重置自动发言时间，让自动轮流继续
            st.session_state.last_auto_speak_time = time.time()
            st.rerun()

def generate_discussion_summary(selected_agents, demo_agents, scenario):
    """生成讨论总结"""

    # 收集所有发言
    discussion_content = "\n\n".join([
        f"{msg['speaker']}: {msg['content']}"
        for msg in st.session_state.simulation_messages
    ])

    summary_prompt = f"""请作为推演主持人，对以下关于"{scenario['title']}"的讨论进行总结：

场景背景：{scenario['context']}

讨论内容：
{discussion_content}

请从以下角度进行总结：
1. 各方主要观点和立场
2. 争议焦点和分歧点
3. 达成的共识
4. 待决策的关键问题

总结要客观、全面，为最终决策提供参考。"""

    if generate_response:
        messages = [
            {"role": "system", "content": "你是推演主持人，需要客观总结讨论内容。"},
            {"role": "user", "content": summary_prompt}
        ]

        try:
            with st.chat_message("moderator"):
                summary = stream_agent_response_simple(
                    "moderator",
                    demo_agents.get("moderator", {"name": "推演导调"}),
                    scenario,
                    summary_prompt,
                    []
                )
        except Exception as e:
            summary = f"各位专家就{scenario['title']}进行了深入讨论，现在需要做出最终决策。"
    else:
        summary = f"各位专家就{scenario['title']}进行了深入讨论，现在需要做出最终决策。"

    # 添加总结消息
    summary_msg = {
        "speaker": "moderator",
        "content": f"📋 **讨论总结**\n\n{summary}\n\n现在请特朗普总统做出最终决策。",
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }
    st.session_state.simulation_messages.append(summary_msg)
    st.session_state.simulation_phase = "decision"


def generate_final_decision(selected_agents, demo_agents, scenario):
    """生成特朗普的最终决策"""

    # 收集所有讨论内容
    discussion_content = "\n\n".join([
        f"{msg['speaker']}: {msg['content']}"
        for msg in st.session_state.simulation_messages
    ])

    # 动态生成决策问题
    def generate_decision_questions(scenario_title, scenario_context):
        """根据场景动态生成决策问题"""

        # 根据场景类型生成相应的决策问题
        if "台海" in scenario_title or "台湾" in scenario_title:
            return [
                f"美国是否应该军事干预{scenario_title.replace('应对策略', '').replace('危机', '')}？",
                "如果干预，采取什么具体方式和程度？",
                "与盟友的协调策略是什么？",
                "经济和外交手段如何配合？",
                "预期结果和风险评估"
            ]
        elif "贸易" in scenario_title:
            return [
                "是否应该实施新的贸易制裁措施？",
                "如何平衡经济利益和政治目标？",
                "与其他贸易伙伴的协调策略？",
                "对国内经济的影响评估？",
                "长期贸易政策调整方向？"
            ]
        elif "中东" in scenario_title or "军事干预" in scenario_title:
            return [
                "是否应该进行军事干预？",
                "军事行动的规模和持续时间？",
                "与地区盟友的合作方式？",
                "人道主义考虑和国际法约束？",
                "撤出策略和后续安排？"
            ]
        else:
            return [
                "应该采取什么主要行动？",
                "行动的具体实施方式和时间表？",
                "需要哪些资源和支持？",
                "潜在风险和应对措施？",
                "预期结果和成功标准？"
            ]

    decision_questions = generate_decision_questions(scenario['title'], scenario['context'])
    questions_text = "\n".join([f"{i+1}. {q}" for i, q in enumerate(decision_questions)])

    decision_prompt = f"""基于以下完整的关于"{scenario['title']}"的讨论，请作为唐纳德·特朗普做出最终决策：

场景背景：{scenario['context']}

讨论内容：
{discussion_content}

请明确回答：
{questions_text}

请给出明确、具体的决策方案，体现特朗普的决策风格。"""

    if generate_response:
        try:
            with st.chat_message("trump"):
                decision = stream_agent_response_simple(
                    "trump",
                    demo_agents.get("trump", {"name": "唐纳德·特朗普"}),
                    scenario,
                    decision_prompt,
                    []
                )
        except Exception as e:
            decision = f"基于各方对{scenario['title']}的深入讨论，我决定采取综合性策略，平衡各方利益，确保美国国家安全和利益最大化。"
    else:
        decision = f"基于各方对{scenario['title']}的深入讨论，我决定采取综合性策略，平衡各方利益，确保美国国家安全和利益最大化。"

    # 添加决策消息
    decision_msg = {
        "speaker": "trump",
        "content": f"⚖️ **最终决策**\n\n{decision}\n\n推演结束。",
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }
    st.session_state.simulation_messages.append(decision_msg)
    st.session_state.simulation_phase = "completed"
    st.success("🎉 推演完成！特朗普的最终决策已生成。")


def reset_simulation():
    """重置推演"""
    st.session_state.simulation_messages = []
    st.session_state.current_round = 0
    st.session_state.simulation_started = False
    st.session_state.auto_mode = False
    st.session_state.simulation_phase = "discussion"
    if 'simulation_config' in st.session_state:
        del st.session_state.simulation_config


def get_conversation_context():
    """获取对话上下文 - 智能管理版本"""

    if not st.session_state.simulation_messages:
        return []

    try:
        # 优先尝试完整版上下文管理器
        from context_manager import get_context_statistics
        stats = get_context_statistics(st.session_state.simulation_messages)

    except ImportError:
        try:
            # 备用：简化版上下文管理器
            from simple_context_manager import get_simple_context_statistics
            stats = get_simple_context_statistics(st.session_state.simulation_messages)

        except ImportError:
            # 最终备用：简单逻辑
            return st.session_state.simulation_messages[-5:] if st.session_state.simulation_messages else []

    # 智能截取逻辑
    if stats.get("estimated_context_ratio", 0) > 0.8:
        # 上下文接近限制，使用滑动窗口
        return st.session_state.simulation_messages[-8:]  # 最近8条
    elif len(st.session_state.simulation_messages) > 20:
        # 消息很多，使用摘要策略
        return st.session_state.simulation_messages[-10:]  # 最近10条
    else:
        # 正常情况，返回所有消息
        return st.session_state.simulation_messages
    
    # 用户参与讨论
    st.markdown("### 💬 用户参与讨论")

    user_input = st.text_area("输入您的观点和建议", placeholder="请输入您对当前推演场景的分析、建议或问题...")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("📝 提交观点", type="primary"):
            if user_input:
                # 添加用户消息到推演记录
                add_message("user", user_input)
                st.success("✅ 您的观点已添加到推演讨论中")
                # 清空输入框并刷新
                st.rerun()
            else:
                st.warning("⚠️ 请先输入您的观点")

    with col2:
        if st.button("🤖 获取AI专家分析"):
            if user_input and generate_response:
                with st.spinner("AI专家正在分析您的观点..."):
                    # 生成AI专家回复
                    messages = [
                        {"role": "system", "content": f"你是{scenario['title']}推演中的资深战略分析专家。请针对用户的观点提供专业、客观的分析和建议。"},
                        {"role": "user", "content": f"推演场景：{scenario['context']}\n\n用户观点：{user_input}\n\n请从战略分析专家的角度提供专业分析和建议："}
                    ]

                    ai_response = generate_response(messages, max_tokens=400, temperature=0.7)

                    # 添加AI回复到推演记录
                    add_message("ai_expert", ai_response)
                    st.success("✅ AI专家分析已添加到讨论中")
                    st.rerun()
            elif not generate_response:
                st.warning("⚠️ AI功能暂不可用，请检查模型配置")
            else:
                st.warning("⚠️ 请先输入您的观点")

def show_results_page():
    """显示推演结果页面"""

    st.header("📊 推演结果与分析")

    st.markdown("### 📈 系统统计数据")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("累计推演次数", "12", "次")

    with col2:
        st.metric("平均参与智能体", "4.2", "个")

    with col3:
        st.metric("平均讨论轮数", "6.8", "轮")

    with col4:
        st.metric("用户参与率", "85%", "5%")

    st.markdown("### 📋 历史推演记录")

    demo_results = [
        {
            "date": "2024-01-15",
            "scenario": "台海危机应对策略",
            "participants": ["特朗普", "军事专家", "政治专家"],
            "rounds": 8,
            "decision": "渐进式军事威慑配合外交努力",
            "quality_score": 8.5
        },
        {
            "date": "2024-01-14",
            "scenario": "中美贸易战升级",
            "participants": ["拜登", "经济专家", "政治专家"],
            "rounds": 6,
            "decision": "多边贸易谈判配合有限制裁",
            "quality_score": 7.8
        },
        {
            "date": "2024-01-13",
            "scenario": "中东军事干预决策",
            "participants": ["特朗普", "军事专家", "经济专家", "用户"],
            "rounds": 10,
            "decision": "有限军事支援配合人道主义援助",
            "quality_score": 9.1
        }
    ]

    for result in demo_results:
        with st.expander(f"📋 {result['date']} - {result['scenario']}"):
            col1, col2 = st.columns(2)

            with col1:
                st.write(f"**参与智能体**: {', '.join(result['participants'])}")
                st.write(f"**讨论轮数**: {result['rounds']} 轮")
                st.write(f"**决策质量评分**: {result['quality_score']}/10")

            with col2:
                st.write(f"**最终决策**: {result['decision']}")

                # 质量评分可视化
                quality_color = "🟢" if result['quality_score'] >= 8 else "🟡" if result['quality_score'] >= 6 else "🔴"
                st.write(f"**质量评级**: {quality_color} {'优秀' if result['quality_score'] >= 8 else '良好' if result['quality_score'] >= 6 else '一般'}")

    # 当前会话结果
    if 'simulation_messages' in st.session_state and st.session_state.simulation_messages:
        st.markdown("### 🎯 当前推演会话")

        current_messages = st.session_state.simulation_messages

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("本次发言数", len(current_messages), "条")
        with col2:
            st.metric("参与智能体", len(set(msg['speaker'] for msg in current_messages)), "个")
        with col3:
            st.metric("推演时长", f"{len(current_messages) * 2}", "分钟")

        # 导出功能
        if st.button("📥 导出当前推演记录"):
            export_data = {
                "推演时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "发言记录": current_messages,
                "统计信息": {
                    "总发言数": len(current_messages),
                    "参与智能体数": len(set(msg['speaker'] for msg in current_messages)),
                    "推演时长": f"{len(current_messages) * 2}分钟"
                }
            }

            st.download_button(
                label="📄 下载JSON格式",
                data=json.dumps(export_data, ensure_ascii=False, indent=2),
                file_name=f"推演记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

if __name__ == "__main__":
    main()
