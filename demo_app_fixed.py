#!/usr/bin/env python3
"""
修复版演示应用 - 解决chat_message嵌套问题
"""

import streamlit as st
import time
from datetime import datetime

# 设置页面配置
st.set_page_config(
    page_title="战略推演系统",
    page_icon="🎯",
    layout="wide"
)

def simple_stream_display(content: str, agent_name: str, agent_id: str):
    """简单的流式显示，避免嵌套问题"""
    
    # 显示智能体信息
    st.write(f"**{agent_name}** - {datetime.now().strftime('%H:%M:%S')}")
    
    # 创建占位符
    placeholder = st.empty()
    displayed_text = ""
    
    # 按字符逐步显示
    for char in content:
        displayed_text += char
        # 更新显示
        placeholder.markdown(f"""
        <div style="background: #f0f0f0; padding: 12px; border-radius: 8px; margin: 5px 0;">
            {displayed_text}<span style="animation: blink 1s infinite; color: #666;">|</span>
        </div>
        
        <style>
        @keyframes blink {{
            0%, 50% {{ opacity: 1; }}
            51%, 100% {{ opacity: 0; }}
        }}
        </style>
        """, unsafe_allow_html=True)
        time.sleep(0.03)
    
    # 完成后显示最终结果
    placeholder.markdown(f"""
    <div style="background: #f0f0f0; padding: 12px; border-radius: 8px; margin: 5px 0;">
        {displayed_text}
    </div>
    """, unsafe_allow_html=True)
    
    return displayed_text

def generate_mock_response(agent_name: str, scenario: str) -> str:
    """生成模拟回应"""
    
    responses = {
        "推演导调": f"各位参与者，欢迎进入{scenario}推演。请各位基于当前形势展开分析，重点关注战略目标、风险评估和替代方案。",
        "特朗普": "我们必须展现美国的实力！但是，我们不能让其他国家占我们便宜。如果要干预，他们必须为此买单。",
        "军事专家": "从军事角度分析，我们需要评估干预的可行性、风险和成本。建议采取渐进式策略，避免过度升级。",
        "政治专家": "这个问题涉及复杂的地缘政治因素。我们需要考虑国际法、盟友关系和长期战略影响。"
    }
    
    return responses.get(agent_name, f"我是{agent_name}，针对{scenario}，我认为需要综合考虑各方面因素。")

def main():
    """主函数"""
    
    st.title("🎯 战略推演系统 - 流式输出测试")
    
    # 初始化
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'agents' not in st.session_state:
        st.session_state.agents = ["推演导调", "特朗普", "军事专家", "政治专家"]
    
    if 'current_agent' not in st.session_state:
        st.session_state.current_agent = 0
    
    # 侧边栏设置
    with st.sidebar:
        st.header("🎮 推演设置")
        
        scenario = "中东军事干预决策"
        st.info(f"当前场景: {scenario}")
        
        enable_stream = st.checkbox("🌊 启用流式输出", value=True)
        typing_speed = st.slider("打字速度", 0.01, 0.1, 0.03)
        
        st.markdown("**参与智能体:**")
        for agent in st.session_state.agents:
            st.write(f"• {agent}")
        
        if st.button("🎬 开始推演"):
            if not st.session_state.messages:
                # 导调开场
                if enable_stream:
                    with st.chat_message("assistant"):
                        response = generate_mock_response("推演导调", scenario)
                        displayed_response = simple_stream_display(response, "推演导调", "moderator")
                        
                        # 添加到消息历史
                        st.session_state.messages.append({
                            "speaker": "推演导调",
                            "content": displayed_response,
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        })
                else:
                    response = generate_mock_response("推演导调", scenario)
                    with st.chat_message("assistant"):
                        st.write(f"**推演导调** - {datetime.now().strftime('%H:%M:%S')}")
                        st.write(response)
                    
                    st.session_state.messages.append({
                        "speaker": "推演导调",
                        "content": response,
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    })
                
                st.rerun()
        
        if st.button("➡️ 下一位发言"):
            if st.session_state.agents:
                current_agent = st.session_state.agents[st.session_state.current_agent % len(st.session_state.agents)]
                
                if current_agent != "推演导调":  # 跳过导调
                    if enable_stream:
                        with st.chat_message("assistant"):
                            response = generate_mock_response(current_agent, scenario)
                            displayed_response = simple_stream_display(response, current_agent, current_agent.lower())
                            
                            # 添加到消息历史
                            st.session_state.messages.append({
                                "speaker": current_agent,
                                "content": displayed_response,
                                "timestamp": datetime.now().strftime("%H:%M:%S")
                            })
                    else:
                        response = generate_mock_response(current_agent, scenario)
                        with st.chat_message("assistant"):
                            st.write(f"**{current_agent}** - {datetime.now().strftime('%H:%M:%S')}")
                            st.write(response)
                        
                        st.session_state.messages.append({
                            "speaker": current_agent,
                            "content": response,
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        })
                
                st.session_state.current_agent += 1
                st.rerun()
        
        if st.button("🔄 重置"):
            st.session_state.messages = []
            st.session_state.current_agent = 0
            st.rerun()
    
    # 主聊天区域
    st.markdown("### 💬 推演讨论")
    
    # 显示所有消息（不使用chat_message避免嵌套）
    for msg in st.session_state.messages:
        st.markdown(f"""
        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #007AFF;">
            <strong>{msg['speaker']}</strong> - {msg['timestamp']}<br><br>
            {msg['content']}
        </div>
        """, unsafe_allow_html=True)
    
    # 用户输入
    st.markdown("### 💬 参与讨论")
    user_input = st.text_area("输入您的观点:", height=100)
    
    if st.button("📤 发送"):
        if user_input:
            user_msg = {
                "speaker": "用户",
                "content": user_input,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(user_msg)
            st.rerun()
    
    # 说明
    st.markdown("### 📝 说明")
    st.info("""
    这是修复版本，解决了chat_message嵌套问题：
    - ✅ 流式输出正常工作
    - ✅ 避免了嵌套错误
    - ✅ 保持了聊天界面效果
    """)

if __name__ == "__main__":
    main()
