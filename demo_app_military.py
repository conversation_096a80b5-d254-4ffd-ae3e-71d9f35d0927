#!/usr/bin/env python3
"""
Strategic Simulation System - 军事风格演示应用

军事主题的战略推演决策仿真平台
"""

import streamlit as st
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 军事主题CSS样式
MILITARY_CSS = """
<style>
/* 军事主题样式 - 军绿色主题 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* 全局样式 */
.stApp {
    background: linear-gradient(135deg, #0a1a0a 0%, #1a2f1a 25%, #2d4a2d 50%, #1e3a1e 75%, #0f1f0f 100%);
    background-attachment: fixed;
    color: #e8e8e8;
    font-family: 'Rajdhani', 'Orbitron', monospace;
    position: relative;
}

/* 添加军事网格背景 */
.stApp::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(144,238,144,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(144,238,144,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: -1;
}

/* 标题样式 */
h1, h2, h3 {
    color: #90EE90 !important;
    text-shadow:
        0 0 10px rgba(144,238,144,0.8),
        2px 2px 4px rgba(0,0,0,0.8),
        0 0 20px rgba(144,238,144,0.4);
    font-weight: 700;
    letter-spacing: 2px;
    font-family: 'Orbitron', monospace;
}

h1 {
    font-size: 2.5rem !important;
    text-transform: uppercase;
    border-bottom: 3px solid #90EE90;
    padding-bottom: 10px;
    margin-bottom: 20px !important;
}

h2 {
    font-size: 1.8rem !important;
    border-left: 4px solid #90EE90;
    padding-left: 15px;
}

h3 {
    font-size: 1.4rem !important;
    color: #32CD32 !important;
}

/* 侧边栏样式 */
.sidebar .sidebar-content {
    background: linear-gradient(180deg, #1a2f1a 0%, #2d4a2d 100%);
    border-right: 3px solid #90EE90;
}

/* 按钮样式 */
.stButton > button {
    background: linear-gradient(45deg, #2d4a2d, #3a5a3a, #2d4a2d);
    color: #90EE90;
    border: 2px solid #90EE90;
    border-radius: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow:
        0 4px 8px rgba(0,0,0,0.3),
        inset 0 1px 0 rgba(144,238,144,0.2);
    font-family: 'Orbitron', monospace;
    position: relative;
    overflow: hidden;
}

.stButton > button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(144,238,144,0.3), transparent);
    transition: left 0.5s;
}

.stButton > button:hover::before {
    left: 100%;
}

.stButton > button:hover {
    background: linear-gradient(45deg, #90EE90, #98FB98, #90EE90);
    color: #1a2f1a;
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(144,238,144,0.4),
        0 0 20px rgba(144,238,144,0.3);
    text-shadow: 0 0 5px rgba(26,47,26,0.8);
}

/* 主要按钮样式 */
.stButton > button[data-baseweb="button"] {
    background: linear-gradient(45deg, #8B4513, #A0522D);
    border-color: #D2691E;
}

.stButton > button[data-baseweb="button"]:hover {
    background: linear-gradient(45deg, #D2691E, #CD853F);
    color: white;
}

/* 卡片样式 */
.stMetric {
    background: rgba(26, 47, 26, 0.8);
    border: 2px solid #90EE90;
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    backdrop-filter: blur(10px);
}

/* 选择框样式 */
.stSelectbox > div > div {
    background: rgba(26, 47, 26, 0.9);
    border: 2px solid #90EE90;
    border-radius: 8px;
    color: #90EE90;
}

/* 文本输入框样式 */
.stTextInput > div > div > input {
    background: rgba(26, 47, 26, 0.9);
    border: 2px solid #90EE90;
    border-radius: 8px;
    color: #e8e8e8;
}

/* 聊天消息样式 */
.stChatMessage {
    background: rgba(26, 47, 26, 0.9);
    border: 2px solid #90EE90;
    border-radius: 15px;
    margin: 15px 0;
    padding: 20px;
    box-shadow:
        0 4px 8px rgba(0,0,0,0.3),
        inset 0 1px 0 rgba(144,238,144,0.1);
    position: relative;
    backdrop-filter: blur(5px);
}

.stChatMessage::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #90EE90, #32CD32, #90EE90);
    border-radius: 15px 15px 0 0;
}

/* 消息发送者样式 */
.stChatMessage .stMarkdown h4 {
    color: #32CD32 !important;
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 10px !important;
    border-bottom: 1px solid #32CD32;
    padding-bottom: 5px;
}

/* 进度条样式 */
.stProgress > div > div > div {
    background: linear-gradient(90deg, #90EE90, #98FB98);
}

/* 警告框样式 */
.stAlert {
    background: rgba(139, 69, 19, 0.2);
    border: 2px solid #8B4513;
    border-radius: 10px;
}

/* 成功框样式 */
.stAlert[data-baseweb="notification"] {
    background: rgba(34, 139, 34, 0.2);
    border: 2px solid #228B22;
}

/* 信息框样式 */
.stAlert[data-baseweb="banner"] {
    background: rgba(46, 139, 87, 0.2);
    border: 2px solid #2E8B57;
}

/* 数据表格样式 */
.dataframe {
    background: rgba(26, 47, 26, 0.9);
    border: 2px solid #90EE90;
    border-radius: 10px;
}

/* 展开器样式 */
.streamlit-expanderHeader {
    background: rgba(26, 47, 26, 0.8);
    border: 2px solid #90EE90;
    border-radius: 8px;
    color: #90EE90;
    font-weight: bold;
}

/* 军事图标动画 */
@keyframes military-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.military-icon {
    animation: military-pulse 2s infinite;
    color: #90EE90;
}

/* 雷达扫描效果 */
@keyframes radar-scan {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.radar-icon {
    animation: radar-scan 3s linear infinite;
    color: #32CD32;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.status-online {
    background: #32CD32;
    box-shadow: 0 0 10px #32CD32;
}

.status-offline {
    background: #DC143C;
    box-shadow: 0 0 10px #DC143C;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 军事徽章样式 */
.military-badge {
    background: linear-gradient(45deg, #90EE90, #98FB98);
    color: #1a2f1a;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-block;
    margin: 5px;
    box-shadow: 0 4px 8px rgba(144,238,144,0.3);
}

/* 命令控制台样式 */
.command-console {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #32CD32;
    border-radius: 10px;
    padding: 20px;
    font-family: 'Courier New', monospace;
    color: #32CD32;
    margin: 10px 0;
}

/* 战术面板样式 */
.tactical-panel {
    background: rgba(26, 47, 26, 0.95);
    border: 3px solid #90EE90;
    border-radius: 20px;
    padding: 25px;
    margin: 20px 0;
    box-shadow:
        0 8px 16px rgba(144,238,144,0.2),
        inset 0 1px 0 rgba(144,238,144,0.1),
        0 0 30px rgba(144,238,144,0.1);
    position: relative;
    backdrop-filter: blur(10px);
}

.tactical-panel::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #90EE90, #32CD32, #90EE90, #32CD32);
    border-radius: 22px;
    z-index: -1;
    animation: border-glow 3s ease-in-out infinite alternate;
}

@keyframes border-glow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 指挥控制台特殊样式 */
.command-center {
    background: rgba(0, 20, 0, 0.95);
    border: 3px solid #32CD32;
    border-radius: 20px;
    padding: 25px;
    margin: 20px 0;
    box-shadow:
        0 0 20px rgba(50,205,50,0.3),
        inset 0 0 20px rgba(50,205,50,0.1);
    position: relative;
}

.command-center::after {
    content: '⚔️ COMMAND CENTER ⚔️';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #0a1a0a;
    color: #32CD32;
    padding: 5px 20px;
    font-family: 'Orbitron', monospace;
    font-weight: bold;
    font-size: 12px;
    letter-spacing: 2px;
    border: 2px solid #32CD32;
    border-radius: 10px;
}

/* 新增军事元素样式 */
.mission-status {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #32CD32;
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    font-family: 'Courier New', monospace;
    color: #32CD32;
    position: relative;
}

.mission-status::before {
    content: '>>> MISSION STATUS <<<';
    position: absolute;
    top: -12px;
    left: 10px;
    background: #0a1a0a;
    padding: 0 10px;
    font-size: 10px;
    font-weight: bold;
    letter-spacing: 1px;
}

/* 雷达扫描动画增强 */
.radar-container {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto;
}

.radar-sweep {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid #32CD32;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(50,205,50,0.1) 0%, transparent 70%);
}

.radar-sweep::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 50%;
    background: linear-gradient(to bottom, #32CD32, transparent);
    transform-origin: bottom;
    transform: translate(-50%, -100%) rotate(0deg);
    animation: radar-sweep 2s linear infinite;
}

@keyframes radar-sweep {
    0% { transform: translate(-50%, -100%) rotate(0deg); }
    100% { transform: translate(-50%, -100%) rotate(360deg); }
}

/* 数据流动画 */
.data-stream {
    position: relative;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #32CD32;
    border-radius: 5px;
    padding: 10px;
    margin: 5px 0;
}

.data-stream::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(50,205,50,0.3), transparent);
    animation: data-flow 2s linear infinite;
}

@keyframes data-flow {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tactical-panel, .command-center {
        padding: 15px;
        margin: 15px 0;
    }

    h1 {
        font-size: 1.8rem !important;
        letter-spacing: 1px;
    }

    h2 {
        font-size: 1.4rem !important;
    }

    h3 {
        font-size: 1.2rem !important;
    }

    .radar-container {
        width: 40px;
        height: 40px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: rgba(26, 47, 26, 0.5);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #90EE90, #32CD32);
    border-radius: 6px;
    border: 2px solid rgba(26, 47, 26, 0.5);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #98FB98, #90EE90);
}
</style>
"""

try:
    from utils.llm_config import llm_config, test_llm_connection, generate_response
    print("✅ AI模块导入成功")
except ImportError as e:
    print(f"⚠️ AI模块导入失败: {e}")
    # 创建备用AI功能
    import os
    from dotenv import load_dotenv
    load_dotenv()

    class FallbackLLMConfig:
        def __init__(self):
            self.api_key = os.getenv('OPENAI_API_KEY')
            self.base_url = os.getenv('OPENAI_BASE_URL')
            self.model_name = os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B')

        def is_configured(self):
            return bool(self.api_key and self.api_key != 'your_openai_api_key_here')

        def get_provider_info(self):
            return {
                'model': self.model_name,
                'provider': 'siliconflow',
                'base_url': self.base_url or 'https://api.siliconflow.cn/v1',
                'configured': str(self.is_configured())
            }

    def fallback_test_connection():
        try:
            import openai
            client = openai.OpenAI(
                api_key=os.getenv('OPENAI_API_KEY'),
                base_url=os.getenv('OPENAI_BASE_URL')
            )

            response = client.chat.completions.create(
                model=os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B'),
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=50
            )

            return {
                'success': True,
                'response': response.choices[0].message.content,
                'model': response.model
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def fallback_generate_response(messages, **kwargs):
        try:
            import openai
            client = openai.OpenAI(
                api_key=os.getenv('OPENAI_API_KEY'),
                base_url=os.getenv('OPENAI_BASE_URL')
            )

            # 检查是否需要流式输出
            if kwargs.get('stream', False):
                return client.chat.completions.create(
                    model=os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B'),
                    messages=messages,
                    max_tokens=kwargs.get('max_tokens', 300),
                    temperature=kwargs.get('temperature', 0.7),
                    stream=True
                )
            else:
                response = client.chat.completions.create(
                    model=os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B'),
                    messages=messages,
                    max_tokens=kwargs.get('max_tokens', 300),
                    temperature=kwargs.get('temperature', 0.7)
                )
                return response.choices[0].message.content
        except Exception as e:
            return f"AI生成失败: {str(e)}"

    llm_config = FallbackLLMConfig()
    test_llm_connection = fallback_test_connection
    generate_response = fallback_generate_response

def load_demo_data():
    """加载演示数据"""
    
    # 智能体数据
    demo_agents = {
        "trump": {
            "name": "🇺🇸 唐纳德·特朗普",
            "role": "前美国总统",
            "description": "美国第45任总统，商人出身，以'美国优先'政策著称",
            "expertise": ["外交政策", "经济政策", "贸易谈判"],
            "personality": "自信、直接、交易性思维"
        },
        "moderator": {
            "name": "🎖️ 军事推演主持人",
            "role": "推演协调官",
            "description": "资深军事战略专家，负责推演流程控制和综合分析",
            "expertise": ["战略分析", "流程控制", "决策评估"],
            "personality": "专业、客观、系统性思维"
        },
        "political_expert": {
            "name": "🏛️ 政治战略专家",
            "role": "政治顾问",
            "description": "国际关系和政治战略专家，擅长政策分析和风险评估",
            "expertise": ["国际关系", "政策分析", "政治风险评估"],
            "personality": "理性、分析性、全局思维"
        },
        "military_expert": {
            "name": "⚔️ 军事战略专家",
            "role": "军事顾问",
            "description": "退役高级军官，军事战略和作战分析专家",
            "expertise": ["军事战略", "作战分析", "国防政策"],
            "personality": "严谨、战术性、实战思维"
        },
        "economic_expert": {
            "name": "💰 经济战略专家",
            "role": "经济顾问",
            "description": "经济学家，专注于经济政策和贸易战略分析",
            "expertise": ["经济政策", "贸易战略", "金融分析"],
            "personality": "数据驱动、逻辑性、前瞻性思维"
        }
    }
    
    # 推演场景数据
    demo_scenarios = {
        "taiwan_crisis": {
            "title": "🏝️ 台海危机推演",
            "description": "模拟台海局势紧张情况下的战略决策过程",
            "context": "近期台海局势紧张，需要制定相应的战略应对方案。",
            "considerations": [
                "军事部署和威慑",
                "外交手段和谈判",
                "经济制裁和影响",
                "国际盟友反应",
                "国内政治影响"
            ],
            "estimated_duration": 45
        },
        "cyber_warfare": {
            "title": "💻 网络战推演",
            "description": "模拟大规模网络攻击的应对策略",
            "context": "面临来自敌对国家的网络攻击威胁，需要制定防御和反击策略。",
            "considerations": [
                "网络安全防御",
                "攻击溯源和归因",
                "反制措施选择",
                "国际合作协调",
                "法律和道德考量"
            ],
            "estimated_duration": 30
        },
        "economic_conflict": {
            "title": "💼 经济战推演",
            "description": "模拟经济制裁和贸易战的应对策略",
            "context": "面临经济制裁和贸易战威胁，需要制定经济应对策略。",
            "considerations": [
                "经济影响评估",
                "替代市场寻找",
                "供应链重组",
                "金融体系稳定",
                "国际合作策略"
            ],
            "estimated_duration": 40
        }
    }
    
    return demo_agents, demo_scenarios

def main():
    """主应用 - 军事风格"""
    
    # 设置页面配置
    st.set_page_config(
        page_title="军事战略推演系统",
        page_icon="⚔️",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 注入军事主题CSS
    st.markdown(MILITARY_CSS, unsafe_allow_html=True)
    
    # 军事风格标题
    st.markdown("""
    <div class="command-center" style="text-align: center; padding: 30px; margin: 20px 0;">
        <div class="radar-container" style="margin-bottom: 20px;">
            <div class="radar-sweep"></div>
        </div>
        <h1 style="color: #90EE90; margin: 20px 0;">
            ⚔️ 军事战略推演指挥中心 ⚔️
        </h1>
        <h3 style="color: #32CD32; margin: 15px 0;">
            🎖️ AI驱动的战术决策仿真平台 🎖️
        </h3>
        <div class="mission-status" style="margin-top: 20px;">
            <div class="data-stream">
                >>> 系统状态: 在线 | 安全等级: 最高 | 准备状态: 就绪 <<<
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # 加载演示数据
    demo_agents, demo_scenarios = load_demo_data()
    
    # 军事风格侧边栏
    with st.sidebar:
        st.markdown("""
        <div class="tactical-panel" style="text-align: center; padding: 15px; margin-bottom: 20px;">
            <div class="radar-container" style="margin-bottom: 10px;">
                <div class="radar-sweep"></div>
            </div>
            <h3 style="color: #90EE90; margin: 0;">🎖️ 指挥控制台</h3>
            <div class="status-indicator status-online"></div>
            <span style="color: #32CD32; font-size: 12px;">系统在线</span>
        </div>
        """, unsafe_allow_html=True)

        # 页面选择
        page = st.selectbox(
            "📋 选择作战模块",
            ["🏠 指挥中心", "⚔️ 作战单位", "📚 情报库", "🗺️ 战场态势", "🚀 开始推演", "💾 数据管理", "📊 战果分析"]
        )
        
        # 系统状态显示
        st.markdown("""
        <div style="background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; border: 1px solid #32CD32; margin-top: 20px;">
            <h4 style="color: #32CD32; margin: 0 0 10px 0;">🖥️ 系统状态</h4>
        </div>
        """, unsafe_allow_html=True)
        
        # 检查AI模型状态
        if llm_config and llm_config.is_configured():
            st.markdown('<span class="status-indicator status-online"></span> AI模型已连接', unsafe_allow_html=True)
        else:
            st.markdown('<span class="status-indicator status-offline"></span> AI模型未连接', unsafe_allow_html=True)
        
        st.markdown('<span class="status-indicator status-online"></span> 推演引擎运行中', unsafe_allow_html=True)
        st.markdown('<span class="status-indicator status-online"></span> 数据库连接正常', unsafe_allow_html=True)
    
    # 主页面路由
    if page == "🏠 指挥中心":
        show_military_home_page(demo_agents, demo_scenarios)
    elif page == "⚔️ 作战单位":
        show_military_agents_page(demo_agents)
    elif page == "📚 情报库":
        show_military_knowledge_page()
    elif page == "🗺️ 战场态势":
        show_military_scenarios_page(demo_scenarios)
    elif page == "🚀 开始推演":
        show_military_simulation_page(demo_agents, demo_scenarios)
    elif page == "💾 数据管理":
        show_military_data_page()
    elif page == "📊 战果分析":
        show_military_results_page()

def show_military_home_page(demo_agents, demo_scenarios):
    """显示军事风格主页"""
    
    st.markdown("""
    <div class="tactical-panel">
        <h2 style="color: #90EE90; text-align: center;">🎖️ 欢迎来到军事战略推演指挥中心</h2>
        <p style="text-align: center; color: #e8e8e8; font-size: 18px;">
            这是一个基于人工智能的多智能体军事战略决策仿真平台，为军事指挥官、战略分析师和决策者提供强大的推演工具。
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # 核心功能展示
    st.markdown("""
    <div class="tactical-panel">
        <h3 style="color: #90EE90;">⚔️ 核心作战能力</h3>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">🤖 多智能体协同作战</h4>
            <p style="color: #e8e8e8;">特朗普、军事专家等各领域智能体协同作战</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">💬 战术讨论与决策</h4>
            <p style="color: #e8e8e8;">可配置的发言顺序、讨论轮数和流程控制</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">🧠 战略分析与评估</h4>
            <p style="color: #e8e8e8;">基于AI的深度情况分析和决策建议</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">📚 情报库增强</h4>
            <p style="color: #e8e8e8;">RAG检索技术增强的专业知识库系统</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">👥 指挥官参与</h4>
            <p style="color: #e8e8e8;">实时参与讨论、提供观点和引导决策过程</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">🎭 角色扮演模拟</h4>
            <p style="color: #e8e8e8;">真实还原各政治人物的思维模式和表达风格</p>
        </div>
        """, unsafe_allow_html=True)
    
    # 系统状态仪表板
    st.markdown("""
    <div class="tactical-panel">
        <h3 style="color: #90EE90; text-align: center;">📊 作战系统状态仪表板</h3>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="stMetric">
            <h4 style="color: #90EE90;">⚔️ 可用作战单位</h4>
            <h2 style="color: #32CD32;">{len(demo_agents)}</h2>
            <p style="color: #e8e8e8;">个智能体</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="stMetric">
            <h4 style="color: #90EE90;">🗺️ 预设战场</h4>
            <h2 style="color: #32CD32;">{len(demo_scenarios)}</h2>
            <p style="color: #e8e8e8;">个推演场景</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        # Check LLM status
        if llm_config and llm_config.is_configured():
            status_text = "🟢 已连接"
            status_color = "#32CD32"
        else:
            status_text = "🔴 未连接"
            status_color = "#DC143C"
        
        st.markdown(f"""
        <div class="stMetric">
            <h4 style="color: #90EE90;">🤖 AI模型状态</h4>
            <h2 style="color: {status_color};">{status_text}</h2>
            <p style="color: #e8e8e8;">智能分析引擎</p>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown("""
        <div class="stMetric">
            <h4 style="color: #90EE90;">🖥️ 系统状态</h4>
            <h2 style="color: #32CD32;">🟢 运行正常</h2>
            <p style="color: #e8e8e8;">指挥中心就绪</p>
        </div>
        """, unsafe_allow_html=True)

    # AI模型配置信息
    if llm_config:
        st.markdown("""
        <div class="tactical-panel">
            <h3 style="color: #90EE90;">🤖 AI智能分析引擎配置</h3>
        </div>
        """, unsafe_allow_html=True)

        provider_info = llm_config.get_provider_info()

        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f"""
            <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
                <h4 style="color: #90EE90;">🎯 使用模型</h4>
                <p style="color: #e8e8e8;">{provider_info['model']}</p>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown(f"""
            <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
                <h4 style="color: #90EE90;">🏢 服务提供商</h4>
                <p style="color: #e8e8e8;">{provider_info['provider']}</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown(f"""
            <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
                <h4 style="color: #90EE90;">🌐 API接口</h4>
                <p style="color: #e8e8e8;">{provider_info['base_url']}</p>
            </div>
            """, unsafe_allow_html=True)
            
            connection_status = "✅ 已连接" if provider_info['configured'] == 'True' else "❌ 未连接"
            connection_color = "#32CD32" if provider_info['configured'] == 'True' else "#DC143C"
            
            st.markdown(f"""
            <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
                <h4 style="color: #90EE90;">🔗 连接状态</h4>
                <p style="color: {connection_color};">{connection_status}</p>
            </div>
            """, unsafe_allow_html=True)

        # 测试连接按钮
        if st.button("🔄 测试AI引擎连接", type="primary"):
            with st.spinner("正在测试AI引擎连接..."):
                result = test_llm_connection()
                if result['success']:
                    st.success("✅ AI引擎连接正常！")
                    st.info(f"模型响应: {result['response'][:100]}...")
                else:
                    st.error(f"❌ AI引擎连接失败: {result['error']}")

def show_military_agents_page(demo_agents):
    """显示军事风格智能体页面"""
    
    st.markdown("""
    <div class="tactical-panel">
        <h2 style="color: #90EE90; text-align: center;">⚔️ 作战单位管理</h2>
        <p style="text-align: center; color: #e8e8e8;">管理参与推演的智能体作战单位</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 显示所有智能体
    for agent_id, agent in demo_agents.items():
        st.markdown(f"""
        <div style="background: rgba(26,47,26,0.8); padding: 20px; border-radius: 15px; border: 2px solid #90EE90; margin: 15px 0;">
            <h3 style="color: #90EE90;">{agent['name']}</h3>
            <p style="color: #e8e8e8;"><strong>军衔/职位:</strong> {agent['role']}</p>
            <p style="color: #e8e8e8;"><strong>作战描述:</strong> {agent['description']}</p>
            <p style="color: #e8e8e8;"><strong>专业领域:</strong> {', '.join(agent['expertise'])}</p>
            <p style="color: #e8e8e8;"><strong>作战风格:</strong> {agent['personality']}</p>
        </div>
        """, unsafe_allow_html=True)

def show_military_knowledge_page():
    """显示军事风格知识库页面"""
    
    st.markdown("""
    <div class="tactical-panel">
        <h2 style="color: #90EE90; text-align: center;">📚 情报库管理</h2>
        <p style="text-align: center; color: #e8e8e8;">管理智能体的专业知识库</p>
    </div>
    """, unsafe_allow_html=True)
    
    st.info("💡 情报库功能正在开发中，请使用增强知识库管理系统")

def show_military_scenarios_page(demo_scenarios):
    """显示军事风格场景页面"""
    
    st.markdown("""
    <div class="tactical-panel">
        <h2 style="color: #90EE90; text-align: center;">🗺️ 战场态势分析</h2>
        <p style="text-align: center; color: #e8e8e8;">预设的推演战场场景</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 显示所有场景
    for scenario_id, scenario in demo_scenarios.items():
        st.markdown(f"""
        <div style="background: rgba(26,47,26,0.8); padding: 20px; border-radius: 15px; border: 2px solid #90EE90; margin: 15px 0;">
            <h3 style="color: #90EE90;">{scenario['title']}</h3>
            <p style="color: #e8e8e8;"><strong>战场描述:</strong> {scenario['description']}</p>
            <p style="color: #e8e8e8;"><strong>态势背景:</strong> {scenario['context']}</p>
            <p style="color: #e8e8e8;"><strong>关键考虑因素:</strong></p>
            <ul style="color: #e8e8e8;">
                {''.join([f'<li>{consideration}</li>' for consideration in scenario['considerations']])}
            </ul>
            <p style="color: #e8e8e8;"><strong>预计推演时长:</strong> {scenario['estimated_duration']} 分钟</p>
        </div>
        """, unsafe_allow_html=True)

def show_military_simulation_page(demo_agents, demo_scenarios):
    """显示军事风格推演页面"""

    # 首先初始化所有必要的session_state
    if 'simulation_messages' not in st.session_state:
        st.session_state.simulation_messages = []

    if 'current_round' not in st.session_state:
        st.session_state.current_round = 0

    if 'auto_mode' not in st.session_state:
        st.session_state.auto_mode = False

    if 'simulation_phase' not in st.session_state:
        st.session_state.simulation_phase = "setup"

    # 确保推演相关状态变量初始化
    if 'simulation_messages' not in st.session_state:
        st.session_state.simulation_messages = []
    if 'current_round' not in st.session_state:
        st.session_state.current_round = 0
    if 'simulation_started' not in st.session_state:
        st.session_state.simulation_started = False

    if 'max_discussion_rounds' not in st.session_state:
        st.session_state.max_discussion_rounds = 20

    if 'last_auto_speak_time' not in st.session_state:
        st.session_state.last_auto_speak_time = 0

    st.markdown("""
    <div class="tactical-panel">
        <h2 style="color: #90EE90; text-align: center;">🚀 军事战略推演指挥中心</h2>
        <p style="text-align: center; color: #e8e8e8;">配置并启动军事战略推演</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 场景选择
    st.markdown("""
    <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 15px 0;">
        <h3 style="color: #90EE90;">🗺️ 选择战场态势</h3>
    </div>
    """, unsafe_allow_html=True)

    # 合并预设和自定义场景
    if 'custom_scenarios' not in st.session_state:
        st.session_state.custom_scenarios = {}

    all_scenarios = {**demo_scenarios, **st.session_state.custom_scenarios}
    scenario_options = {}

    for k, s in all_scenarios.items():
        is_custom = k in st.session_state.custom_scenarios
        label = f"{s['title']} {'(自定义)' if is_custom else '(预设)'}"
        scenario_options[label] = k

    selected_scenario_label = st.selectbox("战场态势", list(scenario_options.keys()))
    selected_scenario_key = scenario_options[selected_scenario_label]
    
    if selected_scenario_label:
        scenario = all_scenarios[selected_scenario_key]

        st.markdown(f"""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 10px 0;">
            <h4 style="color: #90EE90;">🎯 战场态势: {scenario['description']}</h4>
            <p style="color: #e8e8e8;"><strong>态势背景:</strong> {scenario['context']}</p>
        </div>
        """, unsafe_allow_html=True)

        # 显示场景详细信息
        if 'considerations' in scenario and scenario['considerations']:
            st.markdown("**📋 关键战术考虑因素:**")
            for consideration in scenario['considerations']:
                st.write(f"• {consideration}")

        if 'estimated_duration' in scenario:
            st.caption(f"⏱️ 预估推演时长: {scenario['estimated_duration']}分钟")
    
    # 智能体选择
    st.markdown("""
    <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 15px 0;">
        <h3 style="color: #90EE90;">⚔️ 选择作战单位</h3>
    </div>
    """, unsafe_allow_html=True)

    # 合并预设和自定义智能体
    if 'custom_agents' not in st.session_state:
        st.session_state.custom_agents = {}

    all_agents = {**demo_agents, **st.session_state.custom_agents}

    # 智能体选择和知识库配置
    col1, col2 = st.columns([3, 2])

    with col1:
        st.write("**可选作战单位**:")
        selected_agents = []

        for agent_id, agent in all_agents.items():
            is_custom = agent_id in st.session_state.custom_agents
            label = f"{agent['name']} ({agent['role']}) {'🆕' if is_custom else ''}"
            if st.checkbox(label, key=f"select_{agent_id}"):
                selected_agents.append(agent_id)

    with col2:
        # 知识库配置面板
        st.write("**📚 作战单位情报库**:")

        # 导入知识库UI组件
        try:
            from knowledge_ui import show_agent_knowledge_panel, get_agent_knowledge_context

            # 显示选中智能体的知识库状态
            if selected_agents:
                # 只显示预设智能体的知识库（用户智能体暂不支持）
                preset_agents = [aid for aid in selected_agents if aid in demo_agents]

                if preset_agents:
                    st.info(f"💡 为{len(preset_agents)}个作战单位配置专属情报库")

                    # 快速知识库状态
                    from knowledge_base import get_knowledge_base
                    kb = get_knowledge_base()

                    for agent_id in preset_agents[:3]:  # 最多显示3个
                        agent_name = demo_agents[agent_id]['name']
                        files = kb.get_agent_knowledge(agent_id)
                        status_icon = "🟢" if files else "⚪"
                        st.write(f"{status_icon} {agent_name}: {len(files)}个文件")

                    if st.button("🔧 管理情报库", key="manage_kb"):
                        st.session_state.show_knowledge_page = True
                        st.rerun()
                else:
                    st.info("💡 选择预设作战单位以配置情报库")
            else:
                st.info("💡 先选择作战单位，再配置情报库")

        except ImportError:
            st.warning("⚠️ 情报库功能未加载")

        # 添加用户智能体选项
        st.markdown("**👤 添加指挥官控制的作战单位:**")

        # 初始化用户智能体列表
        if 'user_agents' not in st.session_state:
            st.session_state.user_agents = []

        # 显示已添加的用户智能体
        for user_agent in st.session_state.user_agents:
            agent_id = f"user_{user_agent.replace(' ', '_')}"
            if st.checkbox(f"👤 {user_agent} (指挥官控制)", key=f"select_{agent_id}"):
                selected_agents.append(agent_id)
                # 添加到all_agents中以便后续使用
                all_agents[agent_id] = {
                    "name": user_agent,
                    "role": f"指挥官控制 - {user_agent}",
                    "type": "user_controlled"
                }

        # 添加新用户智能体
        new_user_agent = st.text_input("添加新的指挥官控制单位:", placeholder="例如: 张三, 李四, 王五")
        if st.button("➕ 添加指挥官单位"):
            if new_user_agent and new_user_agent not in st.session_state.user_agents:
                st.session_state.user_agents.append(new_user_agent)
                st.success(f"✅ 已添加指挥官单位: {new_user_agent}")
                st.rerun()
            elif new_user_agent in st.session_state.user_agents:
                st.warning("⚠️ 该作战单位已存在")
            else:
                st.warning("⚠️ 请输入作战单位名称")
    
    with col2:
        if selected_agents:
            st.write("**发言顺序配置**:")

            turn_order = st.selectbox(
                "发言模式",
                ["轮流发言", "导调控制", "自由讨论", "自定义顺序"],
                help="选择作战单位的发言顺序模式"
            )
            
            if turn_order == "自定义顺序":
                st.write("**自定义发言顺序**:")

                # 初始化自定义顺序
                if 'custom_order' not in st.session_state:
                    st.session_state.custom_order = selected_agents.copy()

                # 确保自定义顺序包含所有选中的智能体
                if set(st.session_state.custom_order) != set(selected_agents):
                    # 添加新选中的智能体
                    for agent in selected_agents:
                        if agent not in st.session_state.custom_order:
                            st.session_state.custom_order.append(agent)
                    # 移除未选中的智能体
                    st.session_state.custom_order = [
                        agent for agent in st.session_state.custom_order
                        if agent in selected_agents
                    ]

                # 显示当前顺序并提供调整功能
                st.info("💡 使用下方按钮调整发言顺序")

                # 显示当前顺序
                for i, agent_id in enumerate(st.session_state.custom_order):
                    agent_name = all_agents[agent_id]['name']

                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        st.write(f"{i+1}. {agent_name}")

                    with col2:
                        if st.button("⬆️", key=f"up_{i}"):
                            if i > 0:
                                st.session_state.custom_order[i], st.session_state.custom_order[i-1] = \
                                    st.session_state.custom_order[i-1], st.session_state.custom_order[i]
                                st.rerun()

                    with col3:
                        if st.button("⬇️", key=f"down_{i}"):
                            if i < len(st.session_state.custom_order) - 1:
                                st.session_state.custom_order[i], st.session_state.custom_order[i+1] = \
                                    st.session_state.custom_order[i+1], st.session_state.custom_order[i]
                                st.rerun()

                    with col4:
                        if st.button("❌", key=f"remove_{i}"):
                            st.session_state.custom_order.pop(i)
                            st.rerun()

                # 保存自定义顺序到配置中
                st.session_state.simulation_config = {
                    "selected_agents": st.session_state.custom_order,
                    "max_rounds": 4,
                    "turn_order": "custom"
                }

    # 推演配置
    if selected_agents:
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 15px 0;">
            <h3 style="color: #90EE90;">⚙️ 推演配置</h3>
        </div>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        
        with col1:
            st.session_state.max_discussion_rounds = st.slider(
                "最大讨论轮数", 
                min_value=5, 
                max_value=50, 
                value=20, 
                help="设置推演的最大讨论轮数"
            )

        with col2:
            st.session_state.auto_mode = st.checkbox(
                "自动模式", 
                value=False, 
                help="启用自动轮流发言模式"
            )

    # 开始推演
    if selected_agents and selected_scenario_label:
        st.markdown("""
        <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 15px 0;">
            <h3 style="color: #90EE90;">🎬 开始军事推演</h3>
        </div>
        """, unsafe_allow_html=True)

        if st.button("🚀 启动军事推演", type="primary", use_container_width=True):
            # 重置推演状态
            st.session_state.simulation_messages = []
            st.session_state.current_round = 0
            st.session_state.simulation_started = False

            st.session_state.simulation_config = {
                "selected_agents": selected_agents,
                "max_rounds": 4,
                "turn_order": turn_order
            }
            st.session_state.simulation_phase = "discussion"
            st.rerun()

    # 显示推演界面
    if st.session_state.simulation_phase == "discussion" and selected_agents:
        show_military_demo_simulation(selected_agents, demo_agents, scenario)

def show_military_data_page():
    """显示军事风格数据管理页面"""
    
    st.markdown("""
    <div class="tactical-panel">
        <h2 style="color: #90EE90; text-align: center;">💾 作战数据管理</h2>
        <p style="text-align: center; color: #e8e8e8;">管理推演数据和历史记录</p>
    </div>
    """, unsafe_allow_html=True)
    
    st.info("💾 数据管理功能正在开发中")

def show_military_results_page():
    """显示军事风格结果页面"""
    
    st.markdown("""
    <div class="tactical-panel">
        <h2 style="color: #90EE90; text-align: center;">📊 战果分析</h2>
        <p style="text-align: center; color: #e8e8e8;">分析推演结果和决策效果</p>
    </div>
    """, unsafe_allow_html=True)
    
    st.info("📊 战果分析功能正在开发中")

def show_military_demo_simulation(selected_agents, demo_agents, scenario):
    """显示军事风格AI推演过程"""

    st.markdown("""
    <div class="tactical-panel">
        <h3 style="color: #90EE90; text-align: center;">🎭 军事战略推演进行中</h3>
    </div>
    """, unsafe_allow_html=True)

    # 添加简化的CSS样式
    st.markdown("""
    <style>
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    .streaming-cursor {
        animation: blink 1s infinite;
        color: #666;
    }
    </style>
    """, unsafe_allow_html=True)

    # 显示调试信息
    with st.expander("🔧 调试信息", expanded=False):
        st.write(f"AI功能状态: {'✅ 可用' if generate_response else '❌ 不可用'}")
        st.write(f"选择的作战单位: {selected_agents}")
        st.write(f"可用作战单位: {list(demo_agents.keys())}")
        if llm_config:
            st.write(f"API配置: {llm_config.is_configured()}")

    # 更新一些默认值（如果需要）
    if 'decision_maker' not in st.session_state:
        st.session_state.decision_maker = "trump"  # 特朗普做最终决策

    # 显示推演状态和阶段
    phase_names = {
        "discussion": "🗣️ 讨论阶段",
        "summary": "📋 总结阶段",
        "decision": "⚖️ 决策阶段",
        "completed": "✅ 推演完成"
    }

    current_phase_name = phase_names.get(st.session_state.simulation_phase, "🗣️ 讨论阶段")

    if st.session_state.simulation_messages:
        col1, col2 = st.columns(2)
        with col1:
            st.info(f"📊 当前进度：第 {st.session_state.current_round} 轮发言，共 {len(st.session_state.simulation_messages)} 条发言记录")
        with col2:
            st.info(f"🎯 当前阶段：{current_phase_name}")

        # 讨论阶段进度条
        if st.session_state.simulation_phase == "discussion":
            progress = min(st.session_state.current_round / st.session_state.max_discussion_rounds, 1.0)

            # 计算每个智能体的平均发言次数
            if 'simulation_config' in st.session_state:
                config = st.session_state.simulation_config
                agent_count = len(config.get("selected_agents", []))
                rounds_per_agent = config.get("max_rounds", 4)
                current_avg_rounds = st.session_state.current_round / agent_count if agent_count > 0 else 0

                st.progress(progress, text=f"讨论进度: {st.session_state.current_round}/{st.session_state.max_discussion_rounds} 次发言")
                st.info(f"📊 平均每个作战单位已发言: {current_avg_rounds:.1f}/{rounds_per_agent} 轮")
            else:
                st.progress(progress, text=f"讨论进度: {st.session_state.current_round}/{st.session_state.max_discussion_rounds}")

            if st.session_state.current_round >= st.session_state.max_discussion_rounds:
                st.warning("🎯 讨论轮次已满，准备进入总结阶段")
    else:
        st.info("🎬 点击'开始推演'按钮开始AI对话")

    # 显示已有消息
    for msg in st.session_state.simulation_messages:
        # 处理不同类型的发言者
        if msg["speaker"] in demo_agents:
            agent_name = demo_agents[msg["speaker"]]["name"]
            avatar = msg["speaker"]
        elif msg["speaker"] == "user":
            agent_name = "指挥官"
            avatar = "user"
        elif msg["speaker"] == "ai_expert":
            agent_name = "AI专家"
            avatar = "assistant"
        elif msg["speaker"].startswith("user_"):
            # 用户自定义智能体
            agent_name = msg["speaker"].replace("user_", "").replace("_", " ")
            avatar = "user"
        else:
            agent_name = msg["speaker"]
            avatar = "assistant"

        with st.chat_message(avatar):
            st.write(f"**{agent_name}** - {msg['timestamp']}")
            st.write(msg["content"])

    # 推演设置
    col_set1, col_set2 = st.columns(2)
    with col_set1:
        enable_stream = st.checkbox("🌊 启用流式输出", value=True, help="AI回复时逐词显示，更有真实感")
    with col_set2:
        auto_continue = st.checkbox("🔄 自动轮流发言", value=False, help="AI作战单位自动轮流发言，指挥官控制单位会暂停等待输入")

    # 流式输出设置
    if enable_stream:
        st.caption("💡 流式输出已启用，AI回复时会逐词显示")

    # 自动轮流发言处理（仅在讨论阶段）
    if auto_continue and len(st.session_state.simulation_messages) > 0 and st.session_state.simulation_phase == "discussion":
        st.info("🔄 自动轮流发言模式已启用")

        # 检查是否到达最大轮数
        if st.session_state.current_round >= st.session_state.max_discussion_rounds:
            st.warning("🎯 讨论轮次已满，将自动进入总结阶段")
            st.session_state.simulation_phase = "summary"
            st.rerun()
        else:
            # 获取正确的智能体顺序（支持自定义顺序）
            final_agents = selected_agents
            if 'simulation_config' in st.session_state:
                config_agents = st.session_state.simulation_config.get("selected_agents", selected_agents)
                if config_agents:
                    final_agents = config_agents
                    # 调试信息
                    if st.session_state.get('show_context_debug', False):
                        st.info(f"🔄 使用自定义顺序: {[demo_agents.get(a, {}).get('name', a) for a in final_agents]}")

            # 确定当前发言者
            if final_agents:
                current_speaker_index = st.session_state.current_round % len(final_agents)
                current_speaker = final_agents[current_speaker_index]

                # 跳过导调（除了第一次）
                if current_speaker == "moderator" and st.session_state.current_round > 0:
                    st.session_state.current_round += 1
                    current_speaker_index = st.session_state.current_round % len(final_agents)
                    current_speaker = final_agents[current_speaker_index]

                if current_speaker in demo_agents:
                    next_speaker_name = demo_agents[current_speaker]["name"]
                elif current_speaker.startswith("user_"):
                    next_speaker_name = current_speaker.replace("user_", "").replace("_", " ")
                else:
                    next_speaker_name = current_speaker

                st.write(f"👉 下一位发言: **{next_speaker_name}**")

                # 检查是否是用户智能体
                if current_speaker.startswith("user_"):
                    # 用户智能体，暂停自动模式，显示输入界面
                    st.warning(f"⏸️ 轮到指挥官控制单位 **{next_speaker_name}**，自动模式暂停")
                    # 调用用户输入处理函数
                    handle_military_user_agent_input(current_speaker, demo_agents, scenario)
                else:
                    # AI智能体，自动发言
                    st.success(f"🤖 AI作战单位 **{next_speaker_name}** 将在3秒后自动发言")

                    # 初始化自动发言时间
                    if 'last_auto_speak_time' not in st.session_state:
                        st.session_state.last_auto_speak_time = 0

                    import time
                    current_time = time.time()

                    # 检查是否到了自动发言时间
                    if current_time - st.session_state.last_auto_speak_time >= 3:
                        st.session_state.last_auto_speak_time = current_time
                        # 自动执行下一轮发言
                        next_round_military_simulation(selected_agents, demo_agents, scenario, use_stream=enable_stream)
                        st.rerun()
                    else:
                        # 显示倒计时
                        remaining = 3 - (current_time - st.session_state.last_auto_speak_time)
                        st.info(f"⏱️ {remaining:.1f}秒后自动发言...")
                        time.sleep(0.5)
                        st.rerun()

    # 控制按钮区域
    st.markdown("---")

    # 根据阶段显示不同的控制按钮
    if st.session_state.simulation_phase == "discussion":
        # 讨论阶段按钮
        col1, col2, col3 = st.columns(3)

        with col1:
            # 检查是否可以开始推演
            can_start = len(st.session_state.simulation_messages) == 0

            if st.button("🎬 开始推演", disabled=not can_start, use_container_width=True):
                start_military_simulation(selected_agents, demo_agents, scenario, enable_stream)
                st.rerun()

        with col2:
            # 修复：确保current_round存在
            if 'current_round' not in st.session_state:
                st.session_state.current_round = 0
                
            if st.button("➡️ 下一轮发言", disabled=len(st.session_state.simulation_messages) == 0, use_container_width=True):
                next_round_military_simulation(selected_agents, demo_agents, scenario, use_stream=enable_stream)
                st.rerun()

        with col3:
            if st.button("📋 进入总结", disabled=st.session_state.current_round < 3, use_container_width=True):
                st.session_state.simulation_phase = "summary"
                st.rerun()

    elif st.session_state.simulation_phase == "summary":
        # 总结阶段按钮
        col1, col2 = st.columns(2)

        with col1:
            if st.button("📊 生成讨论总结", use_container_width=True):
                generate_military_discussion_summary(selected_agents, demo_agents, scenario)
                st.rerun()

        with col2:
            if st.button("⚖️ 进入决策阶段", use_container_width=True):
                st.session_state.simulation_phase = "decision"
                st.rerun()

    elif st.session_state.simulation_phase == "decision":
        # 决策阶段按钮
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🎯 生成最终决策", use_container_width=True):
                generate_military_final_decision(selected_agents, demo_agents, scenario)
                st.rerun()

        with col2:
            if st.button("✅ 完成推演", use_container_width=True):
                st.session_state.simulation_phase = "completed"
                st.rerun()

    # 重置按钮
    if st.button("🔄 重置推演", use_container_width=True):
        reset_military_simulation()

def start_military_simulation(selected_agents, demo_agents, scenario, use_stream=True):
    """开始军事推演"""

    print(f"🎬 开始军事推演: {scenario['title']}")
    print(f"参与作战单位: {selected_agents}")
    print(f"流式输出: {use_stream}")

    # 修复：确保所有必要的状态变量都被正确初始化
    st.session_state.simulation_started = True
    st.session_state.current_scenario = scenario
    st.session_state.current_agents = selected_agents
    
    # 确保关键状态变量存在
    if 'simulation_messages' not in st.session_state:
        st.session_state.simulation_messages = []
    if 'current_round' not in st.session_state:
        st.session_state.current_round = 0
    if 'simulation_phase' not in st.session_state:
        st.session_state.simulation_phase = "discussion"

    # 导调开场
    if use_stream:
        # 使用流式输出
        with st.chat_message("moderator"):
            moderator_message = stream_military_agent_response_simple(
                "moderator",
                demo_agents.get("moderator", demo_agents[list(demo_agents.keys())[0]]),
                scenario,
                f"作为军事推演主持人，请介绍推演场景：{scenario['title']}，并说明讨论的重点。",
                []
            )
    else:
        # 普通输出
        moderator_message = generate_military_agent_response(
            "moderator",
            demo_agents.get("moderator", demo_agents[list(demo_agents.keys())[0]]),
            scenario,
            f"作为军事推演主持人，请介绍推演场景：{scenario['title']}，并说明讨论的重点。",
            [],
            stream=False
        )

        # 使用chat_message显示
        with st.chat_message("moderator"):
            st.write(f"**军事推演导调** - {datetime.now().strftime('%H:%M:%S')}")
            st.write(moderator_message)

    print(f"导调消息: {moderator_message}")
    add_military_message("moderator", moderator_message)

def next_round_military_simulation(selected_agents, demo_agents, scenario, use_stream=True):
    """下一轮军事发言"""

    # 优先使用配置中的智能体顺序（支持自定义顺序）
    if 'simulation_config' in st.session_state:
        config_agents = st.session_state.simulation_config.get("selected_agents", selected_agents)
        if config_agents:
            selected_agents = config_agents
            print(f"🔄 使用配置中的作战单位顺序: {[demo_agents.get(a, {}).get('name', a) for a in selected_agents]}")

    if not selected_agents:
        print("❌ 没有选择的作战单位")
        return

    # 检查是否应该进入总结阶段
    if st.session_state.current_round >= st.session_state.max_discussion_rounds:
        st.session_state.simulation_phase = "summary"
        st.info("🎯 讨论轮次已满，自动进入总结阶段")
        return

    # 确定下一个发言者
    current_round = st.session_state.current_round
    speaker_id = selected_agents[current_round % len(selected_agents)]

    print(f"🎤 第{current_round + 1}轮发言: {speaker_id}")

    # 跳过导调（已经在开场发言）
    if speaker_id == "moderator" and current_round > 0:
        st.session_state.current_round += 1
        speaker_id = selected_agents[st.session_state.current_round % len(selected_agents)]
        print(f"跳过导调，改为: {speaker_id}")

    # 检查是否是用户自定义智能体
    if speaker_id.startswith("user_"):
        # 用户自定义智能体，需要用户输入
        handle_military_user_agent_input(speaker_id, demo_agents, scenario)
        return

    # 检查智能体是否存在
    if speaker_id not in demo_agents:
        print(f"❌ 作战单位 {speaker_id} 不存在")
        return

    # 生成发言
    context = get_military_conversation_context(demo_agents)

    if use_stream:
        # 使用流式输出
        with st.chat_message(speaker_id):
            agent_response = stream_military_agent_response_simple(
                speaker_id,
                demo_agents[speaker_id],
                scenario,
                f"基于当前讨论，请从{demo_agents[speaker_id]['role']}的角度分析{scenario['title']}情况并提出建议。",
                context
            )
    else:
        # 普通输出
        agent_response = generate_military_agent_response(
            speaker_id,
            demo_agents[speaker_id],
            scenario,
            f"基于当前讨论，请从{demo_agents[speaker_id]['role']}的角度分析{scenario['title']}情况并提出建议。",
            context,
            stream=False
        )

        # 即使是普通输出也显示在聊天框中
        with st.chat_message(speaker_id):
            st.write(f"**{demo_agents[speaker_id]['name']}** - {datetime.now().strftime('%H:%M:%S')}")
            st.write(agent_response)

    print(f"作战单位回应: {agent_response[:100]}...")
    add_military_message(speaker_id, agent_response)
    st.session_state.current_round += 1

def generate_military_agent_response(agent_id, agent_info, scenario, prompt, context, stream=False):
    """生成军事智能体回应"""

    # 调试信息
    print(f"🤖 生成 {agent_info['name']} 的军事回应...")
    print(f"generate_response 函数状态: {generate_response is not None}")
    print(f"流式输出: {stream}")

    if not generate_response:
        print("⚠️ AI功能不可用，返回演示内容")
        return f"[演示模式] 我是{agent_info['name']}，{agent_info['role']}。针对{scenario['title']}，我认为需要仔细分析当前军事形势，制定合适的战术应对策略。"

    # 构建军事风格的提示
    military_prompt = f"""
作为{agent_info['name']}，{agent_info['role']}，请从军事战略角度分析以下情况：

场景：{scenario['title']}
背景：{scenario.get('context', '')}

当前讨论背景：
{context}

请从您的专业角度，结合军事战略思维，分析当前态势并提出战术建议。
要求：
1. 体现军事专业性和战略思维
2. 考虑战术可行性和风险评估
3. 语言要专业、严谨、有说服力
4. 体现您的角色特点和专业领域

请开始您的分析：
"""

    try:
        if stream:
            return generate_response([{"role": "user", "content": military_prompt}], stream=True)
        else:
            return generate_response([{"role": "user", "content": military_prompt}])
    except Exception as e:
        print(f"❌ AI生成失败: {e}")
        return f"[AI生成失败] 我是{agent_info['name']}，{agent_info['role']}。针对{scenario['title']}，我认为需要仔细分析当前军事形势，制定合适的战术应对策略。"

def stream_military_agent_response_simple(agent_id, agent_info, scenario, prompt, context):
    """流式输出军事智能体回应"""

    try:
        response_stream = generate_military_agent_response(agent_id, agent_info, scenario, prompt, context, stream=True)
        
        if hasattr(response_stream, '__iter__'):
            # 流式输出
            message_placeholder = st.empty()
            full_response = ""
            
            for chunk in response_stream:
                if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                    full_response += chunk.choices[0].delta.content
                    message_placeholder.markdown(full_response + "▌")
            
            message_placeholder.markdown(full_response)
            return full_response
        else:
            # 非流式输出
            st.write(response_stream)
            return response_stream
            
    except Exception as e:
        print(f"❌ 流式输出失败: {e}")
        fallback_response = f"[流式输出失败] 我是{agent_info['name']}，{agent_info['role']}。针对{scenario['title']}，我认为需要仔细分析当前军事形势。"
        st.write(fallback_response)
        return fallback_response

def add_military_message(speaker, content):
    """添加军事消息到会话"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    st.session_state.simulation_messages.append({
        "speaker": speaker,
        "content": content,
        "timestamp": timestamp
    })

def handle_military_user_agent_input(speaker_id, demo_agents, scenario):
    """处理军事用户智能体输入"""
    
    # 获取用户智能体名称
    if speaker_id.startswith("user_"):
        user_agent_name = speaker_id.replace("user_", "").replace("_", " ")
    else:
        user_agent_name = speaker_id

    st.markdown(f"""
    <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 15px 0;">
        <h4 style="color: #90EE90;">👤 指挥官控制单位发言</h4>
        <p style="color: #e8e8e8;">当前轮到 <strong>{user_agent_name}</strong> 发言</p>
    </div>
    """, unsafe_allow_html=True)

    # 用户输入
    user_input = st.text_area(
        f"请输入 {user_agent_name} 的发言内容:",
        placeholder="请从军事战略角度分析当前态势并提出建议...",
        height=150
    )

    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("✅ 提交发言", use_container_width=True):
            if user_input.strip():
                add_military_message(speaker_id, user_input)
                st.session_state.current_round += 1
                st.success(f"✅ {user_agent_name} 的发言已提交")
                st.rerun()
            else:
                st.warning("⚠️ 请输入发言内容")

    with col2:
        if st.button("⏭️ 跳过发言", use_container_width=True):
            st.session_state.current_round += 1
            st.info(f"⏭️ {user_agent_name} 的发言已跳过")
            st.rerun()

def get_military_conversation_context(demo_agents=None):
    """获取军事对话上下文"""
    if not st.session_state.simulation_messages:
        return "这是推演的开始，还没有其他发言。"

    # 获取最近的几条消息作为上下文
    recent_messages = st.session_state.simulation_messages[-5:]  # 最近5条

    context_parts = []
    for msg in recent_messages:
        if demo_agents and msg["speaker"] in demo_agents:
            speaker_name = demo_agents[msg["speaker"]]["name"]
        elif msg["speaker"].startswith("user_"):
            speaker_name = msg["speaker"].replace("user_", "").replace("_", " ")
        else:
            speaker_name = msg["speaker"]

        context_parts.append(f"{speaker_name}: {msg['content']}")

    return "\n".join(context_parts)

def generate_military_discussion_summary(selected_agents, demo_agents, scenario):
    """生成军事讨论总结"""
    
    st.markdown("""
    <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 15px 0;">
        <h4 style="color: #90EE90;">📊 生成军事讨论总结</h4>
    </div>
    """, unsafe_allow_html=True)

    if not st.session_state.simulation_messages:
        st.warning("⚠️ 没有讨论记录，无法生成总结")
        return

    # 构建总结提示
    context = get_military_conversation_context(demo_agents)
    summary_prompt = f"""
作为军事推演主持人，请对以下军事战略讨论进行专业总结：

场景：{scenario['title']}
讨论背景：{context}

请从军事战略角度，总结：
1. 各作战单位的主要观点和战术建议
2. 关键的战略考虑因素
3. 潜在的战术风险和机会
4. 下一步行动建议

请生成专业的军事讨论总结：
"""

    try:
        with st.spinner("正在生成军事讨论总结..."):
            summary = generate_response([{"role": "user", "content": summary_prompt}])
            
            with st.chat_message("moderator"):
                st.write(f"**军事推演主持人 - 讨论总结** - {datetime.now().strftime('%H:%M:%S')}")
                st.write(summary)
            
            add_military_message("moderator", f"[讨论总结] {summary}")
            st.success("✅ 军事讨论总结已生成")
            
    except Exception as e:
        st.error(f"❌ 生成总结失败: {e}")

def generate_military_final_decision(selected_agents, demo_agents, scenario):
    """生成军事最终决策"""
    
    st.markdown("""
    <div style="background: rgba(26,47,26,0.8); padding: 15px; border-radius: 10px; border: 2px solid #90EE90; margin: 15px 0;">
        <h4 style="color: #90EE90;">🎯 生成军事最终决策</h4>
    </div>
    """, unsafe_allow_html=True)

    if not st.session_state.simulation_messages:
        st.warning("⚠️ 没有讨论记录，无法生成决策")
        return

    # 构建决策提示
    context = get_military_conversation_context(demo_agents)
    decision_prompt = f"""
作为军事战略决策者，基于以下讨论，请制定最终的军事战略决策：

场景：{scenario['title']}
讨论背景：{context}

请制定一个明确的军事战略决策，包括：
1. 总体战略目标
2. 具体战术行动计划
3. 资源配置建议
4. 风险评估和应对措施
5. 时间表和里程碑

请生成专业的军事战略决策：
"""

    try:
        with st.spinner("正在生成军事战略决策..."):
            decision = generate_response([{"role": "user", "content": decision_prompt}])
            
            with st.chat_message("moderator"):
                st.write(f"**军事战略决策** - {datetime.now().strftime('%H:%M:%S')}")
                st.write(decision)
            
            add_military_message("moderator", f"[最终决策] {decision}")
            st.success("✅ 军事战略决策已生成")
            
    except Exception as e:
        st.error(f"❌ 生成决策失败: {e}")

def reset_military_simulation():
    """重置军事推演"""
    # 修复：确保所有状态变量都被正确重置
    st.session_state.simulation_messages = []
    st.session_state.current_round = 0
    st.session_state.simulation_started = False
    st.session_state.auto_mode = False
    st.session_state.simulation_phase = "discussion"

    # 清理其他相关状态
    if 'simulation_config' in st.session_state:
        del st.session_state.simulation_config
    if 'current_scenario' in st.session_state:
        del st.session_state.current_scenario
    if 'current_agents' in st.session_state:
        del st.session_state.current_agents

    # 清理推演相关的临时状态
    if 'current_agent_index' in st.session_state:
        del st.session_state.current_agent_index
    if 'discussion_summary' in st.session_state:
        del st.session_state.discussion_summary
    if 'final_decision' in st.session_state:
        del st.session_state.final_decision

    st.success("🔄 军事推演已重置")

if __name__ == "__main__":
    main() 