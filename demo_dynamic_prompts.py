#!/usr/bin/env python3
"""
动态提示词生成演示
展示系统如何根据不同场景动态生成提示词
"""

def demo_dynamic_prompts():
    """演示动态提示词生成"""
    
    print("🎯 战略推演系统 - 动态提示词生成演示")
    print("=" * 60)
    print()
    
    # 演示场景
    scenarios = [
        {
            "title": "台海危机应对策略", 
            "context": "情报显示台海周边军事活动增加，美国应如何应对？",
            "type": "军事危机"
        },
        {
            "title": "中美贸易战升级",
            "context": "中国对美国农产品征收新关税，美国应如何回应？", 
            "type": "贸易争端"
        },
        {
            "title": "网络安全威胁应对",
            "context": "发现大规模网络攻击，疑似来自敌对国家，美国应如何应对？",
            "type": "网络安全"
        }
    ]
    
    # 智能体角色
    agents = {
        "trump": "唐纳德·特朗普 (现任总统)",
        "biden": "乔·拜登 (前任总统)", 
        "military_expert": "军事战略专家",
        "political_expert": "政治学专家"
    }
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"📋 场景 {i}: {scenario['title']}")
        print(f"类型: {scenario['type']}")
        print(f"背景: {scenario['context']}")
        print()
        
        # 展示动态生成的决策问题
        print("⚖️ 动态生成的决策问题:")
        
        if "台海" in scenario['title'] or "台湾" in scenario['title']:
            questions = [
                f"美国是否应该军事干预{scenario['title'].replace('应对策略', '').replace('危机', '')}？",
                "如果干预，采取什么具体方式和程度？",
                "与盟友的协调策略是什么？",
                "经济和外交手段如何配合？",
                "预期结果和风险评估"
            ]
        elif "贸易" in scenario['title']:
            questions = [
                "是否应该实施新的贸易制裁措施？",
                "如何平衡经济利益和政治目标？", 
                "与其他贸易伙伴的协调策略？",
                "对国内经济的影响评估？",
                "长期贸易政策调整方向？"
            ]
        else:
            questions = [
                "应该采取什么主要行动？",
                "行动的具体实施方式和时间表？",
                "需要哪些资源和支持？",
                "潜在风险和应对措施？",
                "预期结果和成功标准？"
            ]
            
        for j, question in enumerate(questions, 1):
            print(f"  {j}. {question}")
        print()
        
        # 展示智能体提示词片段
        print("🤖 智能体提示词示例 (特朗普):")
        trump_prompt = f"""你是唐纳德·特朗普，美国第47任总统，现任总统，智囊团最高决策者。

在关于"{scenario['title']}"的智囊团讨论中，你要：
1. 明确回应其他成员的观点（赞同/反对）
2. 从美国利益角度质疑不合理建议  
3. 提出强硬但实用的解决方案
4. 与其他观点进行直接辩论

背景情况：{scenario['context']}

请积极参与辩论，不要只是重复立场。"""
        
        # 只显示前200个字符
        print(trump_prompt[:200] + "...")
        print()
        
        print("-" * 60)
        print()
    
    print("✨ 主要改进:")
    print("• 🔄 从硬编码'台海危机'改为动态场景适配")
    print("• 🎯 智能体提示词根据场景自动调整")
    print("• ⚖️ 决策问题根据场景类型动态生成")
    print("• 🌐 支持任意类型的战略推演场景")
    print("• 🔧 保持完全向后兼容")
    print()
    
    print("🚀 使用方法:")
    print("1. 在Streamlit界面中选择或创建任意场景")
    print("2. 系统自动识别场景类型")
    print("3. 为每个智能体生成个性化提示词")
    print("4. 根据场景生成相关决策问题")
    print("5. 开始智能体间的战略讨论")
    print()
    
    print("🎉 升级完成！系统现在支持通用战略推演场景。")

if __name__ == "__main__":
    demo_dynamic_prompts()
