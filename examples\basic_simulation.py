#!/usr/bin/env python3
"""
Basic Simulation Example

This example demonstrates how to set up and run a basic strategic simulation
using the Strategic Simulation System.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simulation.simulation_controller import Simulation<PERSON>ontroller
from agents.custom_agent import CustomAgent
import json


def main():
    """Run a basic simulation example."""
    
    print("🎯 Strategic Simulation System - Basic Example")
    print("=" * 60)
    
    # Initialize simulation controller
    print("📋 Initializing simulation controller...")
    
    try:
        controller = SimulationController("config/simulation_config.json")
        
        # Load agent configurations
        with open("config/agents_config.json", 'r', encoding='utf-8') as f:
            agents_config = json.load(f)
        
        # Initialize agents
        result = controller.initialize_agents(agents_config, "config/knowledge_base")
        print(f"✅ Loaded {result['total_count']} agents")
        
    except Exception as e:
        print(f"❌ Failed to initialize system: {e}")
        return
    
    # Define a simple scenario
    scenario = {
        "title": "Example Strategic Crisis",
        "description": "A hypothetical strategic crisis requiring immediate response",
        "initial_context": "Intelligence reports indicate a potential threat to national security. The situation requires careful analysis and strategic decision-making.",
        "key_considerations": [
            "National security implications",
            "Economic impact assessment", 
            "International relations effects",
            "Domestic political considerations"
        ]
    }
    
    # Configure simulation
    simulation_config = {
        "max_discussion_rounds": 5,
        "round_time_limit": 300,  # 5 minutes per round
        "enable_user_participation": False,  # Automated for this example
        "auto_save_conversations": True
    }
    
    print(f"\n🎯 Running simulation: {scenario['title']}")
    print(f"📊 Configuration: {simulation_config['max_discussion_rounds']} rounds, {len(result['initialized_agents'])} agents")
    
    try:
        # Run the simulation
        simulation_result = controller.run_simulation(scenario, simulation_config)
        
        print("\n✅ Simulation completed successfully!")
        
        # Display results summary
        print_simulation_summary(simulation_result)
        
        # Export results
        output_path = controller.export_simulation_results()
        print(f"\n💾 Results exported to: {output_path}")
        
    except Exception as e:
        print(f"❌ Simulation failed: {e}")


def print_simulation_summary(simulation_result):
    """Print a summary of simulation results."""
    
    print("\n📊 Simulation Summary")
    print("-" * 40)
    
    # Basic information
    print(f"Scenario: {simulation_result['scenario']['title']}")
    print(f"Status: {simulation_result.get('status', 'Unknown')}")
    print(f"Phases Completed: {len(simulation_result.get('phases_completed', []))}")
    
    # Calculate duration
    if 'start_time' in simulation_result and 'end_time' in simulation_result:
        from datetime import datetime
        start = datetime.fromisoformat(simulation_result['start_time'])
        end = datetime.fromisoformat(simulation_result['end_time'])
        duration = end - start
        print(f"Duration: {duration}")
    
    # Final decision summary
    if 'final_decision' in simulation_result:
        decision = simulation_result['final_decision']
        print(f"\n🎯 Final Decision by {decision.get('decision_maker', 'Unknown')}:")
        decision_text = decision.get('decision', '')
        # Show first 300 characters
        if len(decision_text) > 300:
            print(f"  {decision_text[:300]}...")
        else:
            print(f"  {decision_text}")
    
    # Analysis scores
    if 'analysis_results' in simulation_result:
        analysis = simulation_result['analysis_results']
        if 'overall_scores' in analysis:
            scores = analysis['overall_scores']
            print(f"\n📈 Quality Scores:")
            print(f"  Decision Quality: {scores.get('decision_quality', 0):.1f}/10")
            print(f"  Process Effectiveness: {scores.get('process_effectiveness', 0):.1f}/10")
            print(f"  Overall Score: {scores.get('overall_simulation', 0):.1f}/10")
    
    # Participation summary
    if 'phases_completed' in simulation_result:
        print(f"\n👥 Participation Summary:")
        total_contributions = 0
        for phase in simulation_result['phases_completed']:
            if 'contributions' in phase:
                total_contributions += phase['contributions']
        print(f"  Total Contributions: {total_contributions}")


def create_custom_agent_example():
    """Example of creating a custom agent."""
    
    print("\n👤 Creating Custom Agent Example")
    print("-" * 40)
    
    # Define custom agent configuration
    custom_config = {
        "name": "Strategic Advisor",
        "role": "Senior Strategic Consultant",
        "type": "custom",
        "persona": {
            "background_summary": "Former military officer with 20 years of strategic planning experience",
            "expertise": ["Strategic planning", "Risk assessment", "Crisis management", "International relations"],
            "values": ["National security", "Careful analysis", "Practical solutions", "Team collaboration"],
            "communication_style": "Direct, analytical, solution-focused",
            "decision_principles": ["Evidence-based decisions", "Consider all stakeholders", "Plan for contingencies"]
        }
    }
    
    # Create the custom agent
    try:
        custom_agent = CustomAgent("Strategic Advisor", custom_config)
        print(f"✅ Created custom agent: {custom_agent.name}")
        print(f"   Role: {custom_agent.role}")
        print(f"   Expertise: {', '.join(custom_config['persona']['expertise'])}")
        
        return custom_agent
        
    except Exception as e:
        print(f"❌ Failed to create custom agent: {e}")
        return None


def demonstrate_agent_interaction():
    """Demonstrate agent interaction capabilities."""
    
    print("\n🤖 Agent Interaction Example")
    print("-" * 40)
    
    # Create a custom agent
    custom_agent = create_custom_agent_example()
    
    if custom_agent:
        # Test scenario analysis
        test_scenario = "A cyber attack has been detected on critical infrastructure. What should be the immediate response?"
        
        print(f"\n📋 Test Scenario: {test_scenario}")
        
        try:
            analysis = custom_agent.analyze_strategic_situation(test_scenario)
            print(f"\n💭 Agent Analysis:")
            print(f"  {analysis[:400]}..." if len(analysis) > 400 else f"  {analysis}")
            
        except Exception as e:
            print(f"❌ Failed to get agent analysis: {e}")


if __name__ == "__main__":
    # Check if configuration files exist
    config_files = [
        "config/simulation_config.json",
        "config/agents_config.json"
    ]
    
    missing_files = [f for f in config_files if not Path(f).exists()]
    
    if missing_files:
        print("❌ Missing configuration files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure configuration files are in place before running the example.")
        sys.exit(1)
    
    # Run the example
    main()
    
    # Demonstrate additional features
    demonstrate_agent_interaction()
    
    print("\n🎉 Example completed!")
    print("💡 Try running the web interface with: python main.py web")
    print("💡 Or use the CLI with: python main.py cli --help")
