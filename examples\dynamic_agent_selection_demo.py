#!/usr/bin/env python3
"""
Dynamic Agent Selection Demo

Demonstrates how to dynamically select agents and configure turn orders
for strategic simulations.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simulation.simulation_controller import SimulationController
import json


def demo_dynamic_selection():
    """Demonstrate dynamic agent selection and turn order configuration."""
    
    print("🎯 Dynamic Agent Selection Demo")
    print("=" * 60)
    
    # Initialize simulation controller
    try:
        controller = SimulationController("config/simulation_config.json")
        
        # Load agent configurations
        with open("config/agents_config.json", 'r', encoding='utf-8') as f:
            agents_config = json.load(f)
        
        # Initialize agents
        result = controller.initialize_agents(agents_config, "config/knowledge_base")
        print(f"✅ Loaded {result['total_count']} agents")
        
        available_agents = list(controller.agents.keys())
        print(f"📋 Available agents: {', '.join(available_agents)}")
        
    except Exception as e:
        print(f"❌ Failed to initialize system: {e}")
        return
    
    # Demo scenarios with different agent configurations
    demo_scenarios = [
        {
            "name": "Trump + Experts Only",
            "selected_agents": ["trump", "political_expert", "military_expert"],
            "turn_order": {
                "mode": "Custom Order",
                "custom_order": ["trump", "military_expert", "political_expert"],
                "settings": {"enforce_turn_order": True}
            }
        },
        {
            "name": "Biden vs Trump Debate",
            "selected_agents": ["biden", "trump", "moderator"],  # Assuming Biden agent exists
            "turn_order": {
                "mode": "Moderator Controlled",
                "settings": {"allow_interruptions": True, "max_consecutive_turns": 2}
            }
        },
        {
            "name": "Expert Panel Discussion",
            "selected_agents": ["political_expert", "military_expert", "economic_expert", "moderator"],
            "turn_order": {
                "mode": "Round Robin",
                "settings": {"enforce_turn_order": False, "enable_side_conversations": True}
            }
        }
    ]
    
    # Define test scenario
    scenario = {
        "title": "Dynamic Selection Test Scenario",
        "description": "Testing dynamic agent selection and turn order configuration",
        "initial_context": "This is a test scenario to demonstrate dynamic agent selection capabilities.",
        "key_considerations": ["Agent selection", "Turn order", "Discussion flow"]
    }
    
    simulation_config = {
        "max_discussion_rounds": 3,
        "round_time_limit": 180,
        "enable_user_participation": False,
        "auto_save_conversations": True
    }
    
    # Run demo scenarios
    for i, demo_config in enumerate(demo_scenarios, 1):
        print(f"\n🎬 Demo {i}: {demo_config['name']}")
        print("-" * 50)
        
        # Filter available agents
        selected_agents = [agent for agent in demo_config['selected_agents'] 
                          if agent in available_agents]
        
        if not selected_agents:
            print(f"⚠️ No available agents for this demo, skipping...")
            continue
        
        print(f"👥 Selected agents: {', '.join(selected_agents)}")
        print(f"📋 Turn order mode: {demo_config['turn_order']['mode']}")
        
        if demo_config['turn_order'].get('custom_order'):
            custom_order = [agent for agent in demo_config['turn_order']['custom_order'] 
                           if agent in selected_agents]
            print(f"🔄 Speaking order: {' → '.join(custom_order)}")
        
        try:
            # Run simulation with dynamic configuration
            simulation_result = controller.run_simulation(
                scenario=scenario,
                simulation_config=simulation_config,
                selected_agents=selected_agents,
                turn_order_config=demo_config['turn_order']
            )
            
            print(f"✅ Demo {i} completed successfully!")
            print(f"📊 Participants: {', '.join(simulation_result.get('participating_agents', []))}")
            
            # Show some results
            if 'phases_completed' in simulation_result:
                print(f"📈 Phases completed: {len(simulation_result['phases_completed'])}")
            
        except Exception as e:
            print(f"❌ Demo {i} failed: {e}")
        
        print()  # Add spacing between demos
    
    print("🎉 Dynamic selection demo completed!")


def demo_web_interface_features():
    """Show what's available in the web interface."""
    
    print("\n🖥️ Web Interface Features")
    print("=" * 60)
    
    print("""
The web interface now supports:

🤖 **Dynamic Agent Selection:**
   • Visual agent selection with checkboxes
   • Agent details and role information
   • Custom agent creation and templates
   • Real-time agent availability

📋 **Turn Order Configuration:**
   • Round Robin mode
   • Moderator Controlled mode  
   • Free Discussion mode
   • Custom Order with drag-and-drop

⚙️ **Advanced Settings:**
   • Enforce turn order toggle
   • Allow interruptions option
   • Side conversations enable/disable
   • Max consecutive turns per agent
   • Turn time limits
   • Discussion timeout settings

🎯 **Quick Templates:**
   • Biden Agent template
   • Business Expert template
   • Academic Expert template
   • Custom agent builder

📊 **Real-time Configuration:**
   • Live preview of speaking order
   • Configuration summary
   • Validation and error checking
   • Save/load configurations

To try these features:
1. Run: python main.py web
2. Go to "Setup Simulation" page
3. Explore the "Agent Selection & Configuration" section
4. Try different turn order modes
5. Create custom agents with templates
    """)


def demo_cli_features():
    """Show what's available in the CLI."""
    
    print("\n💻 CLI Features")
    print("=" * 60)
    
    print("""
The CLI now supports:

🤖 **Interactive Agent Selection:**
   python main.py cli run
   • Shows all available agents with roles
   • Number-based selection (1,2,3 or 'all')
   • Validates selections

📋 **Turn Order Configuration:**
   • Interactive mode selection
   • Custom order configuration
   • Advanced settings prompts

🎯 **Template-based Agent Creation:**
   python main.py cli create-agent --template biden
   python main.py cli create-agent --template business
   python main.py cli create-agent --template academic

📚 **Batch Knowledge Upload:**
   python main.py cli upload-knowledge --agent trump --directory docs/
   python main.py cli upload-knowledge --agents trump,biden --files doc1.txt,doc2.md

⚙️ **Advanced Configuration:**
   • Turn order enforcement
   • Interruption settings
   • Time limits
   • Discussion timeouts

Example workflow:
1. python main.py cli init
2. python main.py cli create-agent --template biden
3. python main.py cli run
4. Select agents: 1,2,3
5. Configure turn order: Custom Order
6. Set speaking order
7. Start simulation
    """)


if __name__ == "__main__":
    # Check if configuration files exist
    config_files = [
        "config/simulation_config.json",
        "config/agents_config.json"
    ]
    
    missing_files = [f for f in config_files if not Path(f).exists()]
    
    if missing_files:
        print("❌ Missing configuration files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure configuration files are in place before running the demo.")
        sys.exit(1)
    
    # Run demos
    demo_dynamic_selection()
    demo_web_interface_features()
    demo_cli_features()
    
    print("\n🚀 Ready to try dynamic agent selection!")
    print("💡 Start with: python main.py web")
    print("💡 Or try CLI: python main.py cli run")
