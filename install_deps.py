#!/usr/bin/env python3
"""
依赖包安装脚本
"""

import subprocess
import sys

def install_package(package_name):
    """安装单个包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    """主安装函数"""
    
    print("🚀 战略推演系统 - 依赖包安装")
    print("=" * 40)
    
    # 必需的包
    required_packages = [
        "streamlit>=1.28.0",
        "openai>=1.0.0", 
        "python-dotenv>=1.0.0",
        "requests>=2.25.0"
    ]
    
    # 可选的包（用于更好的功能）
    optional_packages = [
        "tiktoken",  # 精确token计算
    ]
    
    print("📦 安装必需依赖包...")
    success_count = 0
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n✅ 必需包安装完成: {success_count}/{len(required_packages)}")
    
    print("\n📦 安装可选依赖包...")
    optional_success = 0
    
    for package in optional_packages:
        if install_package(package):
            optional_success += 1
    
    print(f"\n✅ 可选包安装完成: {optional_success}/{len(optional_packages)}")
    
    # 测试导入
    print("\n🧪 测试依赖包...")
    
    test_results = {}
    
    # 测试必需包
    try:
        import streamlit
        test_results["streamlit"] = "✅"
    except ImportError:
        test_results["streamlit"] = "❌"
    
    try:
        import openai
        test_results["openai"] = "✅"
    except ImportError:
        test_results["openai"] = "❌"
    
    try:
        import dotenv
        test_results["python-dotenv"] = "✅"
    except ImportError:
        test_results["python-dotenv"] = "❌"
    
    try:
        import requests
        test_results["requests"] = "✅"
    except ImportError:
        test_results["requests"] = "❌"
    
    # 测试可选包
    try:
        import tiktoken
        test_results["tiktoken"] = "✅ (可选)"
    except ImportError:
        test_results["tiktoken"] = "⚠️ (可选，将使用备用方案)"
    
    print("\n📊 依赖包状态:")
    for package, status in test_results.items():
        print(f"  {package}: {status}")
    
    # 总结
    required_ok = all(
        test_results.get(pkg.split(">=")[0], "❌") == "✅" 
        for pkg in ["streamlit", "openai", "python-dotenv", "requests"]
    )
    
    if required_ok:
        print("\n🎉 所有必需依赖已安装，系统可以正常运行！")
        print("\n🚀 现在可以启动系统:")
        print("   python launch.py")
        print("   或")
        print("   streamlit run main_app.py")
    else:
        print("\n⚠️ 部分必需依赖安装失败，请手动安装:")
        failed_packages = [
            pkg for pkg in ["streamlit", "openai", "python-dotenv", "requests"]
            if test_results.get(pkg, "❌") == "❌"
        ]
        for pkg in failed_packages:
            print(f"   pip install {pkg}")

if __name__ == "__main__":
    main()
