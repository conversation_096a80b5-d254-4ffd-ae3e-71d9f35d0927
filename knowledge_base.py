#!/usr/bin/env python3
"""
智能体知识库管理系统
"""

import os
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime
import streamlit as st

class KnowledgeBase:
    """智能体知识库管理器"""
    
    def __init__(self, base_dir: str = "knowledge_bases"):
        """
        初始化知识库管理器
        
        Args:
            base_dir: 知识库存储目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 创建各智能体的知识库目录
        self.agent_dirs = {
            "trump": self.base_dir / "trump",
            "biden": self.base_dir / "biden", 
            "military_expert": self.base_dir / "military_expert",
            "political_expert": self.base_dir / "political_expert",
            "moderator": self.base_dir / "moderator"
        }
        
        for agent_dir in self.agent_dirs.values():
            agent_dir.mkdir(exist_ok=True)
            
        # 元数据文件
        self.metadata_file = self.base_dir / "metadata.json"
        self.load_metadata()
    
    def load_metadata(self):
        """加载知识库元数据"""
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                self.metadata = json.load(f)
        else:
            self.metadata = {
                "agents": {},
                "created_at": datetime.now().isoformat(),
                "version": "1.0"
            }
            self.save_metadata()
    
    def save_metadata(self):
        """保存知识库元数据"""
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, ensure_ascii=False, indent=2)
    
    def upload_knowledge_file(self, agent_id: str, file_content: str, 
                            file_name: str, file_type: str = "text") -> bool:
        """
        为智能体上传知识文件
        
        Args:
            agent_id: 智能体ID
            file_content: 文件内容
            file_name: 文件名
            file_type: 文件类型
            
        Returns:
            是否上传成功
        """
        try:
            if agent_id not in self.agent_dirs:
                return False
            
            # 生成文件ID
            file_id = hashlib.md5(f"{agent_id}_{file_name}_{datetime.now()}".encode()).hexdigest()[:8]
            
            # 保存文件
            agent_dir = self.agent_dirs[agent_id]
            file_path = agent_dir / f"{file_id}_{file_name}"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_content)
            
            # 更新元数据
            if agent_id not in self.metadata["agents"]:
                self.metadata["agents"][agent_id] = {"files": [], "updated_at": ""}
            
            file_info = {
                "file_id": file_id,
                "file_name": file_name,
                "file_type": file_type,
                "file_path": str(file_path),
                "size": len(file_content),
                "uploaded_at": datetime.now().isoformat(),
                "summary": self._generate_summary(file_content)
            }
            
            self.metadata["agents"][agent_id]["files"].append(file_info)
            self.metadata["agents"][agent_id]["updated_at"] = datetime.now().isoformat()
            
            self.save_metadata()
            return True
            
        except Exception as e:
            print(f"上传知识文件失败: {e}")
            return False
    
    def _generate_summary(self, content: str, max_length: int = 100) -> str:
        """生成内容摘要"""
        if len(content) <= max_length:
            return content
        
        # 简单的摘要生成：取前几句话
        sentences = content.split('。')
        summary = ""
        for sentence in sentences:
            if len(summary + sentence) <= max_length:
                summary += sentence + "。"
            else:
                break
        
        return summary if summary else content[:max_length] + "..."
    
    def get_agent_knowledge(self, agent_id: str) -> List[Dict]:
        """获取智能体的知识库文件列表"""
        if agent_id not in self.metadata["agents"]:
            return []
        
        return self.metadata["agents"][agent_id]["files"]
    
    def read_knowledge_file(self, agent_id: str, file_id: str) -> Optional[str]:
        """读取知识文件内容"""
        try:
            files = self.get_agent_knowledge(agent_id)
            for file_info in files:
                if file_info["file_id"] == file_id:
                    file_path = Path(file_info["file_path"])
                    if file_path.exists():
                        with open(file_path, 'r', encoding='utf-8') as f:
                            return f.read()
            return None
        except Exception as e:
            print(f"读取知识文件失败: {e}")
            return None
    
    def delete_knowledge_file(self, agent_id: str, file_id: str) -> bool:
        """删除知识文件"""
        try:
            if agent_id not in self.metadata["agents"]:
                return False
            
            files = self.metadata["agents"][agent_id]["files"]
            for i, file_info in enumerate(files):
                if file_info["file_id"] == file_id:
                    # 删除文件
                    file_path = Path(file_info["file_path"])
                    if file_path.exists():
                        file_path.unlink()
                    
                    # 从元数据中移除
                    files.pop(i)
                    self.metadata["agents"][agent_id]["updated_at"] = datetime.now().isoformat()
                    self.save_metadata()
                    return True
            
            return False
            
        except Exception as e:
            print(f"删除知识文件失败: {e}")
            return False
    
    def get_agent_knowledge_summary(self, agent_id: str) -> str:
        """获取智能体知识库摘要"""
        files = self.get_agent_knowledge(agent_id)
        if not files:
            return "暂无专属知识库"
        
        total_size = sum(f["size"] for f in files)
        file_types = set(f["file_type"] for f in files)
        
        summary = f"共{len(files)}个文件，"
        summary += f"总大小{total_size}字符，"
        summary += f"类型：{', '.join(file_types)}"
        
        return summary
    
    def build_agent_context(self, agent_id: str, query: str = "") -> str:
        """
        为智能体构建知识库上下文
        
        Args:
            agent_id: 智能体ID
            query: 查询关键词（用于相关性筛选）
            
        Returns:
            知识库上下文文本
        """
        files = self.get_agent_knowledge(agent_id)
        if not files:
            return ""
        
        context_parts = []
        context_parts.append(f"【{agent_id}专属知识库】")
        
        for file_info in files:
            content = self.read_knowledge_file(agent_id, file_info["file_id"])
            if content:
                # 如果有查询词，进行简单的相关性筛选
                if query and query.lower() not in content.lower():
                    continue
                
                context_parts.append(f"\n--- {file_info['file_name']} ---")
                # 限制每个文件的内容长度
                if len(content) > 1000:
                    context_parts.append(content[:1000] + "...")
                else:
                    context_parts.append(content)
        
        return "\n".join(context_parts)
    
    def get_all_agents_status(self) -> Dict:
        """获取所有智能体的知识库状态"""
        status = {}
        
        agent_names = {
            "trump": "特朗普",
            "biden": "拜登",
            "military_expert": "军事专家", 
            "political_expert": "政治专家",
            "moderator": "推演导调"
        }
        
        for agent_id, agent_name in agent_names.items():
            files = self.get_agent_knowledge(agent_id)
            status[agent_id] = {
                "name": agent_name,
                "file_count": len(files),
                "total_size": sum(f["size"] for f in files) if files else 0,
                "last_updated": self.metadata["agents"].get(agent_id, {}).get("updated_at", "从未更新"),
                "summary": self.get_agent_knowledge_summary(agent_id)
            }
        
        return status

# 全局知识库实例
knowledge_base = KnowledgeBase()

def get_knowledge_base() -> KnowledgeBase:
    """获取知识库实例"""
    return knowledge_base
