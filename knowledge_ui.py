#!/usr/bin/env python3
"""
知识库上传界面组件
"""

import streamlit as st
from knowledge_base import get_knowledge_base

def show_agent_knowledge_panel(agent_id: str, agent_name: str):
    """
    显示智能体知识库面板
    
    Args:
        agent_id: 智能体ID
        agent_name: 智能体名称
    """
    kb = get_knowledge_base()
    
    with st.expander(f"📚 {agent_name} 专属知识库", expanded=False):
        
        # 知识库状态
        files = kb.get_agent_knowledge(agent_id)
        summary = kb.get_agent_knowledge_summary(agent_id)
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.write(f"**状态**: {summary}")
            
        with col2:
            st.metric("文件数", len(files))
        
        # 上传新文件
        st.markdown("**📤 上传知识文件**")
        
        upload_method = st.radio(
            "上传方式",
            ["📝 文本输入", "📁 文件上传"],
            key=f"upload_method_{agent_id}",
            horizontal=True
        )
        
        if upload_method == "📝 文本输入":
            # 文本输入方式
            col1, col2 = st.columns([3, 1])
            
            with col1:
                file_name = st.text_input(
                    "文件名",
                    placeholder="例如：台海军事分析.txt",
                    key=f"filename_{agent_id}"
                )
            
            with col2:
                file_type = st.selectbox(
                    "类型",
                    ["文本", "分析", "报告", "资料"],
                    key=f"filetype_{agent_id}"
                )
            
            content = st.text_area(
                "内容",
                placeholder=f"输入{agent_name}的专业知识、分析报告、历史案例等...",
                height=150,
                key=f"content_{agent_id}"
            )
            
            if st.button(f"💾 保存到{agent_name}知识库", key=f"save_{agent_id}"):
                if file_name and content:
                    success = kb.upload_knowledge_file(
                        agent_id, content, file_name, file_type
                    )
                    if success:
                        st.success(f"✅ 已保存到{agent_name}的知识库")
                        st.rerun()
                    else:
                        st.error("❌ 保存失败")
                else:
                    st.warning("⚠️ 请填写文件名和内容")
        
        else:
            # 文件上传方式
            uploaded_file = st.file_uploader(
                f"选择文件上传到{agent_name}知识库",
                type=['txt', 'md', 'doc', 'pdf'],
                key=f"upload_{agent_id}"
            )
            
            if uploaded_file is not None:
                try:
                    # 读取文件内容
                    if uploaded_file.type == "text/plain":
                        content = str(uploaded_file.read(), "utf-8")
                    else:
                        content = str(uploaded_file.read(), "utf-8", errors='ignore')
                    
                    file_type = "上传文件"
                    
                    if st.button(f"💾 保存 {uploaded_file.name}", key=f"save_upload_{agent_id}"):
                        success = kb.upload_knowledge_file(
                            agent_id, content, uploaded_file.name, file_type
                        )
                        if success:
                            st.success(f"✅ {uploaded_file.name} 已保存到{agent_name}的知识库")
                            st.rerun()
                        else:
                            st.error("❌ 保存失败")
                            
                except Exception as e:
                    st.error(f"❌ 文件读取失败: {e}")
        
        # 显示现有文件
        if files:
            st.markdown("**📋 已有文件**")
            
            for file_info in files:
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 2, 1, 1])
                    
                    with col1:
                        st.write(f"📄 **{file_info['file_name']}**")
                        st.caption(file_info['summary'])
                    
                    with col2:
                        st.write(f"类型: {file_info['file_type']}")
                        st.write(f"大小: {file_info['size']} 字符")
                    
                    with col3:
                        if st.button("👁️", key=f"view_{agent_id}_{file_info['file_id']}", help="查看"):
                            content = kb.read_knowledge_file(agent_id, file_info['file_id'])
                            if content:
                                st.text_area(
                                    f"📄 {file_info['file_name']}",
                                    content,
                                    height=200,
                                    key=f"view_content_{agent_id}_{file_info['file_id']}"
                                )
                    
                    with col4:
                        if st.button("🗑️", key=f"delete_{agent_id}_{file_info['file_id']}", help="删除"):
                            if kb.delete_knowledge_file(agent_id, file_info['file_id']):
                                st.success("✅ 已删除")
                                st.rerun()
                            else:
                                st.error("❌ 删除失败")
                    
                    st.divider()

def show_knowledge_overview():
    """显示知识库总览"""
    kb = get_knowledge_base()
    status = kb.get_all_agents_status()
    
    st.markdown("### 📚 智能体知识库总览")
    
    # 统计信息
    total_files = sum(s["file_count"] for s in status.values())
    total_size = sum(s["total_size"] for s in status.values())
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("总文件数", total_files)
    with col2:
        st.metric("总内容量", f"{total_size} 字符")
    with col3:
        active_agents = sum(1 for s in status.values() if s["file_count"] > 0)
        st.metric("活跃智能体", f"{active_agents}/5")
    
    # 各智能体状态
    for agent_id, info in status.items():
        with st.container():
            col1, col2, col3, col4 = st.columns([2, 1, 1, 2])
            
            with col1:
                status_icon = "🟢" if info["file_count"] > 0 else "⚪"
                st.write(f"{status_icon} **{info['name']}**")
            
            with col2:
                st.write(f"📁 {info['file_count']} 文件")
            
            with col3:
                st.write(f"📊 {info['total_size']} 字符")
            
            with col4:
                st.write(f"🕐 {info['last_updated'][:10] if info['last_updated'] != '从未更新' else '从未更新'}")
            
            st.caption(info['summary'])
            st.divider()

def show_knowledge_management_page():
    """显示知识库管理页面"""
    st.title("📚 智能体知识库管理")
    st.markdown("**为每个智能体配置专属的知识库，提升推演的专业性和准确性**")
    
    # 总览
    show_knowledge_overview()
    
    # 各智能体知识库管理
    st.markdown("### 🎭 智能体知识库配置")
    
    agents = {
        "trump": "特朗普",
        "biden": "拜登", 
        "military_expert": "军事专家",
        "political_expert": "政治专家",
        "moderator": "推演导调"
    }
    
    # 使用标签页
    tabs = st.tabs([f"{name}" for name in agents.values()])
    
    for i, (agent_id, agent_name) in enumerate(agents.items()):
        with tabs[i]:
            show_agent_knowledge_panel(agent_id, agent_name)
    
    # 使用说明
    with st.expander("💡 使用说明", expanded=False):
        st.markdown("""
        **知识库功能说明**:
        
        1. **📤 上传知识**: 
           - 文本输入：直接输入专业知识、分析报告等
           - 文件上传：上传txt、md等文档文件
        
        2. **🎯 智能体专业化**:
           - **特朗普**: 商业案例、谈判策略、美国政策
           - **拜登**: 外交历史、国际关系、多边合作案例
           - **军事专家**: 军事理论、作战案例、武器分析
           - **政治专家**: 国际法、地缘政治、历史案例
           - **推演导调**: 推演流程、主持技巧、总结模板
        
        3. **📋 知识应用**:
           - 推演时自动加载相关知识
           - 提升AI回复的专业性
           - 基于真实案例进行分析
        
        4. **🔧 管理功能**:
           - 查看文件内容
           - 删除过时文件
           - 实时更新知识库
        """)

def get_agent_knowledge_context(agent_id: str, scenario_context: str = "") -> str:
    """
    获取智能体的知识库上下文（用于AI生成）
    
    Args:
        agent_id: 智能体ID
        scenario_context: 推演场景上下文
        
    Returns:
        知识库上下文文本
    """
    kb = get_knowledge_base()
    
    # 从场景中提取关键词
    query_keywords = ""
    if scenario_context:
        # 简单的关键词提取
        keywords = ["台海", "军事", "外交", "经济", "政治", "危机", "战略"]
        for keyword in keywords:
            if keyword in scenario_context:
                query_keywords += keyword + " "
    
    context = kb.build_agent_context(agent_id, query_keywords.strip())
    
    if context:
        return f"\n\n{context}\n\n【请基于以上专属知识库进行专业分析】"
    else:
        return ""
