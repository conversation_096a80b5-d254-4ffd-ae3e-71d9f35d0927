#!/usr/bin/env python3
"""
Strategic Simulation System - Main Entry Point

This script provides the main entry point for the strategic simulation system,
allowing users to choose between web interface and CLI interface.
"""

import sys
import os
from pathlib import Path
import argparse

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Main entry point for the strategic simulation system."""
    
    parser = argparse.ArgumentParser(
        description="Strategic Simulation System - Military Strategic Simulation with AI Agents",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py web                    # Start web interface
  python main.py cli init               # Initialize CLI system
  python main.py cli run                # Run simulation via CLI
  python main.py cli agents             # List available agents
  python main.py cli create-agent       # Create custom agent
  
For more CLI options, run: python main.py cli --help
        """
    )
    
    parser.add_argument(
        'interface',
        choices=['web', 'cli'],
        help='Interface type to use'
    )
    
    # Parse known args to allow passing remaining args to CLI
    args, remaining_args = parser.parse_known_args()
    
    if args.interface == 'web':
        start_web_interface()
    elif args.interface == 'cli':
        start_cli_interface(remaining_args)


def start_web_interface():
    """Start the Streamlit web interface."""
    
    try:
        import streamlit.web.cli as stcli
        import sys
        
        # Path to the web interface script
        web_script = Path(__file__).parent / "src" / "ui" / "web_interface.py"
        
        # Set up Streamlit arguments
        sys.argv = [
            "streamlit",
            "run",
            str(web_script),
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ]
        
        print("🚀 Starting Strategic Simulation Web Interface...")
        print("📱 Open your browser to: http://localhost:8501")
        print("🛑 Press Ctrl+C to stop the server")
        
        # Start Streamlit
        stcli.main()
        
    except ImportError:
        print("❌ Streamlit not installed. Please install it with:")
        print("   pip install streamlit")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to start web interface: {e}")
        sys.exit(1)


def start_cli_interface(remaining_args):
    """Start the CLI interface."""
    
    try:
        from src.ui.cli_interface import cli
        import sys
        
        # Set up CLI arguments
        sys.argv = ['cli'] + remaining_args
        
        print("🖥️  Starting Strategic Simulation CLI...")
        
        # Start CLI
        cli()
        
    except ImportError as e:
        print(f"❌ Failed to import CLI interface: {e}")
        print("Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to start CLI interface: {e}")
        sys.exit(1)


def check_dependencies():
    """Check if required dependencies are installed."""
    
    required_packages = [
        'click',
        'streamlit', 
        'tinytroupe',
        'openai',
        'langchain'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nPlease install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True


def setup_environment():
    """Set up the environment and create necessary directories."""
    
    # Create necessary directories
    directories = [
        "config",
        "config/knowledge_base", 
        "data",
        "data/conversations",
        "data/decisions",
        "data/reports",
        "data/results",
        "examples",
        "tests"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # Create default configuration files if they don't exist
    create_default_configs()


def create_default_configs():
    """Create default configuration files if they don't exist."""
    
    config_dir = Path("config")
    
    # Check if agents config exists
    agents_config_path = config_dir / "agents_config.json"
    if not agents_config_path.exists():
        print("⚠️  agents_config.json not found. Please copy from the config directory.")
        print("   The system may not work properly without agent configurations.")
    
    # Check if simulation config exists
    sim_config_path = config_dir / "simulation_config.json"
    if not sim_config_path.exists():
        print("⚠️  simulation_config.json not found. Please copy from the config directory.")
        print("   The system may not work properly without simulation configurations.")
    
    # Create .env template if it doesn't exist
    env_path = Path(".env")
    if not env_path.exists():
        env_template = """# Strategic Simulation System Environment Variables

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Anthropic API Configuration (optional)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Memory System Configuration
MEM0_API_KEY=your_mem0_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///data/simulation.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=data/simulation.log

# Security Configuration
SECRET_KEY=your_secret_key_here
"""
        
        with open(env_path, 'w') as f:
            f.write(env_template)
        
        print("📝 Created .env template file. Please update with your API keys.")


if __name__ == "__main__":
    # Set up environment
    setup_environment()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Run main function
    main()
