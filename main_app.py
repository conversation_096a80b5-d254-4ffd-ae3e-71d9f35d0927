#!/usr/bin/env python3
"""
战略推演系统 - 完整项目启动页面
统一的启动入口，包含所有功能模块
"""

import streamlit as st
import subprocess
import sys
import os
from pathlib import Path
from datetime import datetime

# 设置页面配置
st.set_page_config(
    page_title="战略推演系统",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

def show_banner():
    """显示系统横幅"""
    st.markdown("""
    <div style="background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); 
                padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
        <h1 style="color: white; text-align: center; margin: 0;">
            🎯 战略推演系统
        </h1>
        <h3 style="color: #f0f0f0; text-align: center; margin: 0.5rem 0 0 0;">
            智能研讨 · 战略推演· 决策支持
        </h3>
    </div>
    """, unsafe_allow_html=True)

def show_system_info():
    """显示系统信息"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("🤖 AI模型", "Qwen3", "Openai")
    
    with col2:
        st.metric("🎭 智能体", "5+", "可扩展")
    
    with col3:
        st.metric("🔄 推演模式", "3种", "递进式")
    
    with col4:
        st.metric("💬 对话轮数", "可配置", "1-10轮")

def show_features():
    """显示核心功能"""
    st.markdown("### 🌟 核心功能特色")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        #### 🎭 智能体角色
        - **特朗普**: 现任总统，强硬派，商人思维，美国优先
        - **拜登**: 前任总统，外交派，多边合作，国际协调
        - **军事专家**: 专业分析，风险评估，作战计划
        - **政治专家**: 学术视角，历史借鉴，战略影响
        - **经济专家**: 经济分析，贸易影响，金融风险
        - **用户角色**: 您可以扮演任何智囊团成员
        - **导调角色**: 研讨主持人角色
        """)
    
    with col2:
        st.markdown("""
        #### 🔄 推演特色
        - **递进式研讨**: 每轮都有新的深度和维度
        - **真实研讨**: AI智能体会互相回应和质疑
        - **完整上下文**: 基于所有历史讨论进行分析
        - **专业知识**: 每个智能体都有专业背景
        - **流式输出**: AI回复时逐字符显示
        """)

def show_modules():
    """显示功能模块"""
    st.markdown("### 🎮 功能模块选择")
    
    modules = [
        {
            "name": "🎯 完整推演系统",
            "file": "demo_app.py",
            "description": "包含所有功能的主应用，支持多智能体研讨、用户参与、流式输出等",
            "features": ["多智能体研讨", "用户参与", "流式输出", "完整推演流程", "决策支持"],
            "recommended": True
        }
        # {
        #     "name": "🔥 递进式智囊团研讨",
        #     "file": "progressive_debate.py", 
        #     "description": "专门的递进式研讨，每轮都有新的深度，避免重复讨论",
        #     "features": ["递进式轮次", "专业知识库", "避免重复", "轮次主题", "深度分析"],
        #     "recommended": True
        # },
        # {
        #     "name": "🎪 多智能体辩论模式",
        #     "file": "debate_simulation.py",
        #     "description": "专注于AI互动辩论，激烈讨论标记，优化的辩论界面",
        #     "features": ["激烈辩论", "互动标记", "辩论界面", "观点碰撞", "建设性争论"],
        #     "recommended": False
        # },
        # {
        #     "name": "🧪 自动轮流测试",
        #     "file": "auto_speak_test.py",
        #     "description": "测试自动发言功能，AI智能体自动轮流，用户智能体暂停输入",
        #     "features": ["自动轮流", "用户暂停", "测试功能", "流式输出", "轮次控制"],
        #     "recommended": False
        # },
        # {
        #     "name": "👤 用户输入测试",
        #     "file": "user_input_test.py",
        #     "description": "专门测试用户智能体输入功能，确保输入框正常显示",
        #     "features": ["用户输入", "输入测试", "界面测试", "功能验证", "交互测试"],
        #     "recommended": False
        # }
    ]
    
    for i, module in enumerate(modules):
        with st.expander(f"{module['name']} {'⭐ 推荐' if module['recommended'] else ''}", expanded=module['recommended']):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**描述**: {module['description']}")
                st.write("**功能特色**:")
                for feature in module['features']:
                    st.write(f"• {feature}")
            
            with col2:
                if st.button(f"🚀 启动", key=f"launch_{i}", type="primary" if module['recommended'] else "secondary"):
                    launch_module(module['file'], module['name'])

def launch_module(file_name: str, module_name: str):
    """启动指定模块"""
    st.info(f"正在启动 {module_name}...")
    
    # 检查文件是否存在
    if not Path(file_name).exists():
        st.error(f"❌ 找不到文件: {file_name}")
        return
    
    try:
        # 在新的浏览器标签页中启动
        st.markdown(f"""
        <script>
        window.open('http://localhost:8501', '_blank');
        </script>
        """, unsafe_allow_html=True)
        
        # 启动streamlit应用
        subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", file_name,
            "--server.port=8501",
            "--server.address=localhost"
        ])
        
        st.success(f"✅ {module_name} 已启动！")
        st.info("📱 新的浏览器标签页将打开，地址: http://localhost:8501")
        
    except Exception as e:
        st.error(f"❌ 启动失败: {e}")

# def show_quick_start():
#     """显示快速开始指南"""
#     # st.markdown("### 🚀 快速开始指南")
    
#     with st.expander("📋 新用户推荐流程", expanded=True):
#         st.markdown("""
#         #### 🎯 推荐使用流程
        
#         1. **首次体验**: 选择 "🎯 完整推演系统" 
#         2. **进入推演**: 点击左侧 "🚀 开始推演"
#         3. **选择场景**: 选择 "台海危机应对策略"
#         4. **选择智能体**: 勾选特朗普、拜登、军事专家、政治专家
#         5. **配置参数**: 
#            - ✅ 勾选 "🌊 启用流式输出"
#            - ✅ 勾选 "🔄 自动轮流发言" 
#            - ⚙️ 设置 "每个智能体发言轮数: 4"
#         6. **开始推演**: 点击 "🎬 开始推演"
#         7. **观看辩论**: AI智能体会自动进行辩论
#         8. **参与讨论**: 轮到用户智能体时输入您的观点
        
#         #### 🔥 高级体验
        
#         想要体验递进式研讨？选择 "🔥 递进式智囊团研讨"
#         - 每轮都有明确的主题重点
#         - 避免重复讨论内容
#         - 更深入的专业分析
#         """)

def show_system_status():
    """显示系统状态"""
    st.markdown("### 📊 系统状态")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # 检查AI配置
        env_file = Path(".env")
        if env_file.exists():
            st.success("✅ AI配置正常")
        else:
            st.warning("⚠️ 需要配置AI")
    
    with col2:
        # 检查依赖包
        try:
            import streamlit
            import openai
            st.success("✅ 依赖包完整")
        except ImportError:
            st.warning("⚠️ 缺少依赖包")
    
    with col3:
        # 显示当前时间
        current_time = datetime.now().strftime("%H:%M:%S")
        st.info(f"🕐 当前时间: {current_time}")

def show_help():
    """显示帮助信息"""
    with st.expander("❓ 常见问题"):
        st.markdown("""
        **Q: 如何选择合适的模块？**
        A: 新用户推荐"完整推演系统"，想要深度研讨选择"递进式智囊团研讨"
        
        **Q: AI不回应怎么办？**
        A: 检查网络连接，系统会自动重试。确保.env文件配置正确
        
        **Q: 如何添加自定义智能体？**
        A: 在"🤖 智能体管理"页面可以创建和管理自定义智能体
        
        **Q: 推演结果如何保存？**
        A: 系统支持自动保存，也可以手动导出推演记录
        
        **Q: 支持哪些推演场景？**
        A: 目前支持台海危机、中美贸易、国际冲突等多种场景
        """)

def main():
    """主函数"""
    
    # 显示横幅
    show_banner()
    
    # 侧边栏
    with st.sidebar:
        st.markdown("### 🎮 导航菜单")
        
        page = st.selectbox(
            "选择页面",
            ["🏠 主页", "🎯 功能模块",  "📊 系统状态"]
        )
        
        st.markdown("---")
        st.markdown("### 📱 系统信息")
        st.info("战略推演系统 v1.0")
    
    # 主内容区域
    if page == "🏠 主页":
        show_system_info()
        st.markdown("---")
        show_features()
        st.markdown("---")
        # show_quick_start()
        
    elif page == "🎯 功能模块":
        show_modules()
        
    # elif page == "🚀 快速开始":
    #     show_quick_start()
        
    elif page == "📊 系统状态":
        show_system_status()
        
    
    # 底部信息
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        🎯 战略推演系统 | 智能决策支持平台
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
