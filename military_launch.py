#!/usr/bin/env python3
"""
军事风格启动器

提供多种军事主题的启动选项
"""

import os
import sys
import subprocess
from pathlib import Path

def print_military_banner():
    """打印军事风格横幅"""
    print("=" * 60)
    print("⚔️  军事战略推演指挥中心  ⚔️")
    print("🎖️  AI驱动的战术决策仿真平台 🎖️")
    print("=" * 60)
    print()

def show_military_menu():
    """显示军事主题菜单"""
    print("🎯 选择军事主题:")
    print("1. ⚔️  标准军事主题 - 经典军事风格界面")
    print("2. 🎖️  高级军事主题 - 增强版军事界面")
    print("3. 🚀  战术军事主题 - 战术指挥风格")
    print("4. 🔧  返回原始界面 - 标准界面")
    print("5. ❌  退出")
    print()

def launch_military_theme(theme_choice):
    """启动指定的军事主题"""
    
    if theme_choice == "1":
        print("⚔️ 启动标准军事主题...")
        print("🎖️ 军事战略推演指挥中心正在启动...")
        print("🌐 访问地址: http://localhost:8501")
        print("🛑 按 Ctrl+C 停止服务")
        print("-" * 50)
        
        try:
            subprocess.run([
                sys.executable, "-m", "streamlit", "run", "demo_app_military.py",
                "--server.port=8501", "--server.address=0.0.0.0"
            ], check=True)
        except KeyboardInterrupt:
            print("\n🛑 服务已停止")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
    
    elif theme_choice == "2":
        print("🎖️ 高级军事主题功能开发中...")
        print("💡 请先使用标准军事主题")
        input("按回车键继续...")
    
    elif theme_choice == "3":
        print("🚀 战术军事主题功能开发中...")
        print("💡 请先使用标准军事主题")
        input("按回车键继续...")
    
    elif theme_choice == "4":
        print("🔧 启动原始界面...")
        print("🌐 访问地址: http://localhost:8501")
        print("🛑 按 Ctrl+C 停止服务")
        print("-" * 50)
        
        try:
            subprocess.run([
                sys.executable, "-m", "streamlit", "run", "demo_app.py",
                "--server.port=8501", "--server.address=0.0.0.0"
            ], check=True)
        except KeyboardInterrupt:
            print("\n🛑 服务已停止")
        except Exception as e:
            print(f"❌ 启动失败: {e}")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    # 检查文件是否存在
    files_to_check = [
        "demo_app_military.py",
        "demo_app.py",
        "requirements.txt"
    ]
    
    for file in files_to_check:
        if Path(file).exists():
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
    
    print()

def main():
    """主函数"""
    
    # 显示军事横幅
    print_military_banner()
    
    # 检查依赖
    check_dependencies()
    
    while True:
        # 显示菜单
        show_military_menu()
        
        # 获取用户选择
        try:
            choice = input("🎯 请输入选择 (1-5): ").strip()
            
            if choice == "1":
                launch_military_theme("1")
                break
            elif choice == "2":
                launch_military_theme("2")
            elif choice == "3":
                launch_military_theme("3")
            elif choice == "4":
                launch_military_theme("4")
                break
            elif choice == "5":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
                input("按回车键继续...")
        
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main() 