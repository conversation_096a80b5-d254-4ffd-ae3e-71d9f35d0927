#!/usr/bin/env python3
"""
递进式智囊团研讨 - 每轮都有新的深度和维度
"""

import streamlit as st
import time
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

st.set_page_config(page_title="递进式智囊团研讨", page_icon="🎯", layout="wide")

# 导入AI功能
try:
    from utils.llm_config import generate_response
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

def get_progressive_prompt(agent_name: str, round_number: int, total_rounds: int, discussion_history: list) -> str:
    """生成递进式研讨提示"""
    
    # 智能体专业设定
    agent_profiles = {
        "特朗普": {
            "identity": "美国第47任总统，现任总统，商业大亨",
            "expertise": "商业谈判、美国政治、国际贸易、实力外交",
            "perspective": "美国优先、成本效益、实力展示、交易艺术",
            "style": "直接、强硬、商人思维"
        },
        "拜登": {
            "identity": "美国第46任总统，前任总统，资深政治家",
            "expertise": "外交政策、国际关系、多边合作、危机管理",
            "perspective": "盟友协调、外交解决、国际合作、长期稳定",
            "style": "温和、外交导向、经验丰富"
        },
        "军事专家": {
            "identity": "五角大楼高级顾问，退役将军",
            "expertise": "军事战略、作战计划、武器系统、威慑理论",
            "perspective": "军事可行性、战略风险、作战能力、安全评估",
            "style": "专业、客观、数据驱动"
        },
        "政治专家": {
            "identity": "国际关系学者，政治学教授",
            "expertise": "国际法、地缘政治、历史案例、政治理论",
            "perspective": "法理框架、政治后果、历史借鉴、战略影响",
            "style": "学术、深度、理论结合实践"
        }
    }
    
    profile = agent_profiles.get(agent_name, agent_profiles["政治专家"])
    
    # 轮次递进重点
    round_focuses = {
        1: {
            "theme": "初步分析与立场阐述",
            "focus": "基础分析、核心立场、初步判断",
            "instruction": "请从你的专业角度进行初步分析，阐述基本立场和核心观点"
        },
        2: {
            "theme": "深入分析与方案设计", 
            "focus": "具体方案、详细分析、操作路径",
            "instruction": "基于第一轮讨论，深入分析具体问题，提出详细的解决方案和实施路径"
        },
        3: {
            "theme": "风险评估与可行性论证",
            "focus": "风险识别、可行性分析、问题预判",
            "instruction": "评估各方案的风险和可行性，指出潜在问题、副作用和改进建议"
        },
        4: {
            "theme": "综合权衡与最终建议",
            "focus": "综合考量、最终建议、实施优先级",
            "instruction": "综合考虑所有因素，提出最终建议、实施优先级和关键节点"
        }
    }
    
    current_round = min(round_number, 4)
    round_info = round_focuses.get(current_round, round_focuses[4])
    
    # 分析讨论历史
    history_analysis = ""
    if discussion_history:
        history_analysis = "\n【讨论历史分析】\n"
        
        # 按轮次组织
        rounds_content = {}
        for msg in discussion_history:
            speaker = msg["speaker"]
            # 计算该发言者的轮次
            speaker_round = sum(1 for m in discussion_history[:discussion_history.index(msg)+1] if m["speaker"] == speaker)
            
            if speaker_round not in rounds_content:
                rounds_content[speaker_round] = []
            rounds_content[speaker_round].append(f"{speaker}: {msg['content']}")
        
        for r in sorted(rounds_content.keys()):
            history_analysis += f"\n第{r}轮讨论要点：\n"
            for content in rounds_content[r]:
                history_analysis += f"• {content}\n"
        
        # 识别需要深入的维度
        history_analysis += "\n【需要深入的维度】\n"
        history_analysis += "• 寻找讨论中的盲点和遗漏\n"
        history_analysis += "• 识别不同观点的深层分歧\n"
        history_analysis += "• 探索新的分析角度和维度\n"
    
    return f"""你是{profile['identity']}，正在参与台海危机应对策略的高级智囊团递进式研讨。

【你的专业背景】
- 身份：{profile['identity']}
- 专业领域：{profile['expertise']}
- 分析视角：{profile['perspective']}
- 表达风格：{profile['style']}

【当前轮次信息】
- 轮次：第{round_number}轮/共{total_rounds}轮
- 本轮主题：{round_info['theme']}
- 重点关注：{round_info['focus']}
- 具体要求：{round_info['instruction']}

{history_analysis}

【递进式研讨要求】
1. 必须基于完整讨论历史进行分析
2. 从你的专业角度提供独特见解
3. 每轮都要有新的深度和维度
4. 避免重复已充分讨论的内容
5. 与其他专家观点进行建设性互动
6. 推动讨论向更深层次发展

请确保你的发言具有明显的递进性，体现本轮的主题重点。用中文回应，控制在200字以内。"""

def generate_progressive_response(agent_name: str, scenario: str, round_number: int, total_rounds: int, discussion_history: list) -> str:
    """生成递进式回应"""
    
    if AI_AVAILABLE and generate_response:
        prompt = get_progressive_prompt(agent_name, round_number, total_rounds, discussion_history)
        
        user_content = f"""【台海危机应对策略 - 递进式研讨】

{prompt}

场景背景：台海局势升级，美国需要制定综合应对策略，涉及军事、外交、经济等多个维度。

请基于以上要求进行发言。"""
        
        messages = [
            {"role": "system", "content": "你是专业的智囊团成员，参与递进式战略研讨。"},
            {"role": "user", "content": user_content}
        ]
        
        try:
            response = generate_response(messages, max_tokens=300, temperature=0.8)
            return response
        except Exception as e:
            return f"[{agent_name}] 基于当前讨论，我认为需要从{agent_name}的角度进一步深入分析。"
    else:
        # 模拟递进式回应
        round_responses = {
            1: f"[{agent_name}] 从初步分析来看，台海问题涉及多个层面，我的基本立场是...",
            2: f"[{agent_name}] 基于第一轮讨论，我提出具体的解决方案：...",
            3: f"[{agent_name}] 评估各方案的风险，我认为需要注意以下问题：...",
            4: f"[{agent_name}] 综合所有讨论，我的最终建议是：..."
        }
        return round_responses.get(round_number, f"[{agent_name}] 继续深入分析...")

def stream_display(content: str, agent_name: str):
    """流式显示"""
    placeholder = st.empty()
    displayed_text = ""
    
    for char in content:
        displayed_text += char
        placeholder.markdown(f"{displayed_text}<span style='animation: blink 1s infinite; color: #666;'>|</span>", unsafe_allow_html=True)
        time.sleep(0.03)
    
    placeholder.markdown(displayed_text)
    return displayed_text

def main():
    """主函数"""
    
    st.title("🎯 递进式智囊团研讨")
    st.markdown("**每轮都有新的深度和维度，避免重复讨论**")
    
    # 添加CSS
    st.markdown("""
    <style>
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    .round-header {
        background: linear-gradient(90deg, #667eea, #764ba2);
        color: white;
        padding: 10px;
        border-radius: 8px;
        margin: 10px 0;
        font-weight: bold;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 初始化
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'agents' not in st.session_state:
        st.session_state.agents = ["特朗普", "军事专家", "张三", "政治专家", "拜登"]
    
    if 'user_agents' not in st.session_state:
        st.session_state.user_agents = ["张三"]
    
    if 'current_speaker_index' not in st.session_state:
        st.session_state.current_speaker_index = 0
    
    if 'rounds_per_agent' not in st.session_state:
        st.session_state.rounds_per_agent = 4
    
    if 'current_round' not in st.session_state:
        st.session_state.current_round = 0
    
    # 侧边栏
    with st.sidebar:
        st.header("🎯 递进式研讨控制")
        
        scenario = "台海危机应对策略"
        st.info(f"**研讨主题**: {scenario}")
        
        # 设置
        enable_stream = st.checkbox("🌊 流式输出", value=True)
        auto_progress = st.checkbox("🔄 自动推进", value=False)
        st.session_state.rounds_per_agent = st.slider("每个智能体轮数", 2, 6, 4)
        
        # 计算总轮数
        total_speeches = len(st.session_state.agents) * st.session_state.rounds_per_agent
        
        # 进度显示
        if st.session_state.current_round > 0:
            progress = min(st.session_state.current_round / total_speeches, 1.0)
            st.progress(progress, text=f"研讨进度: {st.session_state.current_round}/{total_speeches}")
            
            # 显示各智能体轮次
            st.markdown("**各智能体轮次:**")
            for agent in st.session_state.agents:
                agent_rounds = sum(1 for msg in st.session_state.messages if msg["speaker"] == agent)
                st.write(f"• {agent}: {agent_rounds}/{st.session_state.rounds_per_agent}")
        
        # 控制按钮
        if st.button("🎬 开始研讨"):
            if not st.session_state.messages:
                opening = "欢迎参加台海危机应对策略递进式研讨。我们将进行多轮深入讨论，每轮都要有新的维度和深度。"
                
                opening_msg = {
                    "speaker": "研讨主持人",
                    "content": opening,
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(opening_msg)
                st.rerun()
        
        if st.button("➡️ 下一位发言"):
            handle_next_speaker(scenario, enable_stream)
        
        if st.button("🔄 重置研讨"):
            st.session_state.messages = []
            st.session_state.current_speaker_index = 0
            st.session_state.current_round = 0
            st.rerun()
    
    # 自动推进逻辑
    if auto_progress and st.session_state.messages and st.session_state.current_round < total_speeches:
        current_agent = st.session_state.agents[st.session_state.current_speaker_index]
        is_user_agent = current_agent in st.session_state.user_agents
        
        if not is_user_agent:
            st.info(f"🤖 {current_agent} 正在发言...")
            time.sleep(2)
            handle_next_speaker(scenario, enable_stream)
            st.rerun()
        else:
            st.warning(f"⏸️ 轮到用户 **{current_agent}**，请发言")
    
    # 显示研讨记录
    st.markdown("### 🎯 递进式研讨记录")
    
    current_round_display = 0
    for i, msg in enumerate(st.session_state.messages):
        # 检查是否是新轮次开始
        if i > 0 and msg["speaker"] in st.session_state.agents:
            speaker_previous_count = sum(1 for m in st.session_state.messages[:i] if m["speaker"] == msg["speaker"])
            if speaker_previous_count == 0:  # 该智能体第一次发言
                current_round_display += 1
                if current_round_display <= st.session_state.rounds_per_agent:
                    st.markdown(f'<div class="round-header">🔄 第{current_round_display}轮研讨开始</div>', unsafe_allow_html=True)
        
        is_user_msg = msg["speaker"] in st.session_state.user_agents
        with st.chat_message("user" if is_user_msg else "assistant"):
            # 显示智能体轮次信息
            if msg["speaker"] in st.session_state.agents:
                agent_round = sum(1 for m in st.session_state.messages[:i+1] if m["speaker"] == msg["speaker"])
                st.write(f"**{msg['speaker']}** (第{agent_round}轮) - {msg['timestamp']}")
            else:
                st.write(f"**{msg['speaker']}** - {msg['timestamp']}")
            st.write(msg['content'])
    
    # 用户发言区域
    if st.session_state.agents and st.session_state.current_round < total_speeches:
        current_agent = st.session_state.agents[st.session_state.current_speaker_index]
        is_user_agent = current_agent in st.session_state.user_agents
        
        if is_user_agent:
            # 计算用户当前轮次
            user_round = sum(1 for msg in st.session_state.messages if msg["speaker"] == current_agent) + 1
            
            st.markdown("---")
            st.markdown(f"### 💬 {current_agent} 的第{user_round}轮发言")
            
            # 显示当前轮次重点
            round_themes = {
                1: "初步分析与立场阐述",
                2: "深入分析与方案设计", 
                3: "风险评估与可行性论证",
                4: "综合权衡与最终建议"
            }
            theme = round_themes.get(user_round, "深入分析")
            st.info(f"本轮重点：{theme}")
            
            user_input = st.text_area(
                f"请以 {current_agent} 身份发言:",
                height=120,
                key=f"progressive_input_{current_agent}_{user_round}",
                placeholder=f"请从{theme}的角度发言，避免重复之前的内容..."
            )
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🎤 发言", type="primary", use_container_width=True):
                    if user_input and user_input.strip():
                        new_msg = {
                            "speaker": current_agent,
                            "content": user_input.strip(),
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        }
                        st.session_state.messages.append(new_msg)
                        st.session_state.current_round += 1
                        st.session_state.current_speaker_index = (st.session_state.current_speaker_index + 1) % len(st.session_state.agents)
                        st.success(f"✅ {current_agent} 第{user_round}轮发言完成")
                        st.rerun()
                    else:
                        st.error("请输入发言内容")
            
            with col2:
                if st.button("🤖 AI代发", use_container_width=True):
                    response = generate_progressive_response(
                        current_agent, scenario, user_round, 
                        st.session_state.rounds_per_agent, st.session_state.messages
                    )
                    
                    new_msg = {
                        "speaker": current_agent,
                        "content": f"[AI代发] {response}",
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    }
                    st.session_state.messages.append(new_msg)
                    st.session_state.current_round += 1
                    st.session_state.current_speaker_index = (st.session_state.current_speaker_index + 1) % len(st.session_state.agents)
                    st.success(f"✅ AI为 {current_agent} 第{user_round}轮代发完成")
                    st.rerun()
    
    # 研讨完成
    if st.session_state.current_round >= total_speeches:
        st.success("🎉 递进式研讨完成！每个智能体都完成了多轮深入讨论。")
        st.balloons()

def handle_next_speaker(scenario, enable_stream):
    """处理下一位发言者"""
    if st.session_state.agents:
        current_agent = st.session_state.agents[st.session_state.current_speaker_index]
        is_user_agent = current_agent in st.session_state.user_agents
        
        if not is_user_agent:
            # 计算AI智能体当前轮次
            agent_round = sum(1 for msg in st.session_state.messages if msg["speaker"] == current_agent) + 1
            
            # AI智能体发言
            response = generate_progressive_response(
                current_agent, scenario, agent_round, 
                st.session_state.rounds_per_agent, st.session_state.messages
            )
            
            with st.chat_message("assistant"):
                if enable_stream:
                    displayed_response = stream_display(response, current_agent)
                else:
                    st.write(f"**{current_agent}** (第{agent_round}轮) - {datetime.now().strftime('%H:%M:%S')}")
                    st.write(response)
                    displayed_response = response
            
            new_msg = {
                "speaker": current_agent,
                "content": displayed_response,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(new_msg)
            st.session_state.current_round += 1
            st.session_state.current_speaker_index = (st.session_state.current_speaker_index + 1) % len(st.session_state.agents)

if __name__ == "__main__":
    main()
