#!/usr/bin/env python3
"""
Quick Start Script

快速启动战略推演系统，自动处理依赖和配置问题。
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True


def install_minimal_dependencies():
    """安装最小依赖"""
    print("📦 安装最小依赖包...")
    
    minimal_packages = [
        "streamlit>=1.28.0",
        "openai>=1.0.0", 
        "python-dotenv>=1.0.0",
        "click>=8.1.0",
        "pandas>=2.0.0",
        "pydantic>=2.0.0",
        "requests>=2.31.0"
    ]
    
    for package in minimal_packages:
        try:
            print(f"  安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError:
            print(f"⚠️ 安装 {package} 失败，跳过...")
    
    print("✅ 基础依赖安装完成")


def create_env_file():
    """创建环境变量文件"""
    env_path = Path(".env")
    
    if env_path.exists():
        print("✅ .env 文件已存在")
        return
    
    print("📝 创建 .env 配置文件...")
    
    env_content = """# Strategic Simulation System Environment Variables

# OpenAI API Configuration (推荐)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# 如果没有OpenAI API，可以使用其他模型
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 系统配置
LOG_LEVEL=INFO
"""
    
    with open(env_path, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ .env 文件已创建")
    print("⚠️ 请编辑 .env 文件，添加您的 OpenAI API 密钥")


def check_api_key():
    """检查API密钥"""
    from dotenv import load_dotenv
    load_dotenv()
    
    openai_key = os.getenv('OPENAI_API_KEY')
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')
    
    if openai_key and openai_key != 'your_openai_api_key_here':
        print("✅ 检测到 OpenAI API 密钥")
        return True
    elif anthropic_key and anthropic_key != 'your_anthropic_api_key_here':
        print("✅ 检测到 Anthropic API 密钥")
        return True
    else:
        print("⚠️ 未检测到有效的API密钥")
        print("请编辑 .env 文件，添加您的API密钥")
        return False


def create_demo_mode():
    """创建演示模式（不需要API密钥）"""
    print("🎭 启动演示模式...")
    
    # 创建演示配置
    demo_env = """# 演示模式配置
DEMO_MODE=true
OPENAI_API_KEY=demo_key
OPENAI_MODEL=gpt-4
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(demo_env)
    
    print("✅ 演示模式已配置")


def start_web_interface():
    """启动Web界面"""
    print("🚀 启动Web界面...")
    print("📱 浏览器将自动打开 http://localhost:8501")
    print("🛑 按 Ctrl+C 停止服务器")
    
    try:
        # 启动Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py",
            "--", "web",
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ])
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def main():
    """主函数"""
    print("🎯 Strategic Simulation System - 快速启动")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 安装依赖
    install_minimal_dependencies()
    
    # 创建配置文件
    create_env_file()
    
    # 检查API密钥
    has_api_key = check_api_key()
    
    if not has_api_key:
        print("\n🤔 选择启动模式:")
        print("1. 配置API密钥后启动 (推荐)")
        print("2. 演示模式启动 (功能受限)")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "2":
            create_demo_mode()
        else:
            print("\n📝 请按以下步骤配置API密钥:")
            print("1. 编辑 .env 文件")
            print("2. 将 OPENAI_API_KEY=your_openai_api_key_here 替换为您的真实API密钥")
            print("3. 保存文件后重新运行此脚本")
            return
    
    # 启动Web界面
    start_web_interface()


if __name__ == "__main__":
    main()
