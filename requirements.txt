# 基础框架
tinytroupe>=0.1.0

# AI和LLM相关
openai>=1.0.0
anthropic>=0.7.0
langchain>=0.1.0
langchain-community>=0.0.20
langchain-openai>=0.0.5

# 记忆管理
mem0ai>=0.1.0
chromadb>=0.4.0
faiss-cpu>=1.7.0

# 向量数据库和检索
sentence-transformers>=2.2.0
transformers>=4.30.0
torch>=2.0.0

# Web界面
streamlit>=1.28.0
fastapi>=0.100.0
uvicorn>=0.23.0
gradio>=3.40.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.0.0
python-dotenv>=1.0.0

# 文档处理
pypdf>=3.15.0
python-docx>=0.8.11
markdown>=3.4.0
beautifulsoup4>=4.12.0

# 日志和监控
loguru>=0.7.0
rich>=13.0.0

# 测试
pytest>=7.4.0
pytest-asyncio>=0.21.0

# 工具库
click>=8.1.0
tqdm>=4.65.0
requests>=2.31.0
aiohttp>=3.8.0

# 配置管理
pyyaml>=6.0
toml>=0.10.2

# 数据库
#sqlite3
sqlalchemy>=2.0.0

# 可选：本地LLM支持
# ollama>=0.1.0
# llama-cpp-python>=0.2.0

# 可选：语音支持
# speechrecognition>=3.10.0
# pyttsx3>=2.90

# 可选：图表和可视化
# matplotlib>=3.7.0
# plotly>=5.15.0
# seaborn>=0.12.0
