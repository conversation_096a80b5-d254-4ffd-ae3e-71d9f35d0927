#!/usr/bin/env python3
"""
Strategic Simulation System - 一键启动脚本
使用 Qwen/Qwen3-8B 模型
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("🎯" + "=" * 58 + "🎯")
    print("🎯           战略推演系统 - AI驱动决策仿真平台         🎯")
    print("🎯  AI模型: Qwen/Qwen3-8B                          🎯")
    print("🎯  服务商: SiliconFlow                            🎯")
    print("🎯" + "=" * 58 + "🎯")

def check_python():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 系统要求Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """安装系统依赖"""
    print("\n📦 检查并安装系统依赖包...")

    required_packages = [
        "streamlit>=1.28.0",
        "openai>=1.0.0",
        "python-dotenv>=1.0.0",
        "click>=8.1.0",
        "pandas>=2.0.0",
        "pydantic>=2.0.0",
        "requests>=2.31.0"
    ]

    for package in required_packages:
        package_name = package.split(">=")[0]
        try:
            __import__(package_name.replace("-", "_"))
            print(f"  ✅ {package_name} 已安装")
        except ImportError:
            print(f"  📦 正在安装 {package}...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"  ✅ {package_name} 安装成功")
            except subprocess.CalledProcessError:
                print(f"  ❌ {package_name} 安装失败，请手动安装")

def check_env_file():
    """检查环境变量文件"""
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ .env 文件已存在")
        return True
    
    print("📝 创建 .env 配置文件...")
    
    env_content = """# SiliconFlow API Configuration
OPENAI_API_KEY=sk-xxdbfsgbcrcapwbjdaohlxkilvbmwvxsurallxrunlrrggbw
OPENAI_BASE_URL=https://api.siliconflow.cn/v1
OPENAI_MODEL=Qwen/Qwen3-8B

# Model Configuration
MODEL_PROVIDER=siliconflow
MODEL_NAME=Qwen/Qwen3-8B
TEMPERATURE=0.7
MAX_TOKENS=2000

# System Configuration
DEMO_MODE=false
LOG_LEVEL=INFO
"""
    
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ .env 文件已创建")
    return True

def test_llm_connection():
    """测试AI模型连接"""
    print("\n🧪 测试 Qwen/Qwen3-8B AI模型连接...")

    try:
        # Add src to path
        sys.path.insert(0, str(Path(__file__).parent / "src"))

        from utils.llm_config import test_llm_connection

        result = test_llm_connection()

        if result['success']:
            print("✅ AI模型连接成功!")
            print(f"📝 测试响应: {result['response']}")
            print(f"🏷️ 当前模型: {result['model']}")
            return True
        else:
            print("❌ AI模型连接失败!")
            print(f"🚫 错误信息: {result['error']}")
            print("\n⚠️ 系统将以演示模式启动...")
            return False

    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        print("⚠️ 系统将以演示模式启动...")
        return False

def start_streamlit():
    """启动Web应用界面"""
    print("\n🚀 启动战略推演系统Web界面...")
    print("📱 浏览器将自动打开: http://localhost:8501")
    print("🛑 按 Ctrl+C 停止系统\n")

    try:
        # 启动Web应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "demo_app.py",
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false",
            "--server.headless=false"
        ])
    except KeyboardInterrupt:
        print("\n👋 战略推演系统已停止运行")
    except FileNotFoundError:
        print("❌ 找不到应用文件 demo_app.py")
        print("请确保在 strategic_simulation 项目目录中运行此脚本")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python():
        input("按回车键退出...")
        return
    
    # 安装依赖
    install_dependencies()
    
    # 检查配置文件
    check_env_file()
    
    # 测试LLM连接
    test_llm_connection()
    
    # 启动应用
    start_streamlit()

if __name__ == "__main__":
    main()
