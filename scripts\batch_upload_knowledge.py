#!/usr/bin/env python3
"""
Batch Knowledge Upload Script

Script for batch uploading documents to agent knowledge bases.
Supports txt, md, pdf, docx, and other document formats.
"""

import os
import sys
import argparse
import glob
from pathlib import Path
from typing import List, Dict, Any

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from knowledge.knowledge_manager import KnowledgeManager


def find_documents(directory: str, patterns: List[str] = None) -> List[str]:
    """Find all documents matching the patterns in directory."""
    
    if patterns is None:
        patterns = ["*.txt", "*.md", "*.json", "*.csv"]
    
    documents = []
    for pattern in patterns:
        documents.extend(glob.glob(os.path.join(directory, "**", pattern), recursive=True))
    
    return documents


def process_document(file_path: str) -> str:
    """Process a document and return its content."""
    
    try:
        # Handle different file types
        if file_path.endswith(('.txt', '.md')):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        
        elif file_path.endswith('.json'):
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                content = json.dumps(data, indent=2)
        
        elif file_path.endswith('.csv'):
            import pandas as pd
            df = pd.read_csv(file_path)
            content = df.to_string()
        
        else:
            print(f"⚠️ Unsupported file type: {file_path}")
            return ""
        
        # Add metadata header
        filename = os.path.basename(file_path)
        header = f"# Document: {filename}\n\nSource: {file_path}\nProcessed: {os.path.getctime(file_path)}\n\n"
        
        return header + content
        
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return ""


def batch_upload_to_agent(km: KnowledgeManager, agent_name: str, 
                         documents: List[str], merge_mode: str = "append") -> bool:
    """Upload multiple documents to an agent's knowledge base."""
    
    print(f"📚 Processing {len(documents)} documents for {agent_name}...")
    
    all_content = []
    
    for doc_path in documents:
        print(f"  📄 Processing: {os.path.basename(doc_path)}")
        content = process_document(doc_path)
        
        if content:
            all_content.append(f"\n\n## From {os.path.basename(doc_path)}\n\n{content}")
    
    if not all_content:
        print(f"❌ No valid content found for {agent_name}")
        return False
    
    # Combine all content
    combined_content = "\n".join(all_content)
    
    # Update agent knowledge base
    try:
        success = km.update_agent_knowledge(agent_name, combined_content, merge_mode)
        
        if success:
            print(f"✅ Successfully updated knowledge base for {agent_name}")
            print(f"📊 Added {len(all_content)} documents, {len(combined_content)} characters")
        else:
            print(f"❌ Failed to update knowledge base for {agent_name}")
        
        return success
        
    except Exception as e:
        print(f"❌ Error updating knowledge base: {e}")
        return False


def create_shared_knowledge(km: KnowledgeManager, topic: str, 
                          documents: List[str], agents: List[str] = None) -> bool:
    """Create a shared knowledge base from multiple documents."""
    
    print(f"🌐 Creating shared knowledge base: {topic}")
    
    all_content = []
    
    for doc_path in documents:
        print(f"  📄 Processing: {os.path.basename(doc_path)}")
        content = process_document(doc_path)
        
        if content:
            all_content.append(content)
    
    if not all_content:
        print(f"❌ No valid content found for shared knowledge: {topic}")
        return False
    
    # Combine content
    combined_content = f"# Shared Knowledge: {topic}\n\n" + "\n\n---\n\n".join(all_content)
    
    try:
        kb_path = km.create_shared_knowledge_base(topic, combined_content, agents)
        print(f"✅ Created shared knowledge base: {kb_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating shared knowledge base: {e}")
        return False


def main():
    """Main function for batch knowledge upload."""
    
    parser = argparse.ArgumentParser(
        description="Batch upload documents to agent knowledge bases",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Upload all txt files in docs/ to trump agent
  python batch_upload_knowledge.py --agent trump --directory docs/ --patterns "*.txt"
  
  # Upload specific files to multiple agents
  python batch_upload_knowledge.py --agents trump,moderator --files doc1.txt,doc2.md
  
  # Create shared knowledge base
  python batch_upload_knowledge.py --shared-topic "Military Strategy" --directory strategy_docs/
  
  # Upload with different merge modes
  python batch_upload_knowledge.py --agent expert --directory docs/ --mode replace
        """
    )
    
    # Input options
    parser.add_argument('--agent', help='Target agent name')
    parser.add_argument('--agents', help='Comma-separated list of agent names')
    parser.add_argument('--directory', '-d', help='Directory containing documents')
    parser.add_argument('--files', '-f', help='Comma-separated list of specific files')
    parser.add_argument('--patterns', '-p', help='File patterns (e.g., "*.txt,*.md")', 
                       default="*.txt,*.md,*.json,*.csv")
    
    # Processing options
    parser.add_argument('--mode', choices=['append', 'replace', 'prepend'], 
                       default='append', help='How to merge with existing knowledge')
    parser.add_argument('--shared-topic', help='Create shared knowledge base with this topic')
    parser.add_argument('--kb-dir', default='config/knowledge_base', 
                       help='Knowledge base directory')
    
    # Output options
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without doing it')
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.agent and not args.agents and not args.shared_topic:
        print("❌ Must specify --agent, --agents, or --shared-topic")
        return 1
    
    if not args.directory and not args.files:
        print("❌ Must specify --directory or --files")
        return 1
    
    # Initialize knowledge manager
    try:
        km = KnowledgeManager(args.kb_dir)
        print(f"📚 Initialized knowledge manager: {args.kb_dir}")
    except Exception as e:
        print(f"❌ Failed to initialize knowledge manager: {e}")
        return 1
    
    # Find documents
    documents = []
    
    if args.files:
        documents = [f.strip() for f in args.files.split(',')]
    elif args.directory:
        patterns = [p.strip() for p in args.patterns.split(',')]
        documents = find_documents(args.directory, patterns)
    
    if not documents:
        print("❌ No documents found")
        return 1
    
    print(f"📄 Found {len(documents)} documents:")
    for doc in documents:
        print(f"  • {doc}")
    
    if args.dry_run:
        print("🔍 Dry run mode - no changes will be made")
        return 0
    
    # Process uploads
    success = True
    
    if args.shared_topic:
        # Create shared knowledge base
        agent_list = args.agents.split(',') if args.agents else None
        success = create_shared_knowledge(km, args.shared_topic, documents, agent_list)
    
    else:
        # Upload to specific agents
        target_agents = []
        
        if args.agent:
            target_agents.append(args.agent)
        
        if args.agents:
            target_agents.extend([a.strip() for a in args.agents.split(',')])
        
        for agent_name in target_agents:
            agent_success = batch_upload_to_agent(km, agent_name, documents, args.mode)
            success = success and agent_success
    
    if success:
        print("\n✅ Batch upload completed successfully!")
        
        # Show updated statistics
        analytics = km.get_knowledge_analytics()
        print(f"\n📊 Knowledge Base Statistics:")
        print(f"  Total Agents: {analytics['total_agents']}")
        print(f"  Total Documents: {analytics['overall_stats']['total_documents']}")
        print(f"  Total Words: {analytics['overall_stats']['total_words']:,}")
        
        return 0
    else:
        print("\n❌ Some uploads failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
