#!/usr/bin/env python3
"""
简化版上下文管理器 - 无需外部依赖
"""

from typing import List, Dict, Any, Tuple

class SimpleContextManager:
    """简化版上下文管理器"""
    
    def __init__(self, max_tokens: int = 4000):
        """
        初始化简化版上下文管理器
        
        Args:
            max_tokens: 最大token限制
        """
        self.max_tokens = max_tokens
        self.max_context_tokens = int(max_tokens * 0.7)  # 为回复预留30%空间
    
    def estimate_tokens(self, text: str) -> int:
        """
        估算文本的token数量（简化版本）
        
        Args:
            text: 输入文本
            
        Returns:
            估算的token数量
        """
        if not text:
            return 0
        
        # 改进的token估算算法
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        english_chars = len(text) - chinese_chars
        
        # 基于经验的估算：
        # - 中文字符：约1.5 token
        # - 英文字符：约0.25 token  
        # - 标点符号：约0.5 token
        punctuation_chars = sum(1 for char in text if char in '，。！？；：""''（）【】《》')
        
        estimated_tokens = int(
            chinese_chars * 1.5 + 
            english_chars * 0.25 + 
            punctuation_chars * 0.5
        )
        
        # 最小值保护，避免过小估算
        return max(estimated_tokens, len(text) // 8)
    
    def get_context_strategy(self, message_count: int, total_tokens: int) -> str:
        """
        根据消息数量和token数确定上下文策略
        
        Args:
            message_count: 消息数量
            total_tokens: 总token数
            
        Returns:
            策略名称
        """
        token_ratio = total_tokens / self.max_context_tokens
        
        if token_ratio <= 0.7:
            return "full_history"  # 完整历史
        elif message_count <= 15:
            return "sliding_window"  # 滑动窗口
        else:
            return "summary_recent"  # 摘要+最近
    
    def create_summary(self, messages: List[Dict], max_length: int = 300) -> str:
        """
        创建消息摘要
        
        Args:
            messages: 消息列表
            max_length: 最大摘要长度
            
        Returns:
            摘要文本
        """
        if not messages:
            return ""
        
        # 按发言者分组
        speaker_points = {}
        for msg in messages:
            speaker = msg.get("speaker", "未知")
            content = msg.get("content", "")
            
            if speaker not in speaker_points:
                speaker_points[speaker] = []
            
            # 提取关键句子（简化版本）
            sentences = content.split('。')
            key_sentences = [s.strip() for s in sentences if len(s.strip()) > 10][:2]  # 最多2句
            
            if key_sentences:
                speaker_points[speaker].extend(key_sentences)
        
        # 生成摘要
        summary_parts = []
        for speaker, points in speaker_points.items():
            if points:
                # 合并该发言者的观点
                combined = "；".join(points[:3])  # 最多3个要点
                summary_parts.append(f"{speaker}: {combined}")
        
        summary = "【历史讨论摘要】\n" + "\n".join(summary_parts)
        
        # 长度控制
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
    
    def manage_context(self, 
                      system_prompt: str,
                      conversation_history: List[Dict],
                      current_prompt: str) -> Tuple[List[Dict], Dict]:
        """
        管理上下文，确保不超过token限制
        
        Args:
            system_prompt: 系统提示
            conversation_history: 对话历史
            current_prompt: 当前提示
            
        Returns:
            (优化后的消息列表, 统计信息)
        """
        
        # 基础消息
        base_messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": current_prompt}
        ]
        
        base_tokens = sum(self.estimate_tokens(msg["content"]) for msg in base_messages)
        available_tokens = self.max_context_tokens - base_tokens
        
        stats = {
            "total_history_messages": len(conversation_history),
            "base_tokens": base_tokens,
            "available_tokens": available_tokens,
            "strategy": "none"
        }
        
        if not conversation_history:
            return base_messages, stats
        
        # 计算历史对话token
        history_tokens = sum(
            self.estimate_tokens(msg.get("content", "")) 
            for msg in conversation_history
        )
        
        # 确定策略
        strategy = self.get_context_strategy(len(conversation_history), history_tokens)
        stats["strategy"] = strategy
        
        # 应用策略
        if strategy == "full_history" and history_tokens <= available_tokens:
            # 完整历史
            context_content = self._format_full_history(conversation_history)
            stats["used_messages"] = len(conversation_history)
            
        elif strategy == "sliding_window":
            # 滑动窗口
            context_content = self._format_sliding_window(conversation_history, available_tokens)
            
        else:
            # 摘要+最近
            context_content = self._format_summary_recent(conversation_history, available_tokens)
        
        # 更新用户消息
        final_messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"{current_prompt}\n\n{context_content}"}
        ]
        
        # 最终统计
        final_tokens = sum(self.estimate_tokens(msg["content"]) for msg in final_messages)
        stats["final_tokens"] = final_tokens
        stats["token_usage_ratio"] = final_tokens / self.max_context_tokens
        
        return final_messages, stats
    
    def _format_full_history(self, history: List[Dict]) -> str:
        """格式化完整历史"""
        formatted = "【完整讨论历史】\n"
        for i, msg in enumerate(history):
            speaker = msg.get("speaker", "未知")
            content = msg.get("content", "")
            formatted += f"{i+1}. {speaker}: {content}\n"
        return formatted
    
    def _format_sliding_window(self, history: List[Dict], available_tokens: int) -> str:
        """滑动窗口格式化"""
        # 从最新消息开始选择
        selected_messages = []
        current_tokens = 0
        
        for msg in reversed(history):
            msg_tokens = self.estimate_tokens(msg.get("content", ""))
            if current_tokens + msg_tokens < available_tokens * 0.8:  # 留20%缓冲
                selected_messages.insert(0, msg)
                current_tokens += msg_tokens
            else:
                break
        
        formatted = f"【最近{len(selected_messages)}条讨论】\n"
        for msg in selected_messages:
            speaker = msg.get("speaker", "未知")
            content = msg.get("content", "")
            formatted += f"• {speaker}: {content}\n"
        
        return formatted
    
    def _format_summary_recent(self, history: List[Dict], available_tokens: int) -> str:
        """摘要+最近消息格式化"""
        # 分割：前面用于摘要，后面保持完整
        split_point = max(len(history) - 6, len(history) // 2)
        
        early_messages = history[:split_point]
        recent_messages = history[split_point:]
        
        # 生成摘要
        summary = self.create_summary(early_messages, max_length=200)
        
        # 格式化最近消息
        recent_formatted = "【最近讨论详情】\n"
        for msg in recent_messages:
            speaker = msg.get("speaker", "未知")
            content = msg.get("content", "")
            recent_formatted += f"• {speaker}: {content}\n"
        
        return f"{summary}\n\n{recent_formatted}"
    
    def get_statistics(self, conversation_history: List[Dict]) -> Dict:
        """获取统计信息"""
        if not conversation_history:
            return {"total_messages": 0, "total_tokens": 0}
        
        total_tokens = sum(
            self.estimate_tokens(msg.get("content", "")) 
            for msg in conversation_history
        )
        
        speaker_stats = {}
        for msg in conversation_history:
            speaker = msg.get("speaker", "未知")
            if speaker not in speaker_stats:
                speaker_stats[speaker] = {"count": 0, "tokens": 0}
            
            speaker_stats[speaker]["count"] += 1
            speaker_stats[speaker]["tokens"] += self.estimate_tokens(msg.get("content", ""))
        
        return {
            "total_messages": len(conversation_history),
            "total_tokens": total_tokens,
            "speaker_stats": speaker_stats,
            "estimated_context_ratio": total_tokens / self.max_context_tokens
        }

# 全局实例
simple_context_manager = SimpleContextManager(max_tokens=4000)

def get_simple_managed_context(system_prompt: str, 
                              conversation_history: List[Dict], 
                              current_prompt: str) -> Tuple[List[Dict], Dict]:
    """获取简化版管理的上下文"""
    return simple_context_manager.manage_context(system_prompt, conversation_history, current_prompt)

def get_simple_context_statistics(conversation_history: List[Dict]) -> Dict:
    """获取简化版上下文统计"""
    return simple_context_manager.get_statistics(conversation_history)
