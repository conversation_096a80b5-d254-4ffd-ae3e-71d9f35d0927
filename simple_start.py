#!/usr/bin/env python3
"""
简化启动脚本 - 修复expander嵌套问题后使用
"""

import streamlit as st
import subprocess
import sys
import os

def main():
    """简化启动主函数"""
    
    print("🎯 战略推演系统 - 简化启动")
    print("=" * 50)
    
    # 检查是否在正确目录
    if not os.path.exists("demo_app.py"):
        print("❌ 找不到 demo_app.py 文件")
        print("请确保在 strategic_simulation 目录中运行此脚本")
        input("按回车键退出...")
        return
    
    # 检查环境变量文件
    if not os.path.exists(".env"):
        print("📝 创建 .env 配置文件...")
        with open(".env", "w", encoding="utf-8") as f:
            f.write("""# SiliconFlow API Configuration
OPENAI_API_KEY=sk-xxdbfsgbcrcapwbjdaohlxkilvbmwvxsurallxrunlrrggbw
OPENAI_BASE_URL=https://api.siliconflow.cn/v1
OPENAI_MODEL=Qwen/Qwen3-8B

# Model Configuration
MODEL_PROVIDER=siliconflow
MODEL_NAME=Qwen/Qwen3-8B
TEMPERATURE=0.7
MAX_TOKENS=2000

# System Configuration
DEMO_MODE=false
LOG_LEVEL=INFO
""")
        print("✅ .env 文件已创建")
    
    print("🚀 启动Web界面...")
    print("📱 浏览器将打开: http://localhost:8501")
    print("🛑 按 Ctrl+C 停止系统")
    print()
    
    try:
        # 启动streamlit - 使用完整的推演系统
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "demo_app.py",
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ])
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
