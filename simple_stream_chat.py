#!/usr/bin/env python3
"""
简化版流式聊天室 - 确保流式输出正常工作
"""

import streamlit as st
import time
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 设置页面配置
st.set_page_config(
    page_title="流式聊天测试",
    page_icon="💬",
    layout="wide"
)

# 导入AI功能
try:
    from utils.llm_config import generate_response
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False


def simple_stream_display(content: str, agent_name: str, typing_speed: float = 0.05):
    """简单的流式显示效果"""
    
    # 创建占位符
    placeholder = st.empty()
    displayed_text = ""
    
    # 按字符逐步显示
    for char in content:
        displayed_text += char
        
        # 更新显示
        placeholder.markdown(f"""
        <div style="background: #f0f0f0; padding: 15px; border-radius: 10px; margin: 10px 0;">
            <strong>{agent_name}</strong> 正在发言...<br><br>
            {displayed_text}<span style="animation: blink 1s infinite;">|</span>
        </div>
        
        <style>
        @keyframes blink {{
            0%, 50% {{ opacity: 1; }}
            51%, 100% {{ opacity: 0; }}
        }}
        </style>
        """, unsafe_allow_html=True)
        
        time.sleep(typing_speed)
    
    # 最终显示
    timestamp = datetime.now().strftime("%H:%M:%S")
    placeholder.markdown(f"""
    <div style="background: #f0f0f0; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <strong>{agent_name}</strong> - {timestamp}<br><br>
        {displayed_text}
    </div>
    """, unsafe_allow_html=True)
    
    return displayed_text


def generate_ai_response(agent_name: str, scenario: str):
    """生成AI回应"""
    
    if AI_AVAILABLE and generate_response:
        messages = [
            {"role": "system", "content": f"你是{agent_name}，请用中文回应，控制在100字以内。"},
            {"role": "user", "content": f"场景：{scenario}\n请分析并提出建议。"}
        ]
        
        try:
            response = generate_response(messages, max_tokens=150, temperature=0.8)
            return response
        except Exception as e:
            return f"我是{agent_name}，针对当前情况，我认为需要谨慎分析各方面因素。"
    else:
        return f"[演示] 我是{agent_name}，针对当前情况，我认为需要谨慎分析各方面因素。"


def main():
    """主函数"""
    
    st.title("💬 流式输出测试聊天室")
    
    # 初始化
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'agents' not in st.session_state:
        st.session_state.agents = ["特朗普", "军事专家", "政治专家"]
    
    if 'current_agent' not in st.session_state:
        st.session_state.current_agent = 0
    
    # 侧边栏设置
    with st.sidebar:
        st.header("🎮 设置")
        
        scenario = st.text_area(
            "推演场景:",
            value="台海危机爆发可能性下的美国战略应对",
            height=100
        )
        
        typing_speed = st.slider("打字速度", 0.01, 0.2, 0.05, 0.01)
        
        enable_stream = st.checkbox("启用流式输出", value=True)
        
        st.markdown("**参与智能体:**")
        for agent in st.session_state.agents:
            st.write(f"• {agent}")
        
        if st.button("🎬 开始推演"):
            if not st.session_state.messages:
                opening_msg = {
                    "speaker": "推演导调",
                    "content": f"欢迎参加{scenario}推演讨论。请各位专家依次发言。",
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(opening_msg)
                st.rerun()
        
        if st.button("➡️ 下一位发言"):
            if st.session_state.agents:
                current_agent = st.session_state.agents[st.session_state.current_agent % len(st.session_state.agents)]
                
                if enable_stream:
                    # 流式显示
                    response = generate_ai_response(current_agent, scenario)
                    displayed_response = simple_stream_display(response, current_agent, typing_speed)
                    
                    # 添加到消息列表
                    new_msg = {
                        "speaker": current_agent,
                        "content": displayed_response,
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    }
                    st.session_state.messages.append(new_msg)
                else:
                    # 普通显示
                    response = generate_ai_response(current_agent, scenario)
                    new_msg = {
                        "speaker": current_agent,
                        "content": response,
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    }
                    st.session_state.messages.append(new_msg)
                
                st.session_state.current_agent += 1
                st.rerun()
        
        if st.button("🔄 重置"):
            st.session_state.messages = []
            st.session_state.current_agent = 0
            st.rerun()
    
    # 主聊天区域
    st.markdown("### 💬 讨论记录")
    
    # 显示所有消息
    for msg in st.session_state.messages:
        st.markdown(f"""
        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #007AFF;">
            <strong>{msg['speaker']}</strong> - {msg['timestamp']}<br><br>
            {msg['content']}
        </div>
        """, unsafe_allow_html=True)
    
    # 用户输入
    st.markdown("### 💬 参与讨论")
    user_input = st.text_area("输入您的观点:", height=100)
    
    if st.button("📤 发送"):
        if user_input:
            user_msg = {
                "speaker": "用户",
                "content": user_input,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(user_msg)
            st.rerun()
    
    # 实时流式演示
    st.markdown("### 🌊 实时流式演示")
    
    if st.button("🧪 测试流式输出"):
        test_content = "这是一个流式输出测试。我们可以看到文字逐个字符出现，就像真人在打字一样。这种效果可以让AI对话更加生动有趣。"
        simple_stream_display(test_content, "测试智能体", typing_speed)


if __name__ == "__main__":
    main()
