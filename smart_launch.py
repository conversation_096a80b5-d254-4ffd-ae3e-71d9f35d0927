#!/usr/bin/env python3
"""
智能启动脚本 - 自动检测可用端口
"""

import subprocess
import sys
import socket

def check_port(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=8501, max_attempts=10):
    """查找可用端口"""
    for i in range(max_attempts):
        port = start_port + i
        if check_port(port):
            return port
    return None

def kill_existing_streamlit():
    """尝试关闭现有的streamlit进程"""
    try:
        if sys.platform == "win32":
            # Windows
            subprocess.run(["taskkill", "/F", "/IM", "streamlit.exe"], 
                         capture_output=True, check=False)
            subprocess.run(["taskkill", "/F", "/IM", "python.exe"], 
                         capture_output=True, check=False)
        else:
            # Linux/Mac
            subprocess.run(["pkill", "-f", "streamlit"], 
                         capture_output=True, check=False)
        return True
    except:
        return False

def main():
    print("🎯 智能启动战略推演系统...")
    print("🔍 检测端口状态...")
    
    # 首先尝试默认端口
    preferred_port = 8501
    
    if check_port(preferred_port):
        port = preferred_port
        print(f"✅ 端口 {port} 可用")
    else:
        print(f"⚠️ 端口 {preferred_port} 被占用")
        
        # 询问用户是否关闭现有进程
        print("\n选择操作:")
        print("1. 🔄 关闭现有进程并使用端口8501")
        print("2. 🔀 使用其他可用端口")
        print("3. ❌ 退出")
        
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == "1":
            print("🔄 正在关闭现有进程...")
            if kill_existing_streamlit():
                print("✅ 进程已关闭")
                # 等待一下让端口释放
                import time
                time.sleep(2)
                
                if check_port(preferred_port):
                    port = preferred_port
                    print(f"✅ 端口 {port} 现在可用")
                else:
                    print("⚠️ 端口仍被占用，寻找其他端口...")
                    port = find_available_port(8502)
            else:
                print("⚠️ 无法关闭进程，寻找其他端口...")
                port = find_available_port(8502)
                
        elif choice == "2":
            print("🔍 寻找可用端口...")
            port = find_available_port(8502)
            
        else:
            print("👋 退出")
            return
    
    if port is None:
        print("❌ 找不到可用端口 (8501-8510)")
        print("💡 请手动关闭其他Streamlit应用或重启电脑")
        return
    
    print(f"🚀 启动应用在端口 {port}")
    print(f"📱 浏览器将打开: http://localhost:{port}")
    print("🛑 按 Ctrl+C 停止")
    print()
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main_app.py",
            f"--server.port={port}",
            "--server.address=localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 已停止")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
