"""
Base Strategic Agent

Base class for all strategic simulation agents, extending TinyTroupe's Tiny<PERSON>erson
with strategic simulation specific capabilities.
"""

import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime

from tinytroupe.agent import Tiny<PERSON>erson
from tinytroupe.control import transactional
from tinytroupe import config_manager

from ..memory.memory_manager import MemoryManager
from ..knowledge.rag_system import RAGSystem


class BaseStrategicAgent(TinyPerson):
    """
    Base class for strategic simulation agents.
    
    Extends TinyPerson with strategic simulation specific capabilities:
    - Enhanced memory management
    - Knowledge base integration (RAG)
    - Strategic thinking patterns
    - Decision analysis capabilities
    """
    
    def __init__(self, name: str, agent_config: Dict[str, Any], 
                 knowledge_base_path: Optional[str] = None,
                 memory_manager: Optional[MemoryManager] = None):
        """
        Initialize a strategic agent.
        
        Args:
            name: Agent name
            agent_config: Agent configuration from config file
            knowledge_base_path: Path to agent's knowledge base
            memory_manager: Shared memory manager instance
        """
        super().__init__(name)
        
        self.agent_config = agent_config
        self.agent_type = agent_config.get('type', 'generic')
        self.role = agent_config.get('role', 'Participant')
        
        # Initialize persona from config
        self._setup_persona(agent_config.get('persona', {}))
        
        # Initialize memory management
        self.memory_manager = memory_manager or MemoryManager()
        
        # Initialize knowledge base (RAG)
        self.rag_system = None
        if knowledge_base_path and os.path.exists(knowledge_base_path):
            self.rag_system = RAGSystem(knowledge_base_path)
        
        # Strategic thinking capabilities
        self.strategic_context = {}
        self.decision_history = []
        self.current_scenario = None
        
        # Discussion state
        self.discussion_state = {
            'current_round': 0,
            'total_contributions': 0,
            'last_contribution_time': None,
            'position_stated': False
        }
    
    def _setup_persona(self, persona_config: Dict[str, Any]):
        """Setup agent persona from configuration."""
        
        # Basic information
        if 'age' in persona_config:
            self.define('age', persona_config['age'])
        
        # Occupation details
        if 'occupation' in persona_config:
            occupation = persona_config['occupation']
            self.define('occupation', occupation.get('title', ''))
            self.define('company', occupation.get('company', ''))
            self.define('occupation_description', occupation.get('description', ''))
        
        # Personality traits
        if 'personality' in persona_config:
            personality = persona_config['personality']
            self.define('personality_traits', personality.get('traits', []))
            self.define('communication_style', personality.get('communication_style', ''))
            self.define('decision_making_style', personality.get('decision_making', ''))
            self.define('core_values', personality.get('core_values', []))
            
            if 'emotional_triggers' in personality:
                self.define('emotional_triggers', personality['emotional_triggers'])
        
        # Background information
        if 'background' in persona_config:
            background = persona_config['background']
            for key, value in background.items():
                self.define(f'background_{key}', value)
        
        # Expertise areas
        if 'expertise' in persona_config:
            self.define('expertise_areas', persona_config['expertise'])
        
        # Political positions (for political agents)
        if 'political_positions' in persona_config:
            for position_type, position in persona_config['political_positions'].items():
                self.define(f'position_{position_type}', position)
        
        # Language patterns (for realistic communication)
        if 'language_patterns' in persona_config:
            patterns = persona_config['language_patterns']
            for pattern_type, pattern in patterns.items():
                self.define(f'language_{pattern_type}', pattern)
    
    @transactional()
    def set_scenario_context(self, scenario: Dict[str, Any]):
        """Set the current scenario context for strategic thinking."""
        self.current_scenario = scenario
        self.strategic_context.update({
            'scenario_title': scenario.get('title', ''),
            'scenario_description': scenario.get('description', ''),
            'key_considerations': scenario.get('key_considerations', []),
            'scenario_start_time': datetime.now().isoformat()
        })
        
        # Store scenario in memory
        self.memory_manager.store_scenario_context(self.name, scenario)
    
    @transactional()
    def retrieve_relevant_knowledge(self, query: str, top_k: int = 5) -> List[str]:
        """Retrieve relevant knowledge from RAG system."""
        if not self.rag_system:
            return []
        
        try:
            relevant_docs = self.rag_system.retrieve(query, top_k=top_k)
            return [doc['content'] for doc in relevant_docs]
        except Exception as e:
            print(f"Error retrieving knowledge for {self.name}: {e}")
            return []
    
    @transactional()
    def analyze_strategic_situation(self, situation: str) -> str:
        """
        Analyze a strategic situation from the agent's perspective.
        This method should be overridden by specific agent types.
        """
        # Retrieve relevant knowledge
        relevant_knowledge = self.retrieve_relevant_knowledge(situation)
        
        # Get relevant memories
        relevant_memories = self.memory_manager.get_relevant_memories(
            self.name, situation, limit=5
        )
        
        # Construct analysis prompt
        analysis_prompt = f"""
        As {self.get('occupation', 'a strategic advisor')}, analyze the following situation:
        
        Situation: {situation}
        
        Consider your background:
        - Role: {self.role}
        - Expertise: {self.get('expertise_areas', [])}
        - Core Values: {self.get('core_values', [])}
        
        Relevant Knowledge:
        {chr(10).join(relevant_knowledge[:3]) if relevant_knowledge else 'No specific knowledge available'}
        
        Previous Context:
        {chr(10).join([str(mem) for mem in relevant_memories[:2]]) if relevant_memories else 'No previous context'}
        
        Provide a strategic analysis including:
        1. Key factors and considerations
        2. Potential risks and opportunities  
        3. Your recommended approach
        4. Rationale based on your expertise and values
        """
        
        return self._generate_response(analysis_prompt)
    
    @transactional()
    def contribute_to_discussion(self, topic: str, previous_contributions: List[str] = None) -> str:
        """
        Contribute to a strategic discussion.
        """
        previous_contributions = previous_contributions or []
        
        # Update discussion state
        self.discussion_state['current_round'] += 1
        self.discussion_state['total_contributions'] += 1
        self.discussion_state['last_contribution_time'] = datetime.now().isoformat()
        
        # Retrieve relevant knowledge and memories
        relevant_knowledge = self.retrieve_relevant_knowledge(topic)
        relevant_memories = self.memory_manager.get_relevant_memories(self.name, topic)
        
        # Analyze previous contributions
        previous_context = ""
        if previous_contributions:
            previous_context = f"""
            Previous contributions to consider:
            {chr(10).join([f"- {contrib}" for contrib in previous_contributions[-3:]])}
            """
        
        # Generate contribution
        contribution_prompt = f"""
        You are participating in a strategic discussion about: {topic}
        
        Your role: {self.role}
        Your expertise: {self.get('expertise_areas', [])}
        Your communication style: {self.get('communication_style', 'professional')}
        
        {previous_context}
        
        Relevant knowledge from your background:
        {chr(10).join(relevant_knowledge[:2]) if relevant_knowledge else 'Drawing on general expertise'}
        
        Provide your perspective on this topic. Be specific, actionable, and true to your character.
        Consider both the strategic implications and practical implementation.
        """
        
        contribution = self._generate_response(contribution_prompt)
        
        # Store contribution in memory
        self.memory_manager.store_contribution(
            self.name, topic, contribution, self.discussion_state['current_round']
        )
        
        return contribution
    
    def _generate_response(self, prompt: str) -> str:
        """Generate a response using the agent's LLM capabilities."""
        # This uses TinyPerson's built-in LLM capabilities
        self.think(prompt)
        response = self.act(return_actions=True)
        
        if response and len(response) > 0:
            # Extract the actual response content
            last_action = response[-1] if isinstance(response, list) else response
            if isinstance(last_action, dict) and 'content' in last_action:
                return last_action['content']
            elif isinstance(last_action, str):
                return last_action
        
        return "I need more time to consider this matter."
    
    @transactional()
    def record_decision(self, decision: str, rationale: str, context: Dict[str, Any] = None):
        """Record a strategic decision made by this agent."""
        decision_record = {
            'timestamp': datetime.now().isoformat(),
            'decision': decision,
            'rationale': rationale,
            'context': context or {},
            'scenario': self.current_scenario.get('title', '') if self.current_scenario else '',
            'agent_role': self.role
        }
        
        self.decision_history.append(decision_record)
        self.memory_manager.store_decision(self.name, decision_record)
    
    def get_agent_summary(self) -> Dict[str, Any]:
        """Get a summary of the agent's current state and capabilities."""
        return {
            'name': self.name,
            'type': self.agent_type,
            'role': self.role,
            'expertise': self.get('expertise_areas', []),
            'total_contributions': self.discussion_state['total_contributions'],
            'decisions_made': len(self.decision_history),
            'has_knowledge_base': self.rag_system is not None,
            'current_scenario': self.current_scenario.get('title', '') if self.current_scenario else None
        }
