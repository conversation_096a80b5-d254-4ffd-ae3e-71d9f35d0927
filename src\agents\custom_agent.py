"""
Custom Agent

Allows users to create custom agents with personalized characteristics,
backgrounds, and perspectives for strategic simulations.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from .base_agent import BaseStrategicAgent
from tinytroupe.control import transactional


class CustomAgent(BaseStrategicAgent):
    """
    Custom user-defined agent for strategic simulations.
    
    Allows users to:
    - Define custom personas and backgrounds
    - Set specific expertise areas and perspectives
    - Participate in discussions with unique viewpoints
    - Input real-time text during discussions
    """
    
    def __init__(self, name: str, custom_config: Dict[str, Any],
                 knowledge_base_path: Optional[str] = None,
                 memory_manager=None):
        """Initialize custom agent with user-defined configuration."""
        
        super().__init__(name, custom_config, knowledge_base_path, memory_manager)
        
        # Custom agent specific setup
        self.custom_config = custom_config
        self.user_input_mode = custom_config.get('user_input_mode', False)
        self.user_inputs = []
        
        # Setup custom persona
        self._setup_custom_persona(custom_config)
        
        # Track custom agent state
        self.custom_state = {
            'user_defined_positions': [],
            'real_time_inputs': [],
            'persona_adjustments': [],
            'interaction_preferences': custom_config.get('interaction_preferences', {})
        }
    
    def _setup_custom_persona(self, config: Dict[str, Any]):
        """Setup custom persona from user configuration."""

        persona = config.get('persona', {})

        # Basic custom identity
        self.define('custom_identity', {
            'user_defined_name': config.get('name', 'Custom Participant'),
            'custom_role': config.get('role', 'Strategic Advisor'),
            'background_summary': persona.get('background_summary', 'Experienced professional'),
            'unique_perspective': persona.get('unique_perspective', 'Balanced analytical approach')
        })

        # Enhanced personal information
        if 'age' in persona:
            self.define('age', persona['age'])

        if 'nationality' in persona:
            self.define('nationality', persona['nationality'])

        if 'party_affiliation' in persona:
            self.define('party_affiliation', persona['party_affiliation'])

        # Professional background
        if 'current_position' in persona:
            self.define('current_position', persona['current_position'])

        if 'previous_positions' in persona:
            self.define('previous_positions', persona['previous_positions'])

        if 'education' in persona:
            self.define('education', persona['education'])

        if 'career_highlights' in persona:
            self.define('career_highlights', persona['career_highlights'])

        # Custom expertise and experience
        if 'expertise' in persona:
            self.define('custom_expertise', persona['expertise'])

        if 'specializations' in persona:
            self.define('specializations', persona['specializations'])

        if 'experience' in persona:
            self.define('professional_experience', persona['experience'])

        # Personality and values
        if 'personality_traits' in persona:
            self.define('personality_traits', persona['personality_traits'])

        if 'core_values' in persona:
            self.define('personal_values', persona['core_values'])

        if 'political_philosophy' in persona:
            self.define('political_philosophy', persona['political_philosophy'])

        if 'decision_principles' in persona:
            self.define('decision_principles', persona['decision_principles'])

        # Communication and leadership style
        if 'communication_style' in persona:
            self.define('preferred_communication', persona['communication_style'])

        if 'decision_making_style' in persona:
            self.define('decision_making_style', persona['decision_making_style'])

        if 'leadership_approach' in persona:
            self.define('leadership_approach', persona['leadership_approach'])

        # Custom prompts
        if 'system_prompt' in persona:
            self.define('custom_system_prompt', persona['system_prompt'])

        if 'analysis_prompt_template' in persona:
            self.define('analysis_prompt_template', persona['analysis_prompt_template'])

        if 'discussion_prompt_template' in persona:
            self.define('discussion_prompt_template', persona['discussion_prompt_template'])

        # Knowledge configuration
        if 'knowledge_sources' in persona:
            self.define('knowledge_sources', persona['knowledge_sources'])

        if 'knowledge_areas' in persona:
            self.define('specialized_knowledge', persona['knowledge_areas'])
    
    @transactional()
    def set_user_input_mode(self, enabled: bool):
        """Enable or disable real-time user input mode."""
        self.user_input_mode = enabled
        
        if enabled:
            print(f"User input mode enabled for {self.name}. You can provide real-time input during discussions.")
        else:
            print(f"User input mode disabled for {self.name}. Agent will operate autonomously.")
    
    @transactional()
    def add_user_input(self, user_text: str, context: str = ""):
        """Add real-time user input for the agent to incorporate."""
        
        user_input_record = {
            'timestamp': datetime.now().isoformat(),
            'input_text': user_text,
            'context': context,
            'incorporated': False
        }
        
        self.user_inputs.append(user_input_record)
        self.custom_state['real_time_inputs'].append(user_input_record)
        
        print(f"User input recorded for {self.name}: {user_text[:100]}...")
    
    @transactional()
    def update_persona(self, updates: Dict[str, Any]):
        """Allow dynamic updates to the agent's persona during simulation."""
        
        update_record = {
            'timestamp': datetime.now().isoformat(),
            'updates': updates,
            'reason': updates.get('reason', 'User-requested adjustment')
        }
        
        self.custom_state['persona_adjustments'].append(update_record)
        
        # Apply updates to persona
        for key, value in updates.items():
            if key != 'reason':
                self.define(f'updated_{key}', value)
        
        print(f"Persona updated for {self.name}: {list(updates.keys())}")
    
    @transactional()
    def analyze_strategic_situation(self, situation: str) -> str:
        """Analyze situation from custom agent's perspective."""

        # Get relevant knowledge and user inputs
        relevant_knowledge = self.retrieve_relevant_knowledge(situation)
        recent_user_inputs = [inp for inp in self.user_inputs if not inp['incorporated']]

        # Incorporate user inputs if available
        user_input_context = ""
        if recent_user_inputs:
            user_input_context = f"""
            Recent user guidance:
            {chr(10).join([f"- {inp['input_text']}" for inp in recent_user_inputs[-3:]])}
            """

            # Mark inputs as incorporated
            for inp in recent_user_inputs:
                inp['incorporated'] = True

        # Use custom analysis prompt template if available
        custom_template = self.get('analysis_prompt_template')
        if custom_template:
            analysis_prompt = self._build_custom_analysis_prompt(
                custom_template, situation, user_input_context, relevant_knowledge
            )
        else:
            analysis_prompt = self._build_default_analysis_prompt(
                situation, user_input_context, relevant_knowledge
            )

        return self._generate_response(analysis_prompt)

    def _build_custom_analysis_prompt(self, template: str, situation: str,
                                    user_context: str, knowledge: List[str]) -> str:
        """Build analysis prompt using custom template."""

        # Replace template variables
        prompt = template.format(
            name=self.name,
            role=self.get('custom_role', 'strategic advisor'),
            current_position=self.get('current_position', 'Strategic Advisor'),
            background=self.get('background_summary', 'Experienced professional'),
            education=self.get('education', 'Professional education'),
            expertise=', '.join(self.get('custom_expertise', ['Strategic analysis'])),
            values=', '.join(self.get('personal_values', ['Objective analysis'])),
            personality_traits=', '.join(self.get('personality_traits', ['analytical'])),
            political_philosophy=self.get('political_philosophy', 'Pragmatic approach'),
            communication_style=self.get('preferred_communication', 'Professional'),
            decision_making_style=self.get('decision_making_style', 'Evidence-based'),
            leadership_approach=self.get('leadership_approach', 'Collaborative'),
            situation=situation,
            user_context=user_context,
            relevant_knowledge=chr(10).join(knowledge[:3]) if knowledge else 'Drawing on professional experience'
        )

        return prompt

    def _build_default_analysis_prompt(self, situation: str, user_context: str,
                                     knowledge: List[str]) -> str:
        """Build default analysis prompt."""

        return f"""
        You are {self.name}, a {self.get('custom_role', 'strategic advisor')} participating in this strategic discussion.

        Your background: {self.get('background_summary', 'Experienced professional with diverse expertise')}
        Your current position: {self.get('current_position', 'Strategic Advisor')}
        Your education: {self.get('education', 'Professional education and training')}
        Your expertise: {', '.join(self.get('custom_expertise', ['Strategic analysis', 'Problem solving']))}
        Your values: {', '.join(self.get('personal_values', ['Objective analysis', 'Practical solutions']))}
        Your personality: {', '.join(self.get('personality_traits', ['analytical', 'thoughtful']))}
        Your political philosophy: {self.get('political_philosophy', 'Pragmatic and evidence-based approach')}
        Your communication style: {self.get('preferred_communication', 'Professional and analytical')}
        Your decision-making approach: {self.get('decision_making_style', 'Evidence-based and consultative')}
        Your leadership style: {self.get('leadership_approach', 'Collaborative and experienced-based')}

        SITUATION TO ANALYZE: {situation}

        {user_context}

        Relevant knowledge from your background:
        {chr(10).join(knowledge[:2]) if knowledge else 'Drawing on your professional experience'}

        Provide your strategic analysis considering:
        1. Your unique background, education, and professional experience
        2. Your personal values, political philosophy, and principles
        3. Your expertise and specialized knowledge
        4. Any specific user guidance provided
        5. Practical implementation considerations
        6. Potential risks and opportunities from your perspective
        7. Your characteristic communication and decision-making style

        Be authentic to your defined persona while providing valuable strategic insights.
        Respond in a manner consistent with your personality traits and communication style.
        """
    
    @transactional()
    def contribute_to_discussion(self, topic: str, previous_contributions: List[str] = None) -> str:
        """Contribute to discussion with custom perspective and user input."""
        
        previous_contributions = previous_contributions or []
        
        # Update discussion state
        self.discussion_state['current_round'] += 1
        self.discussion_state['total_contributions'] += 1
        self.discussion_state['last_contribution_time'] = datetime.now().isoformat()
        
        # Check for real-time user input
        if self.user_input_mode:
            print(f"\n[{self.name}] It's your turn to contribute to the discussion about: {topic}")
            print("Previous contributions summary:")
            for i, contrib in enumerate(previous_contributions[-2:], 1):
                print(f"{i}. {contrib[:150]}...")
            
            print(f"\nAs {self.name}, you can either:")
            print("1. Provide specific input for this contribution")
            print("2. Let the agent respond based on its persona")
            
            # In a real implementation, this would wait for user input
            # For now, we'll simulate with any pending user inputs
        
        # Get recent user inputs
        recent_user_inputs = [inp for inp in self.user_inputs if not inp['incorporated']]
        
        # Prepare contribution context
        user_guidance = ""
        if recent_user_inputs:
            user_guidance = f"""
            Your current guidance:
            {chr(10).join([f"- {inp['input_text']}" for inp in recent_user_inputs[-2:]])}
            """
            
            # Mark as incorporated
            for inp in recent_user_inputs:
                inp['incorporated'] = True
        
        # Analyze previous contributions
        previous_context = ""
        if previous_contributions:
            previous_context = f"""
            Previous points made in discussion:
            {chr(10).join([f"- {contrib[:100]}..." for contrib in previous_contributions[-3:]])}
            """
        
        contribution_prompt = f"""
        You are {self.name} contributing to a strategic discussion about: {topic}
        
        Your role: {self.get('custom_role', 'Strategic Advisor')}
        Your expertise: {self.get('custom_expertise', ['Strategic analysis'])}
        Your communication style: {self.get('preferred_communication', 'Professional and analytical')}
        Your decision principles: {self.get('decision_principles', ['Evidence-based', 'Practical'])}
        
        {previous_context}
        
        {user_guidance}
        
        Provide your contribution to this discussion:
        1. Build on or respond to previous contributions as appropriate
        2. Bring your unique perspective and expertise to bear
        3. Incorporate any specific user guidance provided
        4. Be constructive and solution-oriented
        5. Stay true to your defined persona and values
        
        Make your contribution valuable and distinctive.
        """
        
        contribution = self._generate_response(contribution_prompt)
        
        # Store contribution
        self.memory_manager.store_contribution(
            self.name, topic, contribution, self.discussion_state['current_round']
        )
        
        # Record user-defined position if this represents one
        if recent_user_inputs:
            self.custom_state['user_defined_positions'].append({
                'topic': topic,
                'position': contribution,
                'user_inputs': [inp['input_text'] for inp in recent_user_inputs],
                'timestamp': datetime.now().isoformat()
            })
        
        return contribution
    
    @transactional()
    def provide_final_perspective(self, discussion_summary: str) -> str:
        """Provide final perspective on the discussion and decision."""
        
        final_prompt = f"""
        As {self.name}, provide your final perspective on this strategic discussion.
        
        DISCUSSION SUMMARY:
        {discussion_summary}
        
        Your role in this discussion: {self.get('custom_role', 'Strategic Advisor')}
        Your key contributions: {len(self.custom_state['user_defined_positions'])} positions taken
        
        Provide your final thoughts considering:
        1. How well the discussion addressed the key issues
        2. Whether your perspective was adequately considered
        3. Any concerns or additional considerations
        4. Your assessment of the decision-making process
        5. Recommendations for implementation or follow-up
        
        Be honest about your assessment while remaining constructive.
        """
        
        final_perspective = self._generate_response(final_prompt)
        
        # Record final perspective
        self.memory_manager.store_final_perspective(self.name, final_perspective, discussion_summary)
        
        return final_perspective
    
    def get_custom_agent_summary(self) -> Dict[str, Any]:
        """Get summary of custom agent's participation and state."""
        
        base_summary = self.get_agent_summary()
        
        custom_summary = {
            **base_summary,
            'custom_config': {
                'user_input_mode': self.user_input_mode,
                'total_user_inputs': len(self.user_inputs),
                'persona_adjustments': len(self.custom_state['persona_adjustments']),
                'positions_taken': len(self.custom_state['user_defined_positions'])
            },
            'unique_perspective': self.get('unique_perspective', 'Not specified'),
            'custom_expertise': self.get('custom_expertise', []),
            'personal_values': self.get('personal_values', [])
        }
        
        return custom_summary
    
    @classmethod
    def create_from_template(cls, name: str, template_type: str = "general", 
                           custom_overrides: Dict[str, Any] = None) -> 'CustomAgent':
        """Create a custom agent from a predefined template."""
        
        templates = {
            "general": {
                "name": name,
                "role": "Strategic Advisor",
                "persona": {
                    "background_summary": "Experienced professional with broad strategic perspective",
                    "expertise": ["Strategic planning", "Risk analysis", "Decision support"],
                    "values": ["Objective analysis", "Practical solutions", "Stakeholder consideration"],
                    "communication_style": "Professional, analytical, solution-oriented"
                }
            },
            "business": {
                "name": name,
                "role": "Business Strategy Expert",
                "persona": {
                    "background_summary": "Senior business executive with strategic planning experience",
                    "expertise": ["Business strategy", "Market analysis", "Financial planning", "Operations"],
                    "values": ["Efficiency", "Profitability", "Innovation", "Stakeholder value"],
                    "communication_style": "Results-oriented, data-driven, pragmatic"
                }
            },
            "academic": {
                "name": name,
                "role": "Academic Researcher",
                "persona": {
                    "background_summary": "Academic researcher with deep analytical capabilities",
                    "expertise": ["Research methodology", "Policy analysis", "Theoretical frameworks"],
                    "values": ["Evidence-based reasoning", "Intellectual rigor", "Objective analysis"],
                    "communication_style": "Scholarly, detailed, methodical"
                }
            }
        }
        
        base_config = templates.get(template_type, templates["general"])
        
        if custom_overrides:
            # Deep merge custom overrides
            for key, value in custom_overrides.items():
                if key == "persona" and isinstance(value, dict):
                    base_config["persona"].update(value)
                else:
                    base_config[key] = value
        
        return cls(name, base_config)
