"""
Moderator Agent

Strategic simulation director responsible for managing discussion flow,
ensuring comprehensive analysis, and providing objective synthesis.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from .base_agent import BaseStrategicAgent
from tinytroupe.control import transactional


class ModeratorAgent(BaseStrategicAgent):
    """
    Strategic simulation moderator agent.
    
    Responsibilities:
    - Manage discussion flow and phases
    - Ensure all perspectives are heard
    - Synthesize complex information
    - Generate comprehensive analysis and reports
    - Facilitate productive discussions
    - Conduct post-simulation reviews
    """
    
    def __init__(self, agent_config: Dict[str, Any],
                 knowledge_base_path: Optional[str] = None,
                 memory_manager=None):
        """Initialize moderator agent."""
        
        super().__init__("General Moderator", agent_config, knowledge_base_path, memory_manager)
        
        # Moderator-specific setup
        self._setup_moderator_capabilities()
        
        # Simulation state tracking
        self.simulation_state = {
            'current_phase': 'preparation',
            'participants': [],
            'discussion_rounds': 0,
            'key_points_raised': [],
            'decisions_made': [],
            'simulation_start_time': None,
            'phase_transitions': []
        }
        
        # Analysis frameworks
        self.analysis_frameworks = {
            'strategic': ['Objectives', 'Resources', 'Constraints', 'Risks', 'Opportunities'],
            'political': ['Domestic Impact', 'International Relations', 'Public Opinion', 'Coalition Building'],
            'military': ['Capabilities', 'Readiness', 'Doctrine', 'Logistics', 'Intelligence'],
            'economic': ['Costs', 'Benefits', 'Market Impact', 'Trade Effects', 'Resource Allocation']
        }
    
    def _setup_moderator_capabilities(self):
        """Setup moderator-specific capabilities and persona."""
        
        # Professional moderator characteristics
        self.define('moderator_role', {
            'primary_function': 'Strategic simulation director and facilitator',
            'authority_level': 'Process control, not content direction',
            'objectivity_standard': 'Neutral facilitation, comprehensive analysis',
            'expertise_areas': ['Strategic planning', 'Simulation methodology', 'Group dynamics', 'Decision analysis']
        })
        
        # Facilitation principles
        self.define('facilitation_principles', {
            'inclusivity': 'Ensure all perspectives are heard and considered',
            'objectivity': 'Maintain neutral stance while ensuring thorough analysis',
            'structure': 'Provide clear process and time management',
            'synthesis': 'Integrate diverse viewpoints into coherent analysis'
        })
        
        # Communication style for moderation
        self.define('moderation_style', {
            'tone': 'Professional, authoritative, diplomatic',
            'approach': 'Structured, systematic, comprehensive',
            'questioning': 'Socratic method, probing for depth and clarity',
            'summarization': 'Objective, balanced, action-oriented'
        })
    
    @transactional()
    def initialize_simulation(self, scenario: Dict[str, Any], participants: List[str]) -> str:
        """Initialize and open the strategic simulation."""
        
        self.simulation_state.update({
            'current_phase': 'opening',
            'participants': participants,
            'simulation_start_time': datetime.now().isoformat(),
            'scenario': scenario
        })
        
        opening_statement = f"""
        Welcome to this strategic simulation exercise.
        
        SCENARIO: {scenario.get('title', 'Strategic Decision Exercise')}
        
        SITUATION OVERVIEW:
        {scenario.get('description', 'We will be analyzing a strategic situation requiring careful consideration.')}
        
        INITIAL CONTEXT:
        {scenario.get('initial_context', 'Please consider all relevant factors in your analysis.')}
        
        PARTICIPANTS:
        {chr(10).join([f"- {participant}" for participant in participants])}
        
        KEY CONSIDERATIONS:
        {chr(10).join([f"- {consideration}" for consideration in scenario.get('key_considerations', [])])}
        
        PROCESS:
        1. Initial position statements from each participant
        2. Open discussion and analysis phase
        3. Decision formulation by designated decision-maker
        4. Post-decision analysis and lessons learned
        
        We will proceed systematically to ensure comprehensive analysis of this strategic matter.
        Let us begin with initial position statements.
        """
        
        # Record simulation start
        self.memory_manager.store_simulation_event(
            'simulation_start', 
            {'scenario': scenario, 'participants': participants}
        )
        
        return opening_statement
    
    @transactional()
    def facilitate_discussion_round(self, round_number: int, topic_focus: str, 
                                  previous_contributions: List[Dict[str, str]]) -> str:
        """Facilitate a discussion round with specific focus."""
        
        self.simulation_state['discussion_rounds'] = round_number
        
        # Analyze previous contributions
        contribution_summary = self._summarize_contributions(previous_contributions)
        
        # Identify gaps or areas needing more exploration
        analysis_gaps = self._identify_analysis_gaps(previous_contributions, topic_focus)
        
        facilitation_prompt = f"""
        DISCUSSION ROUND {round_number} - FOCUS: {topic_focus}
        
        CONTRIBUTIONS SO FAR:
        {contribution_summary}
        
        ANALYSIS GAPS IDENTIFIED:
        {chr(10).join([f"- {gap}" for gap in analysis_gaps])}
        
        As the simulation director, I want to ensure we have comprehensive coverage of this strategic matter.
        
        For this round, I encourage participants to address:
        1. The specific gaps identified above
        2. Practical implementation considerations
        3. Potential unintended consequences
        4. Resource and timeline requirements
        5. Stakeholder impacts and reactions
        
        Please provide focused, actionable insights that build on our previous discussion.
        """
        
        return facilitation_prompt
    
    def _summarize_contributions(self, contributions: List[Dict[str, str]]) -> str:
        """Summarize key points from participant contributions."""
        
        if not contributions:
            return "No contributions yet."
        
        summary_points = []
        for contrib in contributions:
            participant = contrib.get('participant', 'Unknown')
            content = contrib.get('content', '')[:200] + "..." if len(contrib.get('content', '')) > 200 else contrib.get('content', '')
            summary_points.append(f"{participant}: {content}")
        
        return chr(10).join(summary_points)
    
    def _identify_analysis_gaps(self, contributions: List[Dict[str, str]], topic_focus: str) -> List[str]:
        """Identify gaps in the analysis based on contributions."""
        
        # Get relevant framework for analysis
        framework = self.analysis_frameworks.get('strategic', [])
        
        # Simple gap analysis (in real implementation, this could be more sophisticated)
        covered_areas = set()
        for contrib in contributions:
            content = contrib.get('content', '').lower()
            for area in framework:
                if area.lower() in content:
                    covered_areas.add(area)
        
        gaps = [area for area in framework if area not in covered_areas]
        
        # Add topic-specific gaps
        if 'military' in topic_focus.lower():
            military_areas = ['Force readiness', 'Logistics', 'Intelligence requirements', 'Rules of engagement']
            gaps.extend([area for area in military_areas if not any(area.lower() in contrib.get('content', '').lower() for contrib in contributions)])
        
        return gaps[:5]  # Limit to top 5 gaps
    
    @transactional()
    def synthesize_discussion(self, all_contributions: List[Dict[str, str]], 
                            scenario_context: Dict[str, Any]) -> str:
        """Synthesize the entire discussion into a comprehensive analysis."""
        
        synthesis_prompt = f"""
        As the strategic simulation director, I will now synthesize our comprehensive discussion.
        
        SCENARIO: {scenario_context.get('title', 'Strategic Decision Exercise')}
        
        ANALYSIS SYNTHESIS:
        
        1. STRATEGIC SITUATION ASSESSMENT:
        Based on our discussion, the key strategic factors are:
        {self._extract_strategic_factors(all_contributions)}
        
        2. OPTIONS ANALYSIS:
        The main courses of action identified include:
        {self._extract_options(all_contributions)}
        
        3. RISK ASSESSMENT:
        Critical risks and mitigation strategies:
        {self._extract_risks(all_contributions)}
        
        4. RESOURCE REQUIREMENTS:
        Implementation considerations:
        {self._extract_resources(all_contributions)}
        
        5. STAKEHOLDER IMPACTS:
        Effects on key stakeholders:
        {self._extract_stakeholder_impacts(all_contributions)}
        
        6. RECOMMENDATION FRAMEWORK:
        For the decision-maker's consideration:
        - Immediate actions required
        - Medium-term strategic moves
        - Long-term implications
        - Success metrics and monitoring
        
        This synthesis provides the foundation for strategic decision-making.
        """
        
        synthesis = self._generate_response(synthesis_prompt)
        
        # Store synthesis in memory
        self.memory_manager.store_synthesis(scenario_context.get('title', ''), synthesis, all_contributions)
        
        return synthesis
    
    def _extract_strategic_factors(self, contributions: List[Dict[str, str]]) -> str:
        """Extract key strategic factors from contributions."""
        # Simplified extraction - in practice, this could use NLP
        factors = []
        for contrib in contributions:
            content = contrib.get('content', '')
            if any(keyword in content.lower() for keyword in ['strategic', 'important', 'critical', 'key']):
                # Extract relevant sentences
                sentences = content.split('.')
                for sentence in sentences[:2]:  # Take first 2 sentences
                    if len(sentence.strip()) > 20:
                        factors.append(f"- {sentence.strip()}")
        
        return chr(10).join(factors[:5]) if factors else "- Multiple strategic considerations discussed"
    
    def _extract_options(self, contributions: List[Dict[str, str]]) -> str:
        """Extract strategic options from contributions."""
        options = []
        for contrib in contributions:
            content = contrib.get('content', '')
            if any(keyword in content.lower() for keyword in ['option', 'approach', 'strategy', 'recommend', 'should']):
                sentences = content.split('.')
                for sentence in sentences[:2]:
                    if len(sentence.strip()) > 15:
                        options.append(f"- {sentence.strip()}")
        
        return chr(10).join(options[:4]) if options else "- Various strategic approaches discussed"
    
    def _extract_risks(self, contributions: List[Dict[str, str]]) -> str:
        """Extract risks and concerns from contributions."""
        risks = []
        for contrib in contributions:
            content = contrib.get('content', '')
            if any(keyword in content.lower() for keyword in ['risk', 'concern', 'danger', 'problem', 'challenge']):
                sentences = content.split('.')
                for sentence in sentences[:2]:
                    if len(sentence.strip()) > 15:
                        risks.append(f"- {sentence.strip()}")
        
        return chr(10).join(risks[:4]) if risks else "- Risk considerations were discussed"
    
    def _extract_resources(self, contributions: List[Dict[str, str]]) -> str:
        """Extract resource requirements from contributions."""
        resources = []
        for contrib in contributions:
            content = contrib.get('content', '')
            if any(keyword in content.lower() for keyword in ['resource', 'cost', 'budget', 'personnel', 'time', 'capability']):
                sentences = content.split('.')
                for sentence in sentences[:2]:
                    if len(sentence.strip()) > 15:
                        resources.append(f"- {sentence.strip()}")
        
        return chr(10).join(resources[:4]) if resources else "- Resource requirements were considered"
    
    def _extract_stakeholder_impacts(self, contributions: List[Dict[str, str]]) -> str:
        """Extract stakeholder impacts from contributions."""
        impacts = []
        for contrib in contributions:
            content = contrib.get('content', '')
            if any(keyword in content.lower() for keyword in ['impact', 'affect', 'stakeholder', 'public', 'ally', 'partner']):
                sentences = content.split('.')
                for sentence in sentences[:2]:
                    if len(sentence.strip()) > 15:
                        impacts.append(f"- {sentence.strip()}")
        
        return chr(10).join(impacts[:4]) if impacts else "- Stakeholder impacts were analyzed"
    
    @transactional()
    def conduct_after_action_review(self, final_decision: Dict[str, Any], 
                                  simulation_data: Dict[str, Any]) -> str:
        """Conduct comprehensive after-action review."""
        
        review_prompt = f"""
        AFTER-ACTION REVIEW - STRATEGIC SIMULATION
        
        SCENARIO: {simulation_data.get('scenario', {}).get('title', 'Strategic Exercise')}
        DURATION: {self._calculate_simulation_duration()}
        PARTICIPANTS: {len(self.simulation_state.get('participants', []))} strategic advisors
        
        FINAL DECISION:
        {final_decision.get('decision', 'Decision details not available')}
        
        SIMULATION ANALYSIS:
        
        1. PROCESS EFFECTIVENESS:
        - Discussion quality and depth
        - Participant engagement levels
        - Information sharing effectiveness
        - Decision-making process efficiency
        
        2. STRATEGIC INSIGHTS GAINED:
        - Key learning points from the exercise
        - Alternative approaches identified
        - Risk factors properly assessed
        - Implementation considerations addressed
        
        3. DECISION QUALITY ASSESSMENT:
        - Alignment with strategic objectives
        - Consideration of multiple perspectives
        - Risk mitigation adequacy
        - Implementation feasibility
        
        4. LESSONS LEARNED:
        - Process improvements for future simulations
        - Knowledge gaps identified
        - Collaboration effectiveness
        - Decision support tool effectiveness
        
        5. RECOMMENDATIONS:
        - For future strategic planning processes
        - For decision-making improvements
        - For stakeholder engagement
        - For risk management enhancement
        
        This simulation provided valuable insights into strategic decision-making under complex conditions.
        """
        
        review = self._generate_response(review_prompt)
        
        # Store complete simulation record
        self.memory_manager.store_simulation_complete(
            simulation_data.get('scenario', {}).get('title', ''),
            final_decision,
            review,
            self.simulation_state
        )
        
        return review
    
    def _calculate_simulation_duration(self) -> str:
        """Calculate total simulation duration."""
        if self.simulation_state.get('simulation_start_time'):
            start_time = datetime.fromisoformat(self.simulation_state['simulation_start_time'])
            duration = datetime.now() - start_time
            hours = duration.seconds // 3600
            minutes = (duration.seconds % 3600) // 60
            return f"{hours}h {minutes}m"
        return "Duration not available"
