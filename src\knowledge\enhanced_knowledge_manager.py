#!/usr/bin/env python3
"""
Enhanced Knowledge Manager

支持大规模文档处理、embedding和智能检索的知识库管理系统。
"""

import os
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import logging
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    import faiss
    import numpy as np
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class DocumentMetadata:
    """文档元数据"""
    filename: str
    file_size: int
    file_hash: str
    upload_time: str
    agent_name: str
    document_type: str
    tags: List[str]
    processing_status: str = "pending"
    chunk_count: int = 0
    embedding_count: int = 0


@dataclass
class ChunkMetadata:
    """文档块元数据"""
    chunk_id: str
    doc_id: str
    content: str
    chunk_index: int
    word_count: int
    sentence_count: int
    embedding: Optional[List[float]] = None
    similarity_score: float = 0.0


class EnhancedKnowledgeManager:
    """
    增强的知识库管理器
    
    特性:
    - 大规模文档处理 (支持上千个文档)
    - 智能文档分块和embedding
    - 增量更新和版本管理
    - 多向量数据库支持
    - 文档去重和相似性检测
    - 异步处理和进度跟踪
    """
    
    def __init__(self, base_path: str = "data/knowledge_bases", config: Dict[str, Any] = None):
        """初始化知识库管理器"""
        
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        self.config = config or {}
        
        # 配置参数
        self.chunk_size = self.config.get('chunk_size', 512)
        self.chunk_overlap = self.config.get('chunk_overlap', 50)
        self.max_workers = self.config.get('max_workers', 4)
        self.batch_size = self.config.get('batch_size', 100)
        self.similarity_threshold = self.config.get('similarity_threshold', 0.8)
        
        # 初始化组件
        self.embedding_model = self._initialize_embedding_model()
        self.vector_db = self._initialize_vector_db()
        
        # 文档存储
        self.documents: Dict[str, DocumentMetadata] = {}
        self.chunks: Dict[str, ChunkMetadata] = {}
        self.agent_knowledge_bases: Dict[str, Dict[str, Any]] = {}
        
        # 加载现有数据
        self._load_existing_data()
    
    def _initialize_embedding_model(self):
        """初始化embedding模型"""
        
        model_name = self.config.get('embedding_model', 'sentence-transformers/all-MiniLM-L6-v2')
        
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                logger.info(f"Loading embedding model: {model_name}")
                return SentenceTransformer(model_name)
            except Exception as e:
                logger.error(f"Failed to load embedding model: {e}")
        
        logger.warning("No embedding model available, using text-based search")
        return None
    
    def _initialize_vector_db(self):
        """初始化向量数据库"""
        
        db_type = self.config.get('vector_db_type', 'chromadb')
        
        if db_type == 'chromadb' and CHROMADB_AVAILABLE:
            return self._initialize_chromadb()
        elif db_type == 'faiss' and FAISS_AVAILABLE:
            return self._initialize_faiss()
        else:
            logger.warning("No vector database available, using file-based storage")
            return None
    
    def _initialize_chromadb(self):
        """初始化ChromaDB"""
        
        try:
            db_path = self.base_path / "chromadb"
            db_path.mkdir(exist_ok=True)
            
            client = chromadb.PersistentClient(path=str(db_path))
            
            # 为每个智能体创建独立的collection
            collections = {}
            for agent_name in self._get_agent_names():
                collection_name = f"kb_{agent_name.lower().replace(' ', '_')}"
                collections[agent_name] = client.get_or_create_collection(
                    name=collection_name,
                    metadata={"description": f"Knowledge base for {agent_name}"}
                )
            
            return {'client': client, 'collections': collections}
            
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB: {e}")
            return None
    
    def _initialize_faiss(self):
        """初始化FAISS"""
        
        try:
            # 为每个智能体创建独立的FAISS索引
            indices = {}
            for agent_name in self._get_agent_names():
                indices[agent_name] = {
                    'index': None,
                    'dimension': None,
                    'chunk_ids': []
                }
            
            return {'indices': indices}
            
        except Exception as e:
            logger.error(f"Failed to initialize FAISS: {e}")
            return None
    
    def _get_agent_names(self) -> List[str]:
        """获取所有智能体名称"""
        
        # 从配置文件加载智能体列表
        agents_config_path = Path("config/agents_config.json")
        if agents_config_path.exists():
            try:
                with open(agents_config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return list(config.get('agents', {}).keys())
            except Exception as e:
                logger.error(f"Failed to load agents config: {e}")
        
        # 默认智能体列表
        return ['trump', 'moderator', 'political_expert', 'military_expert', 'economic_expert']
    
    def _load_existing_data(self):
        """加载现有的知识库数据"""
        
        metadata_file = self.base_path / "metadata.json"
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 加载文档元数据
                for doc_id, doc_data in data.get('documents', {}).items():
                    self.documents[doc_id] = DocumentMetadata(**doc_data)
                
                # 加载块元数据
                for chunk_id, chunk_data in data.get('chunks', {}).items():
                    self.chunks[chunk_id] = ChunkMetadata(**chunk_data)
                
                logger.info(f"Loaded {len(self.documents)} documents and {len(self.chunks)} chunks")
                
            except Exception as e:
                logger.error(f"Failed to load existing data: {e}")
    
    def _save_metadata(self):
        """保存元数据"""
        
        metadata_file = self.base_path / "metadata.json"
        
        # 转换数据为可序列化格式
        documents_data = {
            doc_id: {
                'filename': doc.filename,
                'file_size': doc.file_size,
                'file_hash': doc.file_hash,
                'upload_time': doc.upload_time,
                'agent_name': doc.agent_name,
                'document_type': doc.document_type,
                'tags': doc.tags,
                'processing_status': doc.processing_status,
                'chunk_count': doc.chunk_count,
                'embedding_count': doc.embedding_count
            }
            for doc_id, doc in self.documents.items()
        }
        
        chunks_data = {
            chunk_id: {
                'chunk_id': chunk.chunk_id,
                'doc_id': chunk.doc_id,
                'content': chunk.content,
                'chunk_index': chunk.chunk_index,
                'word_count': chunk.word_count,
                'sentence_count': chunk.sentence_count,
                'embedding': chunk.embedding,
                'similarity_score': chunk.similarity_score
            }
            for chunk_id, chunk in self.chunks.items()
        }
        
        data = {
            'documents': documents_data,
            'chunks': chunks_data,
            'last_updated': datetime.now().isoformat()
        }
        
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
    
    def add_documents_to_agent(self, agent_name: str, files: List[Any], 
                              tags: List[str] = None, process_async: bool = True) -> Dict[str, Any]:
        """
        向指定智能体添加文档
        
        Args:
            agent_name: 智能体名称
            files: 文件列表
            tags: 文档标签
            process_async: 是否异步处理
        
        Returns:
            处理结果统计
        """
        
        logger.info(f"Adding {len(files)} documents to {agent_name}")
        
        results = {
            'agent_name': agent_name,
            'total_files': len(files),
            'processed_files': 0,
            'failed_files': 0,
            'total_chunks': 0,
            'total_embeddings': 0,
            'processing_time': 0,
            'errors': []
        }
        
        start_time = datetime.now()
        
        if process_async:
            # 异步处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_file = {
                    executor.submit(self._process_single_document, agent_name, file, tags): file
                    for file in files
                }
                
                for future in as_completed(future_to_file):
                    file = future_to_file[future]
                    try:
                        file_result = future.result()
                        results['processed_files'] += 1
                        results['total_chunks'] += file_result.get('chunks', 0)
                        results['total_embeddings'] += file_result.get('embeddings', 0)
                    except Exception as e:
                        results['failed_files'] += 1
                        results['errors'].append(f"File {file.name}: {str(e)}")
                        logger.error(f"Failed to process file {file.name}: {e}")
        else:
            # 同步处理
            for file in files:
                try:
                    file_result = self._process_single_document(agent_name, file, tags)
                    results['processed_files'] += 1
                    results['total_chunks'] += file_result.get('chunks', 0)
                    results['total_embeddings'] += file_result.get('embeddings', 0)
                except Exception as e:
                    results['failed_files'] += 1
                    results['errors'].append(f"File {file.name}: {str(e)}")
                    logger.error(f"Failed to process file {file.name}: {e}")
        
        results['processing_time'] = (datetime.now() - start_time).total_seconds()
        
        # 保存元数据
        self._save_metadata()
        
        logger.info(f"Completed processing for {agent_name}: {results}")
        return results
    
    def _process_single_document(self, agent_name: str, file: Any, tags: List[str] = None) -> Dict[str, Any]:
        """处理单个文档"""
        
        # 读取文件内容
        content = file.read().decode('utf-8')
        file_hash = hashlib.md5(content.encode()).hexdigest()
        
        # 检查是否已存在
        if self._document_exists(file_hash):
            logger.info(f"Document {file.name} already exists, skipping")
            return {'chunks': 0, 'embeddings': 0, 'status': 'skipped'}
        
        # 创建文档元数据
        doc_id = f"{agent_name}_{file_hash[:8]}"
        doc_metadata = DocumentMetadata(
            filename=file.name,
            file_size=len(content),
            file_hash=file_hash,
            upload_time=datetime.now().isoformat(),
            agent_name=agent_name,
            document_type=self._detect_document_type(file.name),
            tags=tags or []
        )
        
        # 文档分块
        chunks = self._chunk_document(content, doc_id)
        doc_metadata.chunk_count = len(chunks)
        
        # 生成embedding
        if self.embedding_model:
            embeddings = self._generate_embeddings(chunks)
            doc_metadata.embedding_count = len(embeddings)
            
            # 存储到向量数据库
            self._store_embeddings(agent_name, chunks, embeddings)
        
        # 保存文档和块
        self.documents[doc_id] = doc_metadata
        for chunk in chunks:
            self.chunks[chunk.chunk_id] = chunk
        
        doc_metadata.processing_status = "completed"
        
        return {
            'chunks': len(chunks),
            'embeddings': doc_metadata.embedding_count,
            'status': 'completed'
        }
    
    def _document_exists(self, file_hash: str) -> bool:
        """检查文档是否已存在"""
        
        for doc in self.documents.values():
            if doc.file_hash == file_hash:
                return True
        return False
    
    def _detect_document_type(self, filename: str) -> str:
        """检测文档类型"""
        
        ext = Path(filename).suffix.lower()
        type_mapping = {
            '.txt': 'text',
            '.md': 'markdown',
            '.pdf': 'pdf',
            '.docx': 'word',
            '.json': 'json',
            '.csv': 'csv',
            '.xml': 'xml'
        }
        return type_mapping.get(ext, 'unknown')
    
    def _chunk_document(self, content: str, doc_id: str) -> List[ChunkMetadata]:
        """文档分块"""
        
        # 简单的句子分割
        sentences = self._split_into_sentences(content)
        chunks = []
        
        current_chunk = ""
        current_sentences = []
        chunk_index = 0
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) > self.chunk_size and current_chunk:
                # 创建块
                chunk_id = f"{doc_id}_chunk_{chunk_index}"
                chunk = ChunkMetadata(
                    chunk_id=chunk_id,
                    doc_id=doc_id,
                    content=current_chunk.strip(),
                    chunk_index=chunk_index,
                    word_count=len(current_chunk.split()),
                    sentence_count=len(current_sentences)
                )
                chunks.append(chunk)
                
                # 开始新块，保留重叠
                if self.chunk_overlap > 0 and current_sentences:
                    overlap_sentences = current_sentences[-self.chunk_overlap:]
                    current_chunk = " ".join(overlap_sentences)
                    current_sentences = overlap_sentences.copy()
                else:
                    current_chunk = ""
                    current_sentences = []
                
                chunk_index += 1
            
            current_chunk += " " + sentence if current_chunk else sentence
            current_sentences.append(sentence)
        
        # 添加最后一个块
        if current_chunk.strip():
            chunk_id = f"{doc_id}_chunk_{chunk_index}"
            chunk = ChunkMetadata(
                chunk_id=chunk_id,
                doc_id=doc_id,
                content=current_chunk.strip(),
                chunk_index=chunk_index,
                word_count=len(current_chunk.split()),
                sentence_count=len(current_sentences)
            )
            chunks.append(chunk)
        
        return chunks
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """分割文本为句子"""
        
        import re
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        return sentences
    
    def _generate_embeddings(self, chunks: List[ChunkMetadata]) -> List[List[float]]:
        """生成embedding"""
        
        if not self.embedding_model:
            return []
        
        texts = [chunk.content for chunk in chunks]
        
        try:
            embeddings = self.embedding_model.encode(texts)
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            return []
    
    def _store_embeddings(self, agent_name: str, chunks: List[ChunkMetadata], 
                         embeddings: List[List[float]]):
        """存储embedding到向量数据库"""
        
        if not embeddings or not self.vector_db:
            return
        
        try:
            if 'collections' in self.vector_db:
                # ChromaDB
                collection = self.vector_db['collections'].get(agent_name)
                if collection:
                    documents = [chunk.content for chunk in chunks]
                    ids = [chunk.chunk_id for chunk in chunks]
                    metadatas = [
                        {
                            'doc_id': chunk.doc_id,
                            'chunk_index': chunk.chunk_index,
                            'word_count': chunk.word_count
                        }
                        for chunk in chunks
                    ]
                    
                    collection.add(
                        documents=documents,
                        embeddings=embeddings,
                        metadatas=metadatas,
                        ids=ids
                    )
            
            elif 'indices' in self.vector_db:
                # FAISS
                agent_index = self.vector_db['indices'].get(agent_name)
                if agent_index:
                    embeddings_array = np.array(embeddings, dtype='float32')
                    
                    if agent_index['index'] is None:
                        dimension = embeddings_array.shape[1]
                        agent_index['index'] = faiss.IndexFlatIP(dimension)
                        agent_index['dimension'] = dimension
                    
                    # 归一化
                    faiss.normalize_L2(embeddings_array)
                    agent_index['index'].add(embeddings_array)
                    agent_index['chunk_ids'].extend([chunk.chunk_id for chunk in chunks])
        
        except Exception as e:
            logger.error(f"Failed to store embeddings: {e}")
    
    def search_knowledge(self, agent_name: str, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索知识库"""
        
        if not self.vector_db:
            # 回退到文本搜索
            return self._text_search(agent_name, query, top_k)
        
        try:
            if 'collections' in self.vector_db:
                # ChromaDB搜索
                collection = self.vector_db['collections'].get(agent_name)
                if collection:
                    results = collection.query(
                        query_texts=[query],
                        n_results=top_k
                    )
                    
                    return [
                        {
                            'content': doc,
                            'score': 1 - distance,
                            'metadata': metadata
                        }
                        for doc, distance, metadata in zip(
                            results['documents'][0],
                            results['distances'][0],
                            results['metadatas'][0]
                        )
                    ]
            
            elif 'indices' in self.vector_db:
                # FAISS搜索
                agent_index = self.vector_db['indices'].get(agent_name)
                if agent_index and agent_index['index'] and self.embedding_model:
                    query_embedding = self.embedding_model.encode([query])
                    faiss.normalize_L2(query_embedding)
                    
                    scores, indices = agent_index['index'].search(
                        query_embedding.astype('float32'), top_k
                    )
                    
                    results = []
                    for score, idx in zip(scores[0], indices[0]):
                        if idx < len(agent_index['chunk_ids']):
                            chunk_id = agent_index['chunk_ids'][idx]
                            chunk = self.chunks.get(chunk_id)
                            if chunk:
                                results.append({
                                    'content': chunk.content,
                                    'score': float(score),
                                    'metadata': {
                                        'doc_id': chunk.doc_id,
                                        'chunk_index': chunk.chunk_index
                                    }
                                })
                    
                    return results
        
        except Exception as e:
            logger.error(f"Failed to search knowledge base: {e}")
        
        # 回退到文本搜索
        return self._text_search(agent_name, query, top_k)
    
    def _text_search(self, agent_name: str, query: str, top_k: int) -> List[Dict[str, Any]]:
        """文本搜索（回退方案）"""
        
        results = []
        query_lower = query.lower()
        
        for chunk in self.chunks.values():
            if chunk.doc_id.startswith(agent_name):
                # 简单的关键词匹配
                content_lower = chunk.content.lower()
                if query_lower in content_lower:
                    score = content_lower.count(query_lower) / len(content_lower.split())
                    results.append({
                        'content': chunk.content,
                        'score': score,
                        'metadata': {
                            'doc_id': chunk.doc_id,
                            'chunk_index': chunk.chunk_index
                        }
                    })
        
        # 按分数排序
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:top_k]
    
    def get_knowledge_stats(self, agent_name: str = None) -> Dict[str, Any]:
        """获取知识库统计信息"""
        
        stats = {
            'total_documents': len(self.documents),
            'total_chunks': len(self.chunks),
            'total_embeddings': sum(doc.embedding_count for doc in self.documents.values()),
            'agents': {}
        }
        
        # 按智能体分组统计
        for doc in self.documents.values():
            agent = doc.agent_name
            if agent not in stats['agents']:
                stats['agents'][agent] = {
                    'documents': 0,
                    'chunks': 0,
                    'embeddings': 0,
                    'file_types': {},
                    'tags': set()
                }
            
            stats['agents'][agent]['documents'] += 1
            stats['agents'][agent]['chunks'] += doc.chunk_count
            stats['agents'][agent]['embeddings'] += doc.embedding_count
            
            # 文件类型统计
            doc_type = doc.document_type
            stats['agents'][agent]['file_types'][doc_type] = \
                stats['agents'][agent]['file_types'].get(doc_type, 0) + 1
            
            # 标签统计
            stats['agents'][agent]['tags'].update(doc.tags)
        
        # 转换set为list以便JSON序列化
        for agent_stats in stats['agents'].values():
            agent_stats['tags'] = list(agent_stats['tags'])
        
        if agent_name:
            return stats['agents'].get(agent_name, {})
        
        return stats
    
    def remove_document(self, doc_id: str) -> bool:
        """删除文档"""
        
        if doc_id not in self.documents:
            return False
        
        try:
            # 删除相关的块
            chunks_to_remove = [
                chunk_id for chunk_id, chunk in self.chunks.items()
                if chunk.doc_id == doc_id
            ]
            
            for chunk_id in chunks_to_remove:
                del self.chunks[chunk_id]
            
            # 删除文档
            del self.documents[doc_id]
            
            # 保存元数据
            self._save_metadata()
            
            logger.info(f"Removed document {doc_id} and {len(chunks_to_remove)} chunks")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove document {doc_id}: {e}")
            return False
    
    def clear_agent_knowledge(self, agent_name: str) -> bool:
        """清空智能体的知识库"""
        
        try:
            # 删除相关文档
            docs_to_remove = [
                doc_id for doc_id, doc in self.documents.items()
                if doc.agent_name == agent_name
            ]
            
            for doc_id in docs_to_remove:
                self.remove_document(doc_id)
            
            logger.info(f"Cleared knowledge base for {agent_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear knowledge base for {agent_name}: {e}")
            return False 