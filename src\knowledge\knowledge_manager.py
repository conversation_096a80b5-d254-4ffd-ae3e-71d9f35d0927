"""
Knowledge Manager

Manages knowledge bases for multiple agents, handles knowledge updates,
and provides centralized knowledge management capabilities.
"""

import os
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
import shutil

from .rag_system import RAGSystem


class KnowledgeManager:
    """
    Centralized knowledge management for strategic simulation agents.
    
    Features:
    - Multi-agent knowledge base management
    - Knowledge base updates and versioning
    - Cross-agent knowledge sharing
    - Knowledge base analytics
    - Dynamic knowledge injection
    """
    
    def __init__(self, knowledge_base_dir: str = "config/knowledge_base", 
                 config: Dict[str, Any] = None):
        """Initialize knowledge manager."""
        
        self.knowledge_base_dir = Path(knowledge_base_dir)
        self.config = config or {}
        
        # Ensure knowledge base directory exists
        self.knowledge_base_dir.mkdir(parents=True, exist_ok=True)
        
        # Agent knowledge systems
        self.agent_knowledge_systems = {}
        self.knowledge_base_metadata = {}
        
        # Shared knowledge
        self.shared_knowledge = {}
        
        # Initialize existing knowledge bases
        self._discover_knowledge_bases()
    
    def _discover_knowledge_bases(self):
        """Discover existing knowledge base files."""
        
        for kb_file in self.knowledge_base_dir.glob("*_knowledge.txt"):
            agent_name = kb_file.stem.replace('_knowledge', '')
            self._load_agent_knowledge_base(agent_name, str(kb_file))
    
    def _load_agent_knowledge_base(self, agent_name: str, kb_path: str):
        """Load knowledge base for a specific agent."""
        
        try:
            rag_system = RAGSystem(kb_path, self.config.get('rag_config', {}))
            self.agent_knowledge_systems[agent_name] = rag_system
            
            # Store metadata
            self.knowledge_base_metadata[agent_name] = {
                'path': kb_path,
                'loaded_at': datetime.now().isoformat(),
                'stats': rag_system.get_knowledge_base_stats()
            }
            
            print(f"Loaded knowledge base for {agent_name}: {kb_path}")
            
        except Exception as e:
            print(f"Failed to load knowledge base for {agent_name}: {e}")
    
    def create_agent_knowledge_base(self, agent_name: str, 
                                  initial_content: str = None,
                                  source_files: List[str] = None) -> str:
        """Create a new knowledge base for an agent."""
        
        kb_path = self.knowledge_base_dir / f"{agent_name}_knowledge.txt"
        
        # Create initial content
        content = initial_content or f"# {agent_name.title()} Knowledge Base\n\nInitialized on {datetime.now().isoformat()}\n\n"
        
        # Add content from source files if provided
        if source_files:
            for source_file in source_files:
                if os.path.exists(source_file):
                    try:
                        with open(source_file, 'r', encoding='utf-8') as f:
                            source_content = f.read()
                        content += f"\n\n## From {os.path.basename(source_file)}\n\n{source_content}"
                    except Exception as e:
                        print(f"Error reading source file {source_file}: {e}")
        
        # Write knowledge base file
        with open(kb_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Load the knowledge base
        self._load_agent_knowledge_base(agent_name, str(kb_path))
        
        return str(kb_path)
    
    def update_agent_knowledge(self, agent_name: str, new_content: str, 
                             update_type: str = "append") -> bool:
        """Update an agent's knowledge base."""
        
        if agent_name not in self.knowledge_base_metadata:
            print(f"No knowledge base found for {agent_name}")
            return False
        
        kb_path = self.knowledge_base_metadata[agent_name]['path']
        
        try:
            if update_type == "append":
                # Append new content
                with open(kb_path, 'a', encoding='utf-8') as f:
                    f.write(f"\n\n## Update {datetime.now().isoformat()}\n\n{new_content}")
            
            elif update_type == "replace":
                # Replace entire content
                with open(kb_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
            
            elif update_type == "prepend":
                # Prepend new content
                with open(kb_path, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
                
                with open(kb_path, 'w', encoding='utf-8') as f:
                    f.write(f"{new_content}\n\n{existing_content}")
            
            # Reload the knowledge base
            self._load_agent_knowledge_base(agent_name, kb_path)
            
            print(f"Updated knowledge base for {agent_name}")
            return True
            
        except Exception as e:
            print(f"Error updating knowledge base for {agent_name}: {e}")
            return False
    
    def get_agent_knowledge_system(self, agent_name: str) -> Optional[RAGSystem]:
        """Get the RAG system for a specific agent."""
        
        return self.agent_knowledge_systems.get(agent_name)
    
    def query_agent_knowledge(self, agent_name: str, query: str, 
                            top_k: int = 5) -> List[Dict[str, Any]]:
        """Query an agent's knowledge base."""
        
        rag_system = self.get_agent_knowledge_system(agent_name)
        if not rag_system:
            return []
        
        return rag_system.retrieve(query, top_k=top_k)
    
    def query_all_knowledge(self, query: str, top_k: int = 5, 
                          exclude_agents: List[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """Query all agent knowledge bases."""
        
        exclude_agents = exclude_agents or []
        results = {}
        
        for agent_name, rag_system in self.agent_knowledge_systems.items():
            if agent_name not in exclude_agents:
                agent_results = rag_system.retrieve(query, top_k=top_k)
                if agent_results:
                    results[agent_name] = agent_results
        
        return results
    
    def share_knowledge_between_agents(self, source_agent: str, target_agent: str,
                                     knowledge_content: str, topic: str = None) -> bool:
        """Share knowledge from one agent to another."""
        
        if target_agent not in self.knowledge_base_metadata:
            print(f"Target agent {target_agent} has no knowledge base")
            return False
        
        # Format shared knowledge
        topic_header = f"## Shared Knowledge: {topic}" if topic else "## Shared Knowledge"
        shared_content = f"""
{topic_header}
Source: {source_agent}
Shared on: {datetime.now().isoformat()}

{knowledge_content}
"""
        
        return self.update_agent_knowledge(target_agent, shared_content, "append")
    
    def create_shared_knowledge_base(self, topic: str, content: str, 
                                   contributing_agents: List[str] = None) -> str:
        """Create a shared knowledge base accessible to all agents."""
        
        shared_kb_path = self.knowledge_base_dir / f"shared_{topic.lower().replace(' ', '_')}.txt"
        
        # Create shared knowledge content
        header = f"# Shared Knowledge: {topic}\n\n"
        header += f"Created: {datetime.now().isoformat()}\n"
        if contributing_agents:
            header += f"Contributing Agents: {', '.join(contributing_agents)}\n"
        header += "\n---\n\n"
        
        full_content = header + content
        
        # Write shared knowledge file
        with open(shared_kb_path, 'w', encoding='utf-8') as f:
            f.write(full_content)
        
        # Create RAG system for shared knowledge
        shared_rag = RAGSystem(str(shared_kb_path), self.config.get('rag_config', {}))
        self.shared_knowledge[topic] = {
            'rag_system': shared_rag,
            'path': str(shared_kb_path),
            'created_at': datetime.now().isoformat(),
            'contributing_agents': contributing_agents or []
        }
        
        return str(shared_kb_path)
    
    def query_shared_knowledge(self, topic: str, query: str, 
                             top_k: int = 5) -> List[Dict[str, Any]]:
        """Query shared knowledge base."""
        
        if topic not in self.shared_knowledge:
            return []
        
        rag_system = self.shared_knowledge[topic]['rag_system']
        return rag_system.retrieve(query, top_k=top_k)
    
    def backup_knowledge_bases(self, backup_dir: str = None) -> str:
        """Create backup of all knowledge bases."""
        
        backup_dir = backup_dir or f"backups/knowledge_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_path = Path(backup_dir)
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # Copy all knowledge base files
        for kb_file in self.knowledge_base_dir.glob("*.txt"):
            shutil.copy2(kb_file, backup_path / kb_file.name)
        
        # Create backup metadata
        backup_metadata = {
            'backup_timestamp': datetime.now().isoformat(),
            'knowledge_bases': list(self.knowledge_base_metadata.keys()),
            'shared_knowledge': list(self.shared_knowledge.keys()),
            'total_files': len(list(self.knowledge_base_dir.glob("*.txt")))
        }
        
        with open(backup_path / "backup_metadata.json", 'w') as f:
            json.dump(backup_metadata, f, indent=2)
        
        print(f"Knowledge bases backed up to: {backup_path}")
        return str(backup_path)
    
    def restore_knowledge_bases(self, backup_dir: str) -> bool:
        """Restore knowledge bases from backup."""
        
        backup_path = Path(backup_dir)
        if not backup_path.exists():
            print(f"Backup directory not found: {backup_dir}")
            return False
        
        try:
            # Clear current knowledge systems
            self.agent_knowledge_systems.clear()
            self.knowledge_base_metadata.clear()
            self.shared_knowledge.clear()
            
            # Copy backup files
            for backup_file in backup_path.glob("*.txt"):
                target_file = self.knowledge_base_dir / backup_file.name
                shutil.copy2(backup_file, target_file)
            
            # Rediscover knowledge bases
            self._discover_knowledge_bases()
            
            print(f"Knowledge bases restored from: {backup_dir}")
            return True
            
        except Exception as e:
            print(f"Error restoring knowledge bases: {e}")
            return False
    
    def get_knowledge_analytics(self) -> Dict[str, Any]:
        """Get analytics about all knowledge bases."""
        
        analytics = {
            'total_agents': len(self.agent_knowledge_systems),
            'total_shared_knowledge': len(self.shared_knowledge),
            'agent_stats': {},
            'shared_stats': {},
            'overall_stats': {
                'total_documents': 0,
                'total_chunks': 0,
                'total_words': 0
            }
        }
        
        # Agent knowledge analytics
        for agent_name, rag_system in self.agent_knowledge_systems.items():
            stats = rag_system.get_knowledge_base_stats()
            analytics['agent_stats'][agent_name] = stats
            
            # Add to overall stats
            analytics['overall_stats']['total_documents'] += stats['total_documents']
            analytics['overall_stats']['total_chunks'] += stats['total_chunks']
            analytics['overall_stats']['total_words'] += stats['total_words']
        
        # Shared knowledge analytics
        for topic, shared_data in self.shared_knowledge.items():
            stats = shared_data['rag_system'].get_knowledge_base_stats()
            analytics['shared_stats'][topic] = stats
            
            # Add to overall stats
            analytics['overall_stats']['total_documents'] += stats['total_documents']
            analytics['overall_stats']['total_chunks'] += stats['total_chunks']
            analytics['overall_stats']['total_words'] += stats['total_words']
        
        return analytics
    
    def export_knowledge_summary(self, output_path: str) -> str:
        """Export a summary of all knowledge bases."""
        
        analytics = self.get_knowledge_analytics()
        
        summary = {
            'export_timestamp': datetime.now().isoformat(),
            'knowledge_analytics': analytics,
            'agent_knowledge_metadata': self.knowledge_base_metadata,
            'shared_knowledge_metadata': {
                topic: {
                    'path': data['path'],
                    'created_at': data['created_at'],
                    'contributing_agents': data['contributing_agents']
                }
                for topic, data in self.shared_knowledge.items()
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        return output_path
    
    def search_across_all_knowledge(self, query: str, top_k_per_source: int = 3) -> Dict[str, Any]:
        """Search across all knowledge sources and aggregate results."""
        
        all_results = {
            'query': query,
            'timestamp': datetime.now().isoformat(),
            'agent_results': {},
            'shared_results': {},
            'aggregated_results': []
        }
        
        # Search agent knowledge bases
        for agent_name, rag_system in self.agent_knowledge_systems.items():
            results = rag_system.retrieve(query, top_k=top_k_per_source)
            if results:
                all_results['agent_results'][agent_name] = results
                
                # Add to aggregated results with source info
                for result in results:
                    result['source_type'] = 'agent'
                    result['source_name'] = agent_name
                    all_results['aggregated_results'].append(result)
        
        # Search shared knowledge bases
        for topic, shared_data in self.shared_knowledge.items():
            results = shared_data['rag_system'].retrieve(query, top_k=top_k_per_source)
            if results:
                all_results['shared_results'][topic] = results
                
                # Add to aggregated results with source info
                for result in results:
                    result['source_type'] = 'shared'
                    result['source_name'] = topic
                    all_results['aggregated_results'].append(result)
        
        # Sort aggregated results by score
        all_results['aggregated_results'].sort(key=lambda x: x.get('score', 0), reverse=True)
        
        return all_results
    
    def get_agent_knowledge_overlap(self, agent1: str, agent2: str, 
                                  sample_queries: List[str] = None) -> Dict[str, Any]:
        """Analyze knowledge overlap between two agents."""
        
        if agent1 not in self.agent_knowledge_systems or agent2 not in self.agent_knowledge_systems:
            return {'error': 'One or both agents not found'}
        
        # Use default queries if none provided
        if not sample_queries:
            sample_queries = [
                "strategic planning",
                "risk assessment", 
                "decision making",
                "policy analysis",
                "international relations"
            ]
        
        overlap_analysis = {
            'agent1': agent1,
            'agent2': agent2,
            'query_comparisons': [],
            'overlap_score': 0.0
        }
        
        total_overlap = 0
        
        for query in sample_queries:
            results1 = self.query_agent_knowledge(agent1, query, top_k=5)
            results2 = self.query_agent_knowledge(agent2, query, top_k=5)
            
            # Simple overlap calculation based on content similarity
            overlap_count = 0
            for r1 in results1:
                for r2 in results2:
                    # Simple word overlap check
                    words1 = set(r1['content'].lower().split())
                    words2 = set(r2['content'].lower().split())
                    if len(words1.intersection(words2)) > len(words1.union(words2)) * 0.3:
                        overlap_count += 1
                        break
            
            query_overlap = overlap_count / max(len(results1), len(results2)) if results1 or results2 else 0
            total_overlap += query_overlap
            
            overlap_analysis['query_comparisons'].append({
                'query': query,
                'agent1_results': len(results1),
                'agent2_results': len(results2),
                'overlap_ratio': query_overlap
            })
        
        overlap_analysis['overlap_score'] = total_overlap / len(sample_queries) if sample_queries else 0
        
        return overlap_analysis
