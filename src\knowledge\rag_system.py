"""
RAG System

Retrieval-Augmented Generation system for strategic simulation agents.
Provides document indexing, semantic search, and knowledge retrieval capabilities.
"""

import os
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import re
from datetime import datetime

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    print("ChromaDB not available. Using simple text search.")

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("SentenceTransformers not available. Using basic embeddings.")

try:
    import faiss
    import numpy as np
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    print("FAISS not available. Using alternative vector search.")


class RAGSystem:
    """
    Retrieval-Augmented Generation system for knowledge retrieval.
    
    Features:
    - Document indexing and chunking
    - Semantic search with embeddings
    - Multiple vector database backends
    - Relevance scoring and reranking
    - Knowledge base management
    """
    
    def __init__(self, knowledge_base_path: str, config: Dict[str, Any] = None):
        """Initialize RAG system."""
        
        self.knowledge_base_path = knowledge_base_path
        self.config = config or {}
        
        # Configuration
        self.chunk_size = self.config.get('chunk_size', 512)
        self.chunk_overlap = self.config.get('chunk_overlap', 50)
        self.top_k = self.config.get('top_k_retrieval', 5)
        self.similarity_threshold = self.config.get('similarity_threshold', 0.7)
        self.enable_reranking = self.config.get('enable_reranking', True)
        
        # Initialize components
        self.embedding_model = self._initialize_embedding_model()
        self.vector_db = self._initialize_vector_db()
        
        # Document storage
        self.documents = []
        self.chunks = []
        self.chunk_metadata = []
        
        # Load knowledge base
        if os.path.exists(knowledge_base_path):
            self._load_knowledge_base()
    
    def _initialize_embedding_model(self):
        """Initialize embedding model."""
        
        model_name = self.config.get('embedding_model', 'sentence-transformers/all-MiniLM-L6-v2')
        
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                return SentenceTransformer(model_name)
            except Exception as e:
                print(f"Failed to load SentenceTransformer model: {e}")
        
        # Fallback to simple embeddings
        return None
    
    def _initialize_vector_db(self):
        """Initialize vector database."""
        
        db_type = self.config.get('vector_db_type', 'chromadb')
        
        if db_type == 'chromadb' and CHROMADB_AVAILABLE:
            return self._initialize_chromadb()
        elif db_type == 'faiss' and FAISS_AVAILABLE:
            return self._initialize_faiss()
        else:
            return None  # Use simple text search
    
    def _initialize_chromadb(self):
        """Initialize ChromaDB."""
        
        try:
            # Create persistent client
            db_path = self.config.get('chromadb_path', 'data/chromadb')
            Path(db_path).mkdir(parents=True, exist_ok=True)
            
            client = chromadb.PersistentClient(path=db_path)
            
            # Create or get collection
            collection_name = self._get_collection_name()
            collection = client.get_or_create_collection(
                name=collection_name,
                metadata={"description": f"Knowledge base for {self.knowledge_base_path}"}
            )
            
            return {'client': client, 'collection': collection}
            
        except Exception as e:
            print(f"Failed to initialize ChromaDB: {e}")
            return None
    
    def _initialize_faiss(self):
        """Initialize FAISS index."""
        
        try:
            # Will be initialized when first documents are added
            return {'index': None, 'dimension': None}
        except Exception as e:
            print(f"Failed to initialize FAISS: {e}")
            return None
    
    def _get_collection_name(self) -> str:
        """Generate collection name from knowledge base path."""
        
        # Create a safe collection name
        base_name = Path(self.knowledge_base_path).stem
        safe_name = re.sub(r'[^a-zA-Z0-9_-]', '_', base_name)
        return f"kb_{safe_name}"
    
    def _load_knowledge_base(self):
        """Load and process knowledge base documents."""
        
        print(f"Loading knowledge base from: {self.knowledge_base_path}")
        
        if os.path.isfile(self.knowledge_base_path):
            # Single file
            self._process_file(self.knowledge_base_path)
        elif os.path.isdir(self.knowledge_base_path):
            # Directory of files
            for file_path in Path(self.knowledge_base_path).rglob('*'):
                if file_path.is_file() and self._is_supported_file(file_path):
                    self._process_file(str(file_path))
        
        # Index documents
        if self.chunks:
            self._index_documents()
            print(f"Indexed {len(self.chunks)} chunks from {len(self.documents)} documents")
    
    def _is_supported_file(self, file_path: Path) -> bool:
        """Check if file type is supported."""
        
        supported_extensions = {'.txt', '.md', '.json', '.csv'}
        return file_path.suffix.lower() in supported_extensions
    
    def _process_file(self, file_path: str):
        """Process a single file."""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Store document
            doc_id = len(self.documents)
            document = {
                'id': doc_id,
                'path': file_path,
                'content': content,
                'metadata': {
                    'filename': os.path.basename(file_path),
                    'size': len(content),
                    'processed_at': datetime.now().isoformat()
                }
            }
            self.documents.append(document)
            
            # Create chunks
            chunks = self._chunk_document(content, doc_id)
            self.chunks.extend(chunks)
            
        except Exception as e:
            print(f"Error processing file {file_path}: {e}")
    
    def _chunk_document(self, content: str, doc_id: int) -> List[Dict[str, Any]]:
        """Split document into chunks."""
        
        # Simple sentence-based chunking
        sentences = self._split_into_sentences(content)
        chunks = []
        
        current_chunk = ""
        current_sentences = []
        
        for sentence in sentences:
            # Check if adding this sentence would exceed chunk size
            if len(current_chunk) + len(sentence) > self.chunk_size and current_chunk:
                # Create chunk
                chunk_id = len(self.chunks) + len(chunks)
                chunk = {
                    'id': chunk_id,
                    'content': current_chunk.strip(),
                    'doc_id': doc_id,
                    'sentences': current_sentences,
                    'metadata': {
                        'chunk_index': len(chunks),
                        'word_count': len(current_chunk.split()),
                        'sentence_count': len(current_sentences)
                    }
                }
                chunks.append(chunk)
                
                # Start new chunk with overlap
                if self.chunk_overlap > 0 and current_sentences:
                    overlap_sentences = current_sentences[-self.chunk_overlap:]
                    current_chunk = " ".join(overlap_sentences)
                    current_sentences = overlap_sentences.copy()
                else:
                    current_chunk = ""
                    current_sentences = []
            
            current_chunk += " " + sentence if current_chunk else sentence
            current_sentences.append(sentence)
        
        # Add final chunk if not empty
        if current_chunk.strip():
            chunk_id = len(self.chunks) + len(chunks)
            chunk = {
                'id': chunk_id,
                'content': current_chunk.strip(),
                'doc_id': doc_id,
                'sentences': current_sentences,
                'metadata': {
                    'chunk_index': len(chunks),
                    'word_count': len(current_chunk.split()),
                    'sentence_count': len(current_sentences)
                }
            }
            chunks.append(chunk)
        
        return chunks
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        return sentences
    
    def _index_documents(self):
        """Index documents in vector database."""
        
        if not self.chunks:
            return
        
        if self.vector_db and 'collection' in self.vector_db:
            self._index_with_chromadb()
        elif self.vector_db and 'index' in self.vector_db:
            self._index_with_faiss()
        else:
            print("No vector database available. Using text search only.")
    
    def _index_with_chromadb(self):
        """Index documents with ChromaDB."""
        
        try:
            collection = self.vector_db['collection']
            
            # Prepare data for indexing
            documents = [chunk['content'] for chunk in self.chunks]
            metadatas = [chunk['metadata'] for chunk in self.chunks]
            ids = [str(chunk['id']) for chunk in self.chunks]
            
            # Add to collection
            collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            print(f"Indexed {len(documents)} chunks in ChromaDB")
            
        except Exception as e:
            print(f"Error indexing with ChromaDB: {e}")
    
    def _index_with_faiss(self):
        """Index documents with FAISS."""
        
        if not self.embedding_model:
            print("No embedding model available for FAISS indexing")
            return
        
        try:
            # Generate embeddings
            texts = [chunk['content'] for chunk in self.chunks]
            embeddings = self.embedding_model.encode(texts)
            
            # Initialize FAISS index
            dimension = embeddings.shape[1]
            index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
            
            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(embeddings)
            
            # Add to index
            index.add(embeddings.astype('float32'))
            
            self.vector_db['index'] = index
            self.vector_db['dimension'] = dimension
            
            print(f"Indexed {len(embeddings)} chunks in FAISS")
            
        except Exception as e:
            print(f"Error indexing with FAISS: {e}")
    
    def retrieve(self, query: str, top_k: int = None, 
                similarity_threshold: float = None) -> List[Dict[str, Any]]:
        """Retrieve relevant documents for a query."""
        
        top_k = top_k or self.top_k
        similarity_threshold = similarity_threshold or self.similarity_threshold
        
        if self.vector_db and 'collection' in self.vector_db:
            results = self._retrieve_with_chromadb(query, top_k)
        elif self.vector_db and 'index' in self.vector_db:
            results = self._retrieve_with_faiss(query, top_k)
        else:
            results = self._retrieve_with_text_search(query, top_k)
        
        # Filter by similarity threshold
        filtered_results = [r for r in results if r.get('score', 0) >= similarity_threshold]
        
        # Rerank if enabled
        if self.enable_reranking and len(filtered_results) > 1:
            filtered_results = self._rerank_results(query, filtered_results)
        
        return filtered_results[:top_k]
    
    def _retrieve_with_chromadb(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Retrieve using ChromaDB."""
        
        try:
            collection = self.vector_db['collection']
            
            results = collection.query(
                query_texts=[query],
                n_results=top_k
            )
            
            retrieved_docs = []
            for i, doc_id in enumerate(results['ids'][0]):
                chunk_id = int(doc_id)
                chunk = self.chunks[chunk_id]
                
                retrieved_docs.append({
                    'content': chunk['content'],
                    'score': 1 - results['distances'][0][i],  # Convert distance to similarity
                    'metadata': chunk['metadata'],
                    'doc_id': chunk['doc_id'],
                    'chunk_id': chunk_id
                })
            
            return retrieved_docs
            
        except Exception as e:
            print(f"Error retrieving with ChromaDB: {e}")
            return []
    
    def _retrieve_with_faiss(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Retrieve using FAISS."""
        
        if not self.embedding_model or not self.vector_db['index']:
            return []
        
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query])
            faiss.normalize_L2(query_embedding)
            
            # Search
            scores, indices = self.vector_db['index'].search(
                query_embedding.astype('float32'), top_k
            )
            
            retrieved_docs = []
            for i, idx in enumerate(indices[0]):
                if idx < len(self.chunks):
                    chunk = self.chunks[idx]
                    retrieved_docs.append({
                        'content': chunk['content'],
                        'score': float(scores[0][i]),
                        'metadata': chunk['metadata'],
                        'doc_id': chunk['doc_id'],
                        'chunk_id': idx
                    })
            
            return retrieved_docs
            
        except Exception as e:
            print(f"Error retrieving with FAISS: {e}")
            return []
    
    def _retrieve_with_text_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Retrieve using simple text search."""
        
        query_words = query.lower().split()
        scored_chunks = []
        
        for chunk in self.chunks:
            content_lower = chunk['content'].lower()
            
            # Simple scoring based on word matches
            score = 0
            for word in query_words:
                if word in content_lower:
                    score += content_lower.count(word)
            
            if score > 0:
                # Normalize score
                normalized_score = score / (len(query_words) * len(chunk['content'].split()))
                
                scored_chunks.append({
                    'content': chunk['content'],
                    'score': normalized_score,
                    'metadata': chunk['metadata'],
                    'doc_id': chunk['doc_id'],
                    'chunk_id': chunk['id']
                })
        
        # Sort by score
        scored_chunks.sort(key=lambda x: x['score'], reverse=True)
        
        return scored_chunks[:top_k]
    
    def _rerank_results(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rerank results for better relevance."""
        
        # Simple reranking based on query term frequency and position
        query_words = query.lower().split()
        
        for result in results:
            content_lower = result['content'].lower()
            
            # Boost score based on:
            # 1. Query terms appearing early in the text
            # 2. Multiple query terms appearing together
            # 3. Exact phrase matches
            
            position_boost = 0
            proximity_boost = 0
            phrase_boost = 0
            
            # Position boost
            for word in query_words:
                pos = content_lower.find(word)
                if pos != -1:
                    position_boost += 1 / (pos + 1)  # Earlier = higher boost
            
            # Proximity boost
            if len(query_words) > 1:
                for i in range(len(query_words) - 1):
                    word1_pos = content_lower.find(query_words[i])
                    word2_pos = content_lower.find(query_words[i + 1])
                    if word1_pos != -1 and word2_pos != -1:
                        distance = abs(word2_pos - word1_pos)
                        if distance < 50:  # Words close together
                            proximity_boost += 1 / distance
            
            # Phrase boost
            if query.lower() in content_lower:
                phrase_boost = 0.5
            
            # Update score
            original_score = result['score']
            boost_factor = 1 + (position_boost + proximity_boost + phrase_boost) * 0.1
            result['score'] = original_score * boost_factor
        
        # Re-sort by updated scores
        results.sort(key=lambda x: x['score'], reverse=True)
        
        return results
    
    def add_document(self, content: str, metadata: Dict[str, Any] = None) -> int:
        """Add a new document to the knowledge base."""
        
        doc_id = len(self.documents)
        document = {
            'id': doc_id,
            'path': f"dynamic_doc_{doc_id}",
            'content': content,
            'metadata': metadata or {}
        }
        
        self.documents.append(document)
        
        # Create and index chunks
        chunks = self._chunk_document(content, doc_id)
        self.chunks.extend(chunks)
        
        # Update index
        if chunks:
            self._index_new_chunks(chunks)
        
        return doc_id
    
    def _index_new_chunks(self, chunks: List[Dict[str, Any]]):
        """Index new chunks in the vector database."""
        
        if self.vector_db and 'collection' in self.vector_db:
            try:
                collection = self.vector_db['collection']
                
                documents = [chunk['content'] for chunk in chunks]
                metadatas = [chunk['metadata'] for chunk in chunks]
                ids = [str(chunk['id']) for chunk in chunks]
                
                collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids
                )
                
            except Exception as e:
                print(f"Error indexing new chunks: {e}")
    
    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base."""
        
        total_words = sum(len(doc['content'].split()) for doc in self.documents)
        total_chars = sum(len(doc['content']) for doc in self.documents)
        
        return {
            'total_documents': len(self.documents),
            'total_chunks': len(self.chunks),
            'total_words': total_words,
            'total_characters': total_chars,
            'average_chunk_size': sum(len(chunk['content']) for chunk in self.chunks) / len(self.chunks) if self.chunks else 0,
            'vector_db_type': 'chromadb' if self.vector_db and 'collection' in self.vector_db else 'faiss' if self.vector_db and 'index' in self.vector_db else 'text_search',
            'embedding_model_available': self.embedding_model is not None
        }
