"""
Conversation Store

Specialized storage and retrieval system for conversation data,
supporting structured conversation analysis and retrieval.
"""

import json
import sqlite3
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict
import re


class ConversationStore:
    """
    Specialized storage system for conversation data.
    
    Features:
    - Structured conversation storage
    - Turn-by-turn analysis
    - Topic and theme tracking
    - Conversation flow analysis
    - Export capabilities
    """
    
    def __init__(self, db_path: str = "data/conversations.db"):
        """Initialize conversation store."""
        
        self.db_path = db_path
        self._initialize_database()
        
        # Analysis caches
        self.topic_cache = {}
        self.flow_cache = {}
        
    def _initialize_database(self):
        """Initialize conversation database."""
        
        # Ensure data directory exists
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Conversations table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_id TEXT NOT NULL,
                    title TEXT,
                    description TEXT,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    participants TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    metadata TEXT
                )
            ''')
            
            # Turns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS turns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_id TEXT NOT NULL,
                    turn_number INTEGER NOT NULL,
                    speaker TEXT NOT NULL,
                    content TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    turn_type TEXT DEFAULT 'contribution',
                    response_to INTEGER,
                    metadata TEXT,
                    FOREIGN KEY (response_to) REFERENCES turns (id)
                )
            ''')
            
            # Topics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS topics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_id TEXT NOT NULL,
                    topic_name TEXT NOT NULL,
                    description TEXT,
                    start_turn INTEGER,
                    end_turn INTEGER,
                    relevance_score REAL DEFAULT 0.5,
                    metadata TEXT
                )
            ''')
            
            # Analysis table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_id TEXT NOT NULL,
                    analysis_type TEXT NOT NULL,
                    analysis_data TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    metadata TEXT
                )
            ''')
            
            # Create indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_turns_conversation ON turns(conversation_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_turns_speaker ON turns(speaker)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_topics_conversation ON topics(conversation_id)')
            
            conn.commit()
    
    def create_conversation(self, conversation_id: str, title: str, 
                          participants: List[str], description: str = None,
                          metadata: Dict[str, Any] = None) -> str:
        """Create a new conversation record."""
        
        start_time = datetime.now().isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO conversations 
                (conversation_id, title, description, start_time, participants, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (conversation_id, title, description, start_time, 
                  json.dumps(participants), json.dumps(metadata) if metadata else None))
            conn.commit()
        
        return conversation_id
    
    def add_turn(self, conversation_id: str, speaker: str, content: str,
                turn_type: str = "contribution", response_to: int = None,
                metadata: Dict[str, Any] = None) -> int:
        """Add a turn to a conversation."""
        
        timestamp = datetime.now().isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get next turn number
            cursor.execute('''
                SELECT COALESCE(MAX(turn_number), 0) + 1 
                FROM turns WHERE conversation_id = ?
            ''', (conversation_id,))
            turn_number = cursor.fetchone()[0]
            
            # Insert turn
            cursor.execute('''
                INSERT INTO turns 
                (conversation_id, turn_number, speaker, content, timestamp, turn_type, response_to, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (conversation_id, turn_number, speaker, content, timestamp, 
                  turn_type, response_to, json.dumps(metadata) if metadata else None))
            
            turn_id = cursor.lastrowid
            conn.commit()
        
        return turn_id
    
    def get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation details."""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT conversation_id, title, description, start_time, end_time, 
                       participants, status, metadata
                FROM conversations WHERE conversation_id = ?
            ''', (conversation_id,))
            
            result = cursor.fetchone()
            if not result:
                return None
            
            conv_id, title, description, start_time, end_time, participants, status, metadata = result
            
            return {
                'conversation_id': conv_id,
                'title': title,
                'description': description,
                'start_time': start_time,
                'end_time': end_time,
                'participants': json.loads(participants),
                'status': status,
                'metadata': json.loads(metadata) if metadata else {}
            }
    
    def get_turns(self, conversation_id: str, speaker: str = None,
                 turn_type: str = None, limit: int = None) -> List[Dict[str, Any]]:
        """Get turns from a conversation."""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            sql = '''
                SELECT id, turn_number, speaker, content, timestamp, turn_type, response_to, metadata
                FROM turns WHERE conversation_id = ?
            '''
            params = [conversation_id]
            
            if speaker:
                sql += ' AND speaker = ?'
                params.append(speaker)
            
            if turn_type:
                sql += ' AND turn_type = ?'
                params.append(turn_type)
            
            sql += ' ORDER BY turn_number'
            
            if limit:
                sql += ' LIMIT ?'
                params.append(limit)
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            turns = []
            for result in results:
                turn_id, turn_num, speaker, content, timestamp, t_type, response_to, metadata = result
                turns.append({
                    'id': turn_id,
                    'turn_number': turn_num,
                    'speaker': speaker,
                    'content': content,
                    'timestamp': timestamp,
                    'turn_type': t_type,
                    'response_to': response_to,
                    'metadata': json.loads(metadata) if metadata else {}
                })
            
            return turns
    
    def analyze_conversation_flow(self, conversation_id: str) -> Dict[str, Any]:
        """Analyze the flow and dynamics of a conversation."""
        
        turns = self.get_turns(conversation_id)
        if not turns:
            return {}
        
        # Basic flow analysis
        speaker_turns = defaultdict(int)
        turn_lengths = []
        response_patterns = []
        
        for turn in turns:
            speaker_turns[turn['speaker']] += 1
            turn_lengths.append(len(turn['content']))
            
            if turn['response_to']:
                response_patterns.append({
                    'responder': turn['speaker'],
                    'original_turn': turn['response_to'],
                    'response_delay': turn['turn_number'] - turn['response_to']
                })
        
        # Calculate statistics
        avg_turn_length = sum(turn_lengths) / len(turn_lengths) if turn_lengths else 0
        participation_balance = self._calculate_participation_balance(speaker_turns)
        
        flow_analysis = {
            'total_turns': len(turns),
            'unique_speakers': len(speaker_turns),
            'speaker_distribution': dict(speaker_turns),
            'average_turn_length': round(avg_turn_length, 2),
            'participation_balance': participation_balance,
            'response_patterns': response_patterns,
            'conversation_momentum': self._analyze_momentum(turns),
            'engagement_trends': self._analyze_engagement_trends(turns)
        }
        
        # Store analysis
        self._store_analysis(conversation_id, 'flow_analysis', flow_analysis)
        
        return flow_analysis
    
    def _calculate_participation_balance(self, speaker_turns: Dict[str, int]) -> float:
        """Calculate how balanced participation is (0-1 scale)."""
        
        if len(speaker_turns) <= 1:
            return 1.0
        
        total_turns = sum(speaker_turns.values())
        expected_turns_per_speaker = total_turns / len(speaker_turns)
        
        # Calculate variance from expected
        variance = sum((turns - expected_turns_per_speaker) ** 2 
                      for turns in speaker_turns.values()) / len(speaker_turns)
        
        # Normalize to 0-1 scale (lower variance = higher balance)
        max_possible_variance = (total_turns ** 2) / len(speaker_turns)
        balance_score = 1 - (variance / max_possible_variance)
        
        return max(0, min(1, balance_score))
    
    def _analyze_momentum(self, turns: List[Dict[str, Any]]) -> str:
        """Analyze conversation momentum."""
        
        if len(turns) < 4:
            return "insufficient_data"
        
        # Analyze turn length trends
        early_turns = turns[:len(turns)//3]
        late_turns = turns[-len(turns)//3:]
        
        early_avg_length = sum(len(turn['content']) for turn in early_turns) / len(early_turns)
        late_avg_length = sum(len(turn['content']) for turn in late_turns) / len(late_turns)
        
        if late_avg_length > early_avg_length * 1.2:
            return "building"
        elif late_avg_length < early_avg_length * 0.8:
            return "declining"
        else:
            return "steady"
    
    def _analyze_engagement_trends(self, turns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze engagement trends over time."""
        
        # Divide conversation into segments
        segment_size = max(1, len(turns) // 5)
        segments = []
        
        for i in range(0, len(turns), segment_size):
            segment = turns[i:i + segment_size]
            avg_length = sum(len(turn['content']) for turn in segment) / len(segment)
            unique_speakers = len(set(turn['speaker'] for turn in segment))
            
            segments.append({
                'segment': i // segment_size + 1,
                'avg_turn_length': avg_length,
                'unique_speakers': unique_speakers,
                'turn_count': len(segment)
            })
        
        return {
            'segments': segments,
            'trend': self._determine_engagement_trend(segments)
        }
    
    def _determine_engagement_trend(self, segments: List[Dict[str, Any]]) -> str:
        """Determine overall engagement trend."""
        
        if len(segments) < 2:
            return "stable"
        
        first_half_avg = sum(s['avg_turn_length'] for s in segments[:len(segments)//2]) / (len(segments)//2)
        second_half_avg = sum(s['avg_turn_length'] for s in segments[len(segments)//2:]) / (len(segments) - len(segments)//2)
        
        if second_half_avg > first_half_avg * 1.15:
            return "increasing"
        elif second_half_avg < first_half_avg * 0.85:
            return "decreasing"
        else:
            return "stable"
    
    def extract_topics(self, conversation_id: str) -> List[Dict[str, Any]]:
        """Extract topics from conversation content."""
        
        turns = self.get_turns(conversation_id)
        if not turns:
            return []
        
        # Simple topic extraction using keywords
        topic_keywords = {
            'military': ['military', 'force', 'defense', 'army', 'navy', 'troops', 'combat'],
            'economic': ['economic', 'trade', 'market', 'financial', 'cost', 'budget'],
            'diplomatic': ['diplomatic', 'negotiation', 'alliance', 'treaty', 'international'],
            'domestic': ['domestic', 'public', 'congress', 'political', 'election'],
            'security': ['security', 'threat', 'risk', 'intelligence', 'surveillance'],
            'strategic': ['strategic', 'strategy', 'planning', 'objective', 'goal']
        }
        
        topics = []
        
        for topic_name, keywords in topic_keywords.items():
            relevance_score = 0
            start_turn = None
            end_turn = None
            
            for turn in turns:
                content_lower = turn['content'].lower()
                matches = sum(1 for keyword in keywords if keyword in content_lower)
                
                if matches > 0:
                    if start_turn is None:
                        start_turn = turn['turn_number']
                    end_turn = turn['turn_number']
                    relevance_score += matches
            
            if relevance_score > 0:
                # Normalize relevance score
                relevance_score = min(1.0, relevance_score / (len(turns) * len(keywords)))
                
                topics.append({
                    'topic_name': topic_name,
                    'description': f"Discussion about {topic_name} matters",
                    'start_turn': start_turn,
                    'end_turn': end_turn,
                    'relevance_score': round(relevance_score, 3),
                    'keyword_matches': len([k for k in keywords if any(k in turn['content'].lower() for turn in turns)])
                })
        
        # Sort by relevance
        topics.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        # Store topics
        for topic in topics:
            self._store_topic(conversation_id, topic)
        
        return topics
    
    def _store_topic(self, conversation_id: str, topic: Dict[str, Any]):
        """Store a topic in the database."""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO topics 
                (conversation_id, topic_name, description, start_turn, end_turn, relevance_score, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (conversation_id, topic['topic_name'], topic['description'],
                  topic['start_turn'], topic['end_turn'], topic['relevance_score'],
                  json.dumps(topic)))
            conn.commit()
    
    def _store_analysis(self, conversation_id: str, analysis_type: str, analysis_data: Dict[str, Any]):
        """Store analysis results."""
        
        timestamp = datetime.now().isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO conversation_analysis 
                (conversation_id, analysis_type, analysis_data, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (conversation_id, analysis_type, json.dumps(analysis_data), timestamp))
            conn.commit()
    
    def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """Get a comprehensive summary of a conversation."""
        
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return {}
        
        turns = self.get_turns(conversation_id)
        topics = self.extract_topics(conversation_id)
        flow_analysis = self.analyze_conversation_flow(conversation_id)
        
        # Calculate duration
        if conversation['end_time']:
            start = datetime.fromisoformat(conversation['start_time'])
            end = datetime.fromisoformat(conversation['end_time'])
            duration = str(end - start)
        else:
            duration = "ongoing"
        
        return {
            'conversation_details': conversation,
            'statistics': {
                'total_turns': len(turns),
                'duration': duration,
                'participants': len(conversation['participants']),
                'topics_discussed': len(topics)
            },
            'topics': topics,
            'flow_analysis': flow_analysis,
            'key_contributors': self._identify_key_contributors(turns),
            'conversation_highlights': self._extract_highlights(turns)
        }
    
    def _identify_key_contributors(self, turns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify key contributors to the conversation."""
        
        contributor_stats = defaultdict(lambda: {'turns': 0, 'total_words': 0, 'avg_length': 0})
        
        for turn in turns:
            speaker = turn['speaker']
            word_count = len(turn['content'].split())
            
            contributor_stats[speaker]['turns'] += 1
            contributor_stats[speaker]['total_words'] += word_count
        
        # Calculate averages and sort
        contributors = []
        for speaker, stats in contributor_stats.items():
            stats['avg_length'] = stats['total_words'] / stats['turns'] if stats['turns'] > 0 else 0
            contributors.append({
                'speaker': speaker,
                **stats
            })
        
        contributors.sort(key=lambda x: x['total_words'], reverse=True)
        return contributors[:5]  # Top 5 contributors
    
    def _extract_highlights(self, turns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract conversation highlights."""
        
        highlights = []
        
        # Find longest contributions (likely detailed analysis)
        long_turns = sorted(turns, key=lambda x: len(x['content']), reverse=True)[:3]
        
        for turn in long_turns:
            highlights.append({
                'type': 'detailed_analysis',
                'speaker': turn['speaker'],
                'turn_number': turn['turn_number'],
                'preview': turn['content'][:200] + "..." if len(turn['content']) > 200 else turn['content'],
                'word_count': len(turn['content'].split())
            })
        
        return highlights
    
    def export_conversation(self, conversation_id: str, output_path: str, 
                          format: str = "json") -> str:
        """Export conversation data to file."""
        
        summary = self.get_conversation_summary(conversation_id)
        turns = self.get_turns(conversation_id)
        
        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'conversation_summary': summary,
            'full_transcript': turns
        }
        
        if format.lower() == "json":
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        elif format.lower() == "markdown":
            self._export_to_markdown(export_data, output_path)
        
        return output_path
    
    def _export_to_markdown(self, data: Dict[str, Any], output_path: str):
        """Export conversation to markdown format."""
        
        with open(output_path, 'w', encoding='utf-8') as f:
            summary = data['conversation_summary']
            
            f.write(f"# {summary['conversation_details']['title']}\n\n")
            f.write(f"**Description:** {summary['conversation_details']['description']}\n\n")
            f.write(f"**Participants:** {', '.join(summary['conversation_details']['participants'])}\n\n")
            f.write(f"**Duration:** {summary['statistics']['duration']}\n\n")
            
            f.write("## Statistics\n\n")
            stats = summary['statistics']
            for key, value in stats.items():
                f.write(f"- **{key.replace('_', ' ').title()}:** {value}\n")
            
            f.write("\n## Topics Discussed\n\n")
            for topic in summary['topics']:
                f.write(f"- **{topic['topic_name'].title()}** (Relevance: {topic['relevance_score']:.2f})\n")
            
            f.write("\n## Full Transcript\n\n")
            for turn in data['full_transcript']:
                f.write(f"### Turn {turn['turn_number']} - {turn['speaker']}\n")
                f.write(f"*{turn['timestamp']}*\n\n")
                f.write(f"{turn['content']}\n\n")
                f.write("---\n\n")
    
    def close_conversation(self, conversation_id: str):
        """Mark a conversation as completed."""
        
        end_time = datetime.now().isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE conversations 
                SET end_time = ?, status = 'completed'
                WHERE conversation_id = ?
            ''', (end_time, conversation_id))
            conn.commit()
