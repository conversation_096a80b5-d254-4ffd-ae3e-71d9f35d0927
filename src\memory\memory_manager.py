"""
Memory Manager

Manages long-term memory, conversation history, and context for strategic simulation agents.
Integrates with external memory systems like mem0 for persistent memory capabilities.
"""

import json
import sqlite3
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
from collections import defaultdict

try:
    # Try to import mem0 for advanced memory capabilities
    from mem0 import Memory
    MEM0_AVAILABLE = True
except ImportError:
    MEM0_AVAILABLE = False
    print("mem0 not available. Using local memory storage only.")


class MemoryManager:
    """
    Manages memory for strategic simulation agents.
    
    Features:
    - Long-term memory storage and retrieval
    - Conversation history management
    - Context-aware memory consolidation
    - Cross-session memory persistence
    - Integration with external memory systems (mem0)
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize memory manager."""
        
        self.config = config or {}
        
        # Memory configuration
        self.conversation_memory_limit = self.config.get('conversation_memory_limit', 50)
        self.semantic_memory_enabled = self.config.get('semantic_memory_enabled', True)
        self.episodic_memory_enabled = self.config.get('episodic_memory_enabled', True)
        self.memory_consolidation_interval = self.config.get('memory_consolidation_interval', 10)
        self.cross_session_memory = self.config.get('cross_session_memory', True)
        
        # Initialize storage
        self.db_path = self.config.get('db_path', 'data/memory.db')
        self._initialize_database()
        
        # Initialize external memory system
        self.external_memory = None
        if MEM0_AVAILABLE and self.config.get('use_mem0', True):
            self._initialize_mem0()
        
        # In-memory caches
        self.conversation_cache = defaultdict(list)
        self.context_cache = defaultdict(dict)
        self.memory_stats = defaultdict(int)
    
    def _initialize_database(self):
        """Initialize SQLite database for local memory storage."""
        
        # Ensure data directory exists
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create database and tables
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Conversations table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_name TEXT NOT NULL,
                    topic TEXT NOT NULL,
                    content TEXT NOT NULL,
                    round_number INTEGER,
                    timestamp TEXT NOT NULL,
                    session_id TEXT,
                    metadata TEXT
                )
            ''')
            
            # Long-term memories table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_name TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    importance_score REAL DEFAULT 0.5,
                    timestamp TEXT NOT NULL,
                    last_accessed TEXT,
                    access_count INTEGER DEFAULT 0,
                    tags TEXT,
                    metadata TEXT
                )
            ''')
            
            # Scenarios table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS scenarios (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    scenario_title TEXT NOT NULL,
                    scenario_data TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    participants TEXT,
                    outcome TEXT,
                    metadata TEXT
                )
            ''')
            
            # Decisions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS decisions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_name TEXT NOT NULL,
                    decision_data TEXT NOT NULL,
                    scenario_id INTEGER,
                    timestamp TEXT NOT NULL,
                    metadata TEXT,
                    FOREIGN KEY (scenario_id) REFERENCES scenarios (id)
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_agent ON conversations(agent_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_timestamp ON conversations(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_memories_agent ON memories(agent_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_memories_type ON memories(memory_type)')
            
            conn.commit()
    
    def _initialize_mem0(self):
        """Initialize mem0 external memory system."""
        
        try:
            # Initialize mem0 with configuration
            mem0_config = self.config.get('mem0_config', {})
            self.external_memory = Memory(config=mem0_config)
            print("mem0 memory system initialized successfully")
        except Exception as e:
            print(f"Failed to initialize mem0: {e}")
            self.external_memory = None
    
    def store_contribution(self, agent_name: str, topic: str, content: str, 
                          round_number: int, session_id: str = None, 
                          metadata: Dict[str, Any] = None):
        """Store a conversation contribution."""
        
        timestamp = datetime.now().isoformat()
        session_id = session_id or self._generate_session_id()
        
        # Store in local database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO conversations 
                (agent_name, topic, content, round_number, timestamp, session_id, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (agent_name, topic, content, round_number, timestamp, session_id, 
                  json.dumps(metadata) if metadata else None))
            conn.commit()
        
        # Store in conversation cache
        self.conversation_cache[agent_name].append({
            'topic': topic,
            'content': content,
            'round_number': round_number,
            'timestamp': timestamp,
            'session_id': session_id,
            'metadata': metadata
        })
        
        # Limit cache size
        if len(self.conversation_cache[agent_name]) > self.conversation_memory_limit:
            self.conversation_cache[agent_name] = self.conversation_cache[agent_name][-self.conversation_memory_limit:]
        
        # Store in external memory if available
        if self.external_memory:
            try:
                self.external_memory.add(
                    content,
                    user_id=agent_name,
                    metadata={
                        'type': 'contribution',
                        'topic': topic,
                        'round_number': round_number,
                        'timestamp': timestamp,
                        **(metadata or {})
                    }
                )
            except Exception as e:
                print(f"Failed to store in external memory: {e}")
        
        self.memory_stats['contributions_stored'] += 1
    
    def store_memory(self, agent_name: str, memory_type: str, content: str,
                    importance_score: float = 0.5, tags: List[str] = None,
                    metadata: Dict[str, Any] = None):
        """Store a long-term memory."""
        
        timestamp = datetime.now().isoformat()
        tags_str = json.dumps(tags) if tags else None
        
        # Store in local database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO memories 
                (agent_name, memory_type, content, importance_score, timestamp, tags, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (agent_name, memory_type, content, importance_score, timestamp, 
                  tags_str, json.dumps(metadata) if metadata else None))
            conn.commit()
        
        # Store in external memory if available
        if self.external_memory:
            try:
                self.external_memory.add(
                    content,
                    user_id=agent_name,
                    metadata={
                        'type': memory_type,
                        'importance_score': importance_score,
                        'tags': tags or [],
                        'timestamp': timestamp,
                        **(metadata or {})
                    }
                )
            except Exception as e:
                print(f"Failed to store memory in external system: {e}")
        
        self.memory_stats['memories_stored'] += 1
    
    def get_relevant_memories(self, agent_name: str, query: str, 
                             limit: int = 10, memory_type: str = None) -> List[Dict[str, Any]]:
        """Retrieve relevant memories for a query."""
        
        memories = []
        
        # Try external memory first (more sophisticated retrieval)
        if self.external_memory:
            try:
                external_memories = self.external_memory.search(
                    query, 
                    user_id=agent_name,
                    limit=limit
                )
                
                for memory in external_memories:
                    memories.append({
                        'content': memory.get('memory', ''),
                        'score': memory.get('score', 0.0),
                        'metadata': memory.get('metadata', {}),
                        'source': 'external'
                    })
                    
            except Exception as e:
                print(f"Failed to retrieve from external memory: {e}")
        
        # Fallback to local database
        if len(memories) < limit:
            local_memories = self._search_local_memories(agent_name, query, limit - len(memories), memory_type)
            memories.extend(local_memories)
        
        # Update access statistics
        for memory in memories:
            self._update_memory_access(memory)
        
        return memories[:limit]
    
    def _search_local_memories(self, agent_name: str, query: str, 
                              limit: int, memory_type: str = None) -> List[Dict[str, Any]]:
        """Search local memories using simple text matching."""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Simple text search (could be enhanced with FTS)
            query_words = query.lower().split()
            
            sql = '''
                SELECT content, memory_type, importance_score, timestamp, tags, metadata
                FROM memories 
                WHERE agent_name = ?
            '''
            params = [agent_name]
            
            if memory_type:
                sql += ' AND memory_type = ?'
                params.append(memory_type)
            
            sql += ' ORDER BY importance_score DESC, timestamp DESC LIMIT ?'
            params.append(limit * 2)  # Get more to filter
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            # Score results based on query match
            scored_results = []
            for result in results:
                content, mem_type, importance, timestamp, tags, metadata = result
                
                # Simple scoring based on word matches
                content_lower = content.lower()
                score = sum(1 for word in query_words if word in content_lower)
                score = score / len(query_words) if query_words else 0
                
                # Boost score by importance
                final_score = score * (1 + importance)
                
                if score > 0:  # Only include if there's some match
                    scored_results.append({
                        'content': content,
                        'memory_type': mem_type,
                        'importance_score': importance,
                        'timestamp': timestamp,
                        'tags': json.loads(tags) if tags else [],
                        'metadata': json.loads(metadata) if metadata else {},
                        'score': final_score,
                        'source': 'local'
                    })
            
            # Sort by score and return top results
            scored_results.sort(key=lambda x: x['score'], reverse=True)
            return scored_results[:limit]
    
    def _update_memory_access(self, memory: Dict[str, Any]):
        """Update memory access statistics."""
        
        # This would update access count and last accessed time
        # Implementation depends on memory source
        pass
    
    def store_scenario_context(self, agent_name: str, scenario: Dict[str, Any]):
        """Store scenario context for an agent."""
        
        timestamp = datetime.now().isoformat()
        
        # Store in context cache
        self.context_cache[agent_name]['current_scenario'] = {
            'scenario': scenario,
            'timestamp': timestamp
        }
        
        # Store as memory
        self.store_memory(
            agent_name,
            'scenario_context',
            f"Participating in scenario: {scenario.get('title', 'Unknown')}. {scenario.get('description', '')}",
            importance_score=0.8,
            tags=['scenario', 'context'],
            metadata={'scenario_data': scenario}
        )
    
    def store_decision(self, agent_name: str, decision_record: Dict[str, Any]):
        """Store a decision made by an agent."""
        
        timestamp = datetime.now().isoformat()
        
        # Store in local database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO decisions (agent_name, decision_data, timestamp, metadata)
                VALUES (?, ?, ?, ?)
            ''', (agent_name, json.dumps(decision_record), timestamp, None))
            conn.commit()
        
        # Store as high-importance memory
        decision_summary = decision_record.get('decision', '')[:200] + "..."
        self.store_memory(
            agent_name,
            'decision',
            f"Made strategic decision: {decision_summary}",
            importance_score=0.9,
            tags=['decision', 'strategic'],
            metadata=decision_record
        )
        
        self.memory_stats['decisions_stored'] += 1
    
    def store_simulation_event(self, event_type: str, event_data: Any):
        """Store a simulation-level event."""
        
        timestamp = datetime.now().isoformat()
        
        # Store as memory for all participants if applicable
        if isinstance(event_data, dict) and 'participants' in event_data:
            for participant in event_data['participants']:
                self.store_memory(
                    participant,
                    'simulation_event',
                    f"Simulation event: {event_type}",
                    importance_score=0.6,
                    tags=['simulation', event_type],
                    metadata={'event_type': event_type, 'event_data': event_data}
                )
        
        self.memory_stats['events_stored'] += 1
    
    def consolidate_memories(self, agent_name: str):
        """Consolidate and organize memories for an agent."""
        
        if not self.external_memory:
            return
        
        try:
            # Use mem0's built-in consolidation if available
            # This would merge similar memories and update importance scores
            pass
        except Exception as e:
            print(f"Memory consolidation failed: {e}")
    
    def get_conversation_history(self, agent_name: str, topic: str = None, 
                                limit: int = None) -> List[Dict[str, Any]]:
        """Get conversation history for an agent."""
        
        # Check cache first
        if agent_name in self.conversation_cache:
            cached_conversations = self.conversation_cache[agent_name]
            if topic:
                cached_conversations = [c for c in cached_conversations if c['topic'] == topic]
            
            if limit:
                cached_conversations = cached_conversations[-limit:]
            
            if cached_conversations:
                return cached_conversations
        
        # Fallback to database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            sql = 'SELECT topic, content, round_number, timestamp, session_id, metadata FROM conversations WHERE agent_name = ?'
            params = [agent_name]
            
            if topic:
                sql += ' AND topic = ?'
                params.append(topic)
            
            sql += ' ORDER BY timestamp DESC'
            
            if limit:
                sql += ' LIMIT ?'
                params.append(limit)
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            conversations = []
            for result in results:
                topic_db, content, round_num, timestamp, session_id, metadata = result
                conversations.append({
                    'topic': topic_db,
                    'content': content,
                    'round_number': round_num,
                    'timestamp': timestamp,
                    'session_id': session_id,
                    'metadata': json.loads(metadata) if metadata else {}
                })
            
            return list(reversed(conversations))  # Return in chronological order
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory system statistics."""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Count memories by type
            cursor.execute('SELECT memory_type, COUNT(*) FROM memories GROUP BY memory_type')
            memory_counts = dict(cursor.fetchall())
            
            # Count conversations
            cursor.execute('SELECT COUNT(*) FROM conversations')
            conversation_count = cursor.fetchone()[0]
            
            # Count decisions
            cursor.execute('SELECT COUNT(*) FROM decisions')
            decision_count = cursor.fetchone()[0]
        
        return {
            'memory_counts_by_type': memory_counts,
            'total_conversations': conversation_count,
            'total_decisions': decision_count,
            'runtime_stats': dict(self.memory_stats),
            'external_memory_available': self.external_memory is not None,
            'cache_sizes': {agent: len(convs) for agent, convs in self.conversation_cache.items()}
        }
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID."""
        return hashlib.md5(datetime.now().isoformat().encode()).hexdigest()[:12]
    
    def clear_cache(self, agent_name: str = None):
        """Clear memory caches."""
        
        if agent_name:
            if agent_name in self.conversation_cache:
                del self.conversation_cache[agent_name]
            if agent_name in self.context_cache:
                del self.context_cache[agent_name]
        else:
            self.conversation_cache.clear()
            self.context_cache.clear()
    
    def export_memories(self, agent_name: str, output_path: str):
        """Export all memories for an agent to a file."""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get all data for the agent
            cursor.execute('SELECT * FROM conversations WHERE agent_name = ?', (agent_name,))
            conversations = cursor.fetchall()
            
            cursor.execute('SELECT * FROM memories WHERE agent_name = ?', (agent_name,))
            memories = cursor.fetchall()
            
            cursor.execute('SELECT * FROM decisions WHERE agent_name = ?', (agent_name,))
            decisions = cursor.fetchall()
        
        export_data = {
            'agent_name': agent_name,
            'export_timestamp': datetime.now().isoformat(),
            'conversations': conversations,
            'memories': memories,
            'decisions': decisions,
            'stats': self.get_memory_stats()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return output_path
