"""
数据模型定义 - 使用Pydantic确保数据安全和正确性
"""

from pydantic import BaseModel, Field, validator, root_validator
from typing import List, Dict, Any, Optional, Union, Literal
from datetime import datetime
from enum import Enum


class AgentType(str, Enum):
    """智能体类型枚举"""
    POLITICAL_LEADER = "political_leader"
    DOMAIN_EXPERT = "domain_expert"
    BUSINESS_LEADER = "business_leader"
    ACADEMIC = "academic"
    MILITARY_LEADER = "military_leader"
    SIMULATION_CONTROLLER = "simulation_controller"
    USER_CONTROLLED = "user_controlled"
    CUSTOM = "custom"


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


class SimulationPhase(str, Enum):
    """推演阶段枚举"""
    SETUP = "setup"
    PREPARATION = "preparation"
    DISCUSSION = "discussion"
    ANALYSIS = "analysis"
    DECISION = "decision"
    COMPLETED = "completed"


class TurnOrderMode(str, Enum):
    """发言顺序模式"""
    ROUND_ROBIN = "round_robin"
    MODERATOR_CONTROLLED = "moderator_controlled"
    FREE_DISCUSSION = "free_discussion"
    CUSTOM_ORDER = "custom_order"


class LLMMessage(BaseModel):
    """LLM消息模型"""
    role: MessageRole
    content: str = Field(..., min_length=1, max_length=10000)
    
    @validator('content')
    def validate_content(cls, v):
        """验证消息内容"""
        if not v or not v.strip():
            raise ValueError("消息内容不能为空")
        return v.strip()


class LLMRequest(BaseModel):
    """LLM请求模型"""
    messages: List[LLMMessage]
    model: str = Field(..., min_length=1)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=1000, ge=1, le=4000)
    stream: bool = Field(default=False)
    
    @validator('messages')
    def validate_messages(cls, v):
        """验证消息列表"""
        if not v:
            raise ValueError("消息列表不能为空")
        if len(v) > 50:
            raise ValueError("消息列表过长，最多50条")
        return v


class LLMResponse(BaseModel):
    """LLM响应模型"""
    content: str
    model: str
    usage: Optional[Dict[str, int]] = None
    finish_reason: Optional[str] = None
    
    @validator('content')
    def validate_content(cls, v):
        """验证响应内容"""
        if not v or not v.strip():
            raise ValueError("响应内容不能为空")
        return v.strip()


class AgentPersona(BaseModel):
    """智能体人格模型"""
    nationality: Optional[str] = None
    background: Optional[str] = None
    expertise: List[str] = Field(default_factory=list)
    personality: List[str] = Field(default_factory=list)
    communication_style: Optional[str] = None
    core_values: Optional[str] = None
    
    @validator('expertise', 'personality')
    def validate_lists(cls, v):
        """验证列表字段"""
        return [item.strip() for item in v if item and item.strip()]


class Agent(BaseModel):
    """智能体模型"""
    name: str = Field(..., min_length=1, max_length=100)
    role: str = Field(..., min_length=1, max_length=200)
    type: AgentType
    persona: Optional[AgentPersona] = None
    system_prompt: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    
    @validator('name', 'role')
    def validate_strings(cls, v):
        """验证字符串字段"""
        if not v or not v.strip():
            raise ValueError("字段不能为空")
        return v.strip()


class SimulationMessage(BaseModel):
    """推演消息模型"""
    speaker: str = Field(..., min_length=1)
    content: str = Field(..., min_length=1, max_length=5000)
    timestamp: str
    message_type: Literal["agent", "user", "system"] = "agent"
    
    @validator('speaker', 'content')
    def validate_fields(cls, v):
        """验证字段"""
        if not v or not v.strip():
            raise ValueError("字段不能为空")
        return v.strip()
    
    @validator('timestamp')
    def validate_timestamp(cls, v):
        """验证时间戳格式"""
        try:
            datetime.strptime(v, "%H:%M:%S")
        except ValueError:
            raise ValueError("时间戳格式错误，应为 HH:MM:SS")
        return v


class Scenario(BaseModel):
    """推演场景模型"""
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1, max_length=1000)
    context: str = Field(..., min_length=1, max_length=2000)
    considerations: List[str] = Field(default_factory=list)
    type: Optional[str] = None
    estimated_duration: Optional[int] = Field(None, ge=5, le=300)
    difficulty_level: Optional[str] = None
    suggested_agents: List[str] = Field(default_factory=list)
    
    @validator('title', 'description', 'context')
    def validate_required_fields(cls, v):
        """验证必填字段"""
        if not v or not v.strip():
            raise ValueError("字段不能为空")
        return v.strip()
    
    @validator('considerations', 'suggested_agents')
    def validate_lists(cls, v):
        """验证列表字段"""
        return [item.strip() for item in v if item and item.strip()]


class TurnOrderConfig(BaseModel):
    """发言顺序配置模型"""
    mode: TurnOrderMode
    custom_order: Optional[List[str]] = None
    settings: Dict[str, Any] = Field(default_factory=dict)
    
    @root_validator
    def validate_custom_order(cls, values):
        """验证自定义顺序"""
        mode = values.get('mode')
        custom_order = values.get('custom_order')
        
        if mode == TurnOrderMode.CUSTOM_ORDER and not custom_order:
            raise ValueError("自定义顺序模式需要提供发言顺序")
        
        return values


class SimulationConfig(BaseModel):
    """推演配置模型"""
    max_discussion_rounds: int = Field(default=10, ge=1, le=50)
    round_time_limit: int = Field(default=300, ge=30, le=1800)
    enable_user_participation: bool = Field(default=True)
    auto_save_conversations: bool = Field(default=True)
    enable_stream_output: bool = Field(default=True)
    auto_continue: bool = Field(default=False)


class SimulationState(BaseModel):
    """推演状态模型"""
    id: str
    scenario: Scenario
    config: SimulationConfig
    selected_agents: List[str]
    turn_order_config: Optional[TurnOrderConfig] = None
    current_phase: SimulationPhase = SimulationPhase.SETUP
    current_round: int = Field(default=0, ge=0)
    messages: List[SimulationMessage] = Field(default_factory=list)
    start_time: datetime = Field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    @validator('selected_agents')
    def validate_selected_agents(cls, v):
        """验证选择的智能体"""
        if not v:
            raise ValueError("必须选择至少一个智能体")
        if len(v) > 20:
            raise ValueError("选择的智能体过多，最多20个")
        return v


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: bool = True
    message: str
    error_type: str
    timestamp: datetime = Field(default_factory=datetime.now)


class SuccessResponse(BaseModel):
    """成功响应模型"""
    success: bool = True
    data: Any
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
