"""
Decision Analyzer

Analyzes strategic decisions made during simulations, providing insights
into decision quality, process effectiveness, and potential outcomes.
"""

import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import statistics
from collections import defaultdict

from ..memory.memory_manager import MemoryManager


class DecisionAnalyzer:
    """
    Analyzes strategic decisions and simulation outcomes.
    
    Provides:
    - Decision quality assessment
    - Process effectiveness analysis
    - Risk and opportunity identification
    - Comparative analysis across simulations
    - Lessons learned extraction
    """
    
    def __init__(self, memory_manager: MemoryManager):
        """Initialize decision analyzer."""
        
        self.memory_manager = memory_manager
        
        # Analysis frameworks
        self.decision_criteria = {
            'strategic_alignment': 'Alignment with stated objectives',
            'feasibility': 'Practical implementation possibility',
            'risk_assessment': 'Identification and mitigation of risks',
            'stakeholder_consideration': 'Impact on key stakeholders',
            'resource_efficiency': 'Optimal use of available resources',
            'timeline_realism': 'Realistic implementation timeline',
            'contingency_planning': 'Preparation for alternative scenarios'
        }
        
        self.process_metrics = {
            'participation_balance': 'Equal participation across experts',
            'information_quality': 'Depth and accuracy of information used',
            'deliberation_depth': 'Thoroughness of discussion',
            'consensus_building': 'Effort to build agreement',
            'time_efficiency': 'Effective use of discussion time',
            'conflict_resolution': 'Handling of disagreements'
        }
    
    def analyze_simulation(self, simulation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive analysis of a completed simulation."""
        
        analysis_results = {
            'simulation_id': simulation_data['id'],
            'analysis_timestamp': datetime.now().isoformat(),
            'decision_analysis': self._analyze_decision_quality(simulation_data),
            'process_analysis': self._analyze_process_effectiveness(simulation_data),
            'participation_analysis': self._analyze_participation_patterns(simulation_data),
            'content_analysis': self._analyze_discussion_content(simulation_data),
            'outcome_assessment': self._assess_potential_outcomes(simulation_data),
            'lessons_learned': self._extract_lessons_learned(simulation_data),
            'recommendations': self._generate_recommendations(simulation_data)
        }
        
        # Calculate overall scores
        analysis_results['overall_scores'] = self._calculate_overall_scores(analysis_results)
        
        # Store analysis in memory
        self.memory_manager.store_decision_analysis(simulation_data['id'], analysis_results)
        
        return analysis_results
    
    def _analyze_decision_quality(self, simulation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the quality of the final decision."""
        
        final_decision = simulation_data.get('final_decision', {})
        if not final_decision:
            return {'error': 'No final decision found'}
        
        decision_text = final_decision.get('decision', '')
        
        # Analyze decision against criteria
        quality_scores = {}
        
        for criterion, description in self.decision_criteria.items():
            score = self._score_decision_criterion(decision_text, criterion)
            quality_scores[criterion] = {
                'score': score,
                'description': description,
                'rationale': self._get_scoring_rationale(decision_text, criterion, score)
            }
        
        # Calculate average quality score
        avg_score = statistics.mean([scores['score'] for scores in quality_scores.values()])
        
        return {
            'overall_quality_score': round(avg_score, 2),
            'criterion_scores': quality_scores,
            'decision_text_length': len(decision_text),
            'decision_complexity': self._assess_decision_complexity(decision_text),
            'implementation_clarity': self._assess_implementation_clarity(decision_text)
        }
    
    def _score_decision_criterion(self, decision_text: str, criterion: str) -> float:
        """Score a decision against a specific criterion (0-10 scale)."""
        
        # Simple keyword-based scoring - could be enhanced with NLP
        criterion_keywords = {
            'strategic_alignment': ['objective', 'goal', 'strategy', 'mission', 'purpose'],
            'feasibility': ['realistic', 'achievable', 'practical', 'possible', 'implementation'],
            'risk_assessment': ['risk', 'threat', 'danger', 'mitigation', 'contingency'],
            'stakeholder_consideration': ['stakeholder', 'impact', 'affected', 'consequences'],
            'resource_efficiency': ['cost', 'resource', 'budget', 'efficient', 'allocation'],
            'timeline_realism': ['timeline', 'schedule', 'time', 'deadline', 'phase'],
            'contingency_planning': ['contingency', 'alternative', 'backup', 'plan b', 'if']
        }
        
        keywords = criterion_keywords.get(criterion, [])
        decision_lower = decision_text.lower()
        
        # Count keyword matches
        matches = sum(1 for keyword in keywords if keyword in decision_lower)
        
        # Base score on matches and text length
        base_score = min(matches * 2, 8)  # Max 8 from keywords
        
        # Add bonus for detailed explanations
        if len(decision_text) > 500:
            base_score += 1
        if len(decision_text) > 1000:
            base_score += 1
        
        return min(base_score, 10)
    
    def _get_scoring_rationale(self, decision_text: str, criterion: str, score: float) -> str:
        """Provide rationale for criterion scoring."""
        
        if score >= 8:
            return f"Strong evidence of {criterion} consideration in the decision"
        elif score >= 6:
            return f"Moderate attention to {criterion} in the decision"
        elif score >= 4:
            return f"Some consideration of {criterion}, but could be stronger"
        else:
            return f"Limited evidence of {criterion} consideration"
    
    def _assess_decision_complexity(self, decision_text: str) -> str:
        """Assess the complexity of the decision."""
        
        # Simple heuristics
        word_count = len(decision_text.split())
        sentence_count = len([s for s in decision_text.split('.') if s.strip()])
        
        if word_count > 800 and sentence_count > 15:
            return "High complexity - multi-faceted decision with detailed considerations"
        elif word_count > 400 and sentence_count > 8:
            return "Medium complexity - several factors considered"
        else:
            return "Low complexity - straightforward decision"
    
    def _assess_implementation_clarity(self, decision_text: str) -> str:
        """Assess how clear the implementation path is."""
        
        implementation_keywords = ['step', 'phase', 'first', 'then', 'next', 'implement', 'execute', 'action']
        decision_lower = decision_text.lower()
        
        implementation_mentions = sum(1 for keyword in implementation_keywords if keyword in decision_lower)
        
        if implementation_mentions >= 5:
            return "High clarity - clear implementation steps outlined"
        elif implementation_mentions >= 3:
            return "Medium clarity - some implementation guidance provided"
        else:
            return "Low clarity - limited implementation details"
    
    def _analyze_process_effectiveness(self, simulation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the effectiveness of the decision-making process."""
        
        phases = simulation_data.get('phases_completed', [])
        discussion_data = simulation_data.get('discussion_data', {})
        
        process_scores = {}
        
        for metric, description in self.process_metrics.items():
            score = self._score_process_metric(simulation_data, metric)
            process_scores[metric] = {
                'score': score,
                'description': description
            }
        
        # Calculate timing efficiency
        timing_analysis = self._analyze_timing_efficiency(phases)
        
        # Analyze discussion flow
        flow_analysis = self._analyze_discussion_flow(discussion_data)
        
        return {
            'overall_process_score': round(statistics.mean([scores['score'] for scores in process_scores.values()]), 2),
            'metric_scores': process_scores,
            'timing_analysis': timing_analysis,
            'discussion_flow': flow_analysis,
            'total_duration': self._calculate_total_duration(simulation_data)
        }
    
    def _score_process_metric(self, simulation_data: Dict[str, Any], metric: str) -> float:
        """Score a process metric (0-10 scale)."""
        
        # Simplified scoring - could be enhanced
        if metric == 'participation_balance':
            return self._score_participation_balance(simulation_data)
        elif metric == 'information_quality':
            return self._score_information_quality(simulation_data)
        elif metric == 'deliberation_depth':
            return self._score_deliberation_depth(simulation_data)
        elif metric == 'time_efficiency':
            return self._score_time_efficiency(simulation_data)
        else:
            return 7.0  # Default score
    
    def _score_participation_balance(self, simulation_data: Dict[str, Any]) -> float:
        """Score participation balance across agents."""
        
        # Get contribution counts per participant
        contribution_counts = defaultdict(int)
        
        phases = simulation_data.get('phases_completed', [])
        for phase in phases:
            if 'round_data' in phase:
                round_data = phase['round_data']
                for contrib in round_data.contributions:
                    contribution_counts[contrib['participant']] += 1
        
        if not contribution_counts:
            return 5.0
        
        # Calculate balance (lower standard deviation = better balance)
        counts = list(contribution_counts.values())
        if len(counts) <= 1:
            return 10.0
        
        std_dev = statistics.stdev(counts)
        mean_count = statistics.mean(counts)
        
        # Normalize: lower relative std dev = higher score
        if mean_count == 0:
            return 5.0
        
        relative_std = std_dev / mean_count
        balance_score = max(0, 10 - (relative_std * 10))
        
        return round(balance_score, 1)
    
    def _score_information_quality(self, simulation_data: Dict[str, Any]) -> float:
        """Score the quality of information used in discussions."""
        
        # Simple heuristic based on contribution length and detail
        total_words = 0
        total_contributions = 0
        
        phases = simulation_data.get('phases_completed', [])
        for phase in phases:
            if 'round_data' in phase:
                round_data = phase['round_data']
                for contrib in round_data.contributions:
                    total_words += len(contrib['content'].split())
                    total_contributions += 1
        
        if total_contributions == 0:
            return 5.0
        
        avg_words_per_contribution = total_words / total_contributions
        
        # Score based on average contribution length
        if avg_words_per_contribution > 150:
            return 9.0
        elif avg_words_per_contribution > 100:
            return 7.5
        elif avg_words_per_contribution > 50:
            return 6.0
        else:
            return 4.0
    
    def _score_deliberation_depth(self, simulation_data: Dict[str, Any]) -> float:
        """Score the depth of deliberation."""
        
        phases = simulation_data.get('phases_completed', [])
        discussion_phases = [p for p in phases if p['phase'] in ['initial_positions', 'open_discussion']]
        
        if not discussion_phases:
            return 5.0
        
        total_rounds = sum(p.get('total_rounds', 1) for p in discussion_phases)
        total_contributions = sum(p.get('contributions', 0) for p in discussion_phases)
        
        # Score based on discussion volume
        if total_rounds >= 5 and total_contributions >= 20:
            return 9.0
        elif total_rounds >= 3 and total_contributions >= 12:
            return 7.0
        elif total_rounds >= 2 and total_contributions >= 6:
            return 5.0
        else:
            return 3.0
    
    def _score_time_efficiency(self, simulation_data: Dict[str, Any]) -> float:
        """Score time efficiency of the process."""
        
        start_time = datetime.fromisoformat(simulation_data['start_time'])
        end_time = datetime.fromisoformat(simulation_data.get('end_time', datetime.now().isoformat()))
        
        duration_hours = (end_time - start_time).total_seconds() / 3600
        
        # Score based on duration (assuming 2-4 hours is optimal)
        if 2 <= duration_hours <= 4:
            return 9.0
        elif 1 <= duration_hours <= 6:
            return 7.0
        elif duration_hours <= 8:
            return 5.0
        else:
            return 3.0
    
    def _analyze_timing_efficiency(self, phases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze timing efficiency across phases."""
        
        phase_durations = {}
        
        for i, phase in enumerate(phases):
            if i < len(phases) - 1:
                start_time = datetime.fromisoformat(phase['timestamp'])
                end_time = datetime.fromisoformat(phases[i + 1]['timestamp'])
                duration = (end_time - start_time).total_seconds() / 60  # minutes
                phase_durations[phase['phase']] = duration
        
        return {
            'phase_durations': phase_durations,
            'total_phases': len(phases),
            'average_phase_duration': statistics.mean(phase_durations.values()) if phase_durations else 0
        }
    
    def _analyze_discussion_flow(self, discussion_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the flow and dynamics of discussion."""
        
        if not discussion_data or 'rounds' not in discussion_data:
            return {'error': 'No discussion data available'}
        
        rounds = discussion_data['rounds']
        
        # Analyze contribution patterns
        contributions_per_round = [len(round_data.contributions) for round_data in rounds]
        
        # Analyze engagement trends
        engagement_trend = "stable"
        if len(contributions_per_round) > 1:
            if contributions_per_round[-1] > contributions_per_round[0]:
                engagement_trend = "increasing"
            elif contributions_per_round[-1] < contributions_per_round[0]:
                engagement_trend = "decreasing"
        
        return {
            'total_rounds': len(rounds),
            'contributions_per_round': contributions_per_round,
            'average_contributions_per_round': statistics.mean(contributions_per_round) if contributions_per_round else 0,
            'engagement_trend': engagement_trend,
            'discussion_momentum': self._assess_discussion_momentum(rounds)
        }
    
    def _assess_discussion_momentum(self, rounds: List[Any]) -> str:
        """Assess the momentum of discussion across rounds."""
        
        if len(rounds) < 2:
            return "insufficient_data"
        
        # Simple assessment based on contribution lengths
        early_avg_length = statistics.mean([
            len(contrib['content']) for round_data in rounds[:len(rounds)//2]
            for contrib in round_data.contributions
        ]) if rounds else 0
        
        late_avg_length = statistics.mean([
            len(contrib['content']) for round_data in rounds[len(rounds)//2:]
            for contrib in round_data.contributions
        ]) if rounds else 0
        
        if late_avg_length > early_avg_length * 1.2:
            return "building"
        elif late_avg_length < early_avg_length * 0.8:
            return "declining"
        else:
            return "steady"
    
    def _analyze_participation_patterns(self, simulation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze participation patterns of different agents."""
        
        participant_stats = defaultdict(lambda: {
            'total_contributions': 0,
            'total_words': 0,
            'phases_participated': set(),
            'average_contribution_length': 0
        })
        
        phases = simulation_data.get('phases_completed', [])
        for phase in phases:
            if 'round_data' in phase:
                round_data = phase['round_data']
                for contrib in round_data.contributions:
                    participant = contrib['participant']
                    participant_stats[participant]['total_contributions'] += 1
                    participant_stats[participant]['total_words'] += len(contrib['content'].split())
                    participant_stats[participant]['phases_participated'].add(phase['phase'])
        
        # Calculate averages
        for participant, stats in participant_stats.items():
            if stats['total_contributions'] > 0:
                stats['average_contribution_length'] = stats['total_words'] / stats['total_contributions']
            stats['phases_participated'] = list(stats['phases_participated'])
        
        return dict(participant_stats)
    
    def _analyze_discussion_content(self, simulation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the content and themes of the discussion."""
        
        # Simple content analysis - could be enhanced with NLP
        all_content = []
        
        phases = simulation_data.get('phases_completed', [])
        for phase in phases:
            if 'round_data' in phase:
                round_data = phase['round_data']
                for contrib in round_data.contributions:
                    all_content.append(contrib['content'])
        
        combined_text = ' '.join(all_content).lower()
        
        # Identify key themes
        theme_keywords = {
            'military': ['military', 'force', 'defense', 'army', 'navy', 'air force', 'troops'],
            'economic': ['economic', 'trade', 'market', 'financial', 'cost', 'budget', 'economy'],
            'diplomatic': ['diplomatic', 'negotiation', 'alliance', 'treaty', 'international'],
            'domestic': ['domestic', 'public', 'congress', 'political', 'election', 'voters'],
            'risk': ['risk', 'threat', 'danger', 'concern', 'problem', 'challenge']
        }
        
        theme_scores = {}
        for theme, keywords in theme_keywords.items():
            score = sum(1 for keyword in keywords if keyword in combined_text)
            theme_scores[theme] = score
        
        return {
            'total_words': len(combined_text.split()),
            'theme_analysis': theme_scores,
            'dominant_themes': sorted(theme_scores.items(), key=lambda x: x[1], reverse=True)[:3],
            'content_diversity': len(set(combined_text.split())) / len(combined_text.split()) if combined_text else 0
        }
    
    def _assess_potential_outcomes(self, simulation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess potential outcomes of the decision."""
        
        final_decision = simulation_data.get('final_decision', {})
        decision_text = final_decision.get('decision', '')
        
        # Simple outcome assessment
        outcome_indicators = {
            'success_likelihood': self._assess_success_likelihood(decision_text),
            'implementation_challenges': self._identify_implementation_challenges(decision_text),
            'stakeholder_reactions': self._predict_stakeholder_reactions(decision_text),
            'unintended_consequences': self._identify_potential_consequences(decision_text)
        }
        
        return outcome_indicators
    
    def _assess_success_likelihood(self, decision_text: str) -> Dict[str, Any]:
        """Assess likelihood of decision success."""
        
        positive_indicators = ['plan', 'strategy', 'systematic', 'coordinated', 'resources', 'support']
        negative_indicators = ['risk', 'uncertain', 'difficult', 'challenge', 'opposition']
        
        decision_lower = decision_text.lower()
        positive_count = sum(1 for indicator in positive_indicators if indicator in decision_lower)
        negative_count = sum(1 for indicator in negative_indicators if indicator in decision_lower)
        
        if positive_count > negative_count:
            likelihood = "High"
        elif positive_count == negative_count:
            likelihood = "Medium"
        else:
            likelihood = "Low"
        
        return {
            'likelihood': likelihood,
            'positive_indicators': positive_count,
            'negative_indicators': negative_count,
            'confidence': min(abs(positive_count - negative_count) * 20, 100)
        }
    
    def _identify_implementation_challenges(self, decision_text: str) -> List[str]:
        """Identify potential implementation challenges."""
        
        challenge_keywords = {
            'resource_constraints': ['budget', 'cost', 'funding', 'resource'],
            'political_opposition': ['opposition', 'congress', 'political', 'resistance'],
            'technical_complexity': ['complex', 'technical', 'difficult', 'sophisticated'],
            'time_pressure': ['urgent', 'immediate', 'deadline', 'time'],
            'coordination_issues': ['coordination', 'multiple', 'agencies', 'departments']
        }
        
        identified_challenges = []
        decision_lower = decision_text.lower()
        
        for challenge_type, keywords in challenge_keywords.items():
            if any(keyword in decision_lower for keyword in keywords):
                identified_challenges.append(challenge_type.replace('_', ' ').title())
        
        return identified_challenges
    
    def _predict_stakeholder_reactions(self, decision_text: str) -> Dict[str, str]:
        """Predict likely stakeholder reactions."""
        
        # Simplified prediction based on content
        stakeholder_reactions = {
            'Congress': 'Mixed - depends on party alignment',
            'Military': 'Supportive - if resources provided',
            'Public': 'Cautious - needs clear communication',
            'Allies': 'Concerned - requires consultation',
            'Media': 'Critical - will scrutinize details'
        }
        
        return stakeholder_reactions
    
    def _identify_potential_consequences(self, decision_text: str) -> List[str]:
        """Identify potential unintended consequences."""
        
        # Simple consequence identification
        consequences = [
            "Escalation of tensions with adversaries",
            "Strain on alliance relationships",
            "Domestic political backlash",
            "Economic market volatility",
            "Resource allocation challenges"
        ]
        
        return consequences[:3]  # Return top 3
    
    def _extract_lessons_learned(self, simulation_data: Dict[str, Any]) -> List[str]:
        """Extract key lessons learned from the simulation."""
        
        lessons = [
            "Importance of comprehensive stakeholder analysis",
            "Need for clear implementation timelines",
            "Value of diverse expert perspectives",
            "Critical role of risk assessment",
            "Significance of contingency planning"
        ]
        
        return lessons
    
    def _generate_recommendations(self, simulation_data: Dict[str, Any]) -> List[str]:
        """Generate recommendations for future simulations."""
        
        recommendations = [
            "Enhance pre-simulation briefing materials",
            "Allocate more time for risk assessment discussion",
            "Include additional subject matter experts",
            "Develop more detailed implementation scenarios",
            "Improve decision documentation process"
        ]
        
        return recommendations
    
    def _calculate_overall_scores(self, analysis_results: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall scores for the simulation."""
        
        decision_score = analysis_results.get('decision_analysis', {}).get('overall_quality_score', 5.0)
        process_score = analysis_results.get('process_analysis', {}).get('overall_process_score', 5.0)
        
        overall_score = (decision_score + process_score) / 2
        
        return {
            'decision_quality': decision_score,
            'process_effectiveness': process_score,
            'overall_simulation': round(overall_score, 2)
        }
    
    def _calculate_total_duration(self, simulation_data: Dict[str, Any]) -> str:
        """Calculate total simulation duration."""
        
        start_time = datetime.fromisoformat(simulation_data['start_time'])
        end_time = datetime.fromisoformat(simulation_data.get('end_time', datetime.now().isoformat()))
        
        duration = end_time - start_time
        hours = duration.seconds // 3600
        minutes = (duration.seconds % 3600) // 60
        
        return f"{hours}h {minutes}m"
