"""
Discussion Manager

Manages multi-agent strategic discussions with customizable turn orders,
round control, user participation, and real-time interaction.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..agents.base_agent import BaseStrategicAgent
from ..memory.memory_manager import MemoryManager


class DiscussionPhase(Enum):
    """Discussion phases for structured conversations."""
    PREPARATION = "preparation"
    OPENING = "opening"
    INITIAL_POSITIONS = "initial_positions"
    OPEN_DISCUSSION = "open_discussion"
    DECISION_PHASE = "decision_phase"
    DEBRIEF = "debrief"
    COMPLETED = "completed"


@dataclass
class TurnOrder:
    """Defines turn order configuration for discussions."""
    participants: List[str]
    mode: str = "sequential"  # sequential, random, dynamic, user_controlled
    max_consecutive_turns: int = 2
    allow_interruptions: bool = False
    enable_direct_responses: bool = True


@dataclass
class DiscussionRound:
    """Represents a single discussion round."""
    round_number: int
    phase: DiscussionPhase
    topic_focus: str
    participants: List[str]
    contributions: List[Dict[str, Any]]
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_limit: Optional[int] = None  # seconds


class DiscussionManager:
    """
    Manages strategic discussions between multiple agents.
    
    Features:
    - Customizable turn orders and discussion phases
    - Real-time user participation
    - Round and time management
    - Conversation flow control
    - Memory integration
    """
    
    def __init__(self, memory_manager: MemoryManager,
                 config: Dict[str, Any] = None):
        """Initialize discussion manager."""

        self.memory_manager = memory_manager
        self.config = config or {}

        # Discussion state
        self.current_discussion = None
        self.participants = {}  # agent_name -> agent_instance
        self.discussion_history = []
        self.current_phase = DiscussionPhase.PREPARATION
        self.current_round = 0

        # Turn management
        self.turn_order = None
        self.selected_participants = []  # Dynamically selected participants
        self.custom_speaking_order = []  # Custom order if specified

        # Advanced discussion settings
        self.enforce_turn_order = True
        self.allow_interruptions = False
        self.enable_side_conversations = False
        self.max_consecutive_turns = 1
        self.turn_time_limit = 120
        self.discussion_timeout = 1800  # 30 minutes
        self.current_speaker = None
        self.turn_queue = []
        self.speaking_history = []
        
        # User participation
        self.user_participants = {}  # user_name -> user_agent
        self.pending_user_inputs = {}
        
        # Event callbacks
        self.event_callbacks = {
            'round_start': [],
            'turn_start': [],
            'contribution_made': [],
            'phase_change': [],
            'discussion_complete': []
        }
        
        # Configuration
        self.max_discussion_rounds = self.config.get('max_discussion_rounds', 10)
        self.default_round_time_limit = self.config.get('default_round_time_limit', 300)
        self.enable_user_participation = self.config.get('enable_user_participation', True)
    
    def add_participant(self, agent: BaseStrategicAgent):
        """Add an agent participant to the discussion."""
        self.participants[agent.name] = agent
        print(f"Added participant: {agent.name} ({agent.role})")
    
    def add_user_participant(self, user_name: str, user_agent: BaseStrategicAgent):
        """Add a user-controlled participant."""
        self.add_participant(user_agent)
        self.user_participants[user_name] = user_agent
        print(f"Added user participant: {user_name} as {user_agent.name}")

    def configure_discussion(self, selected_participants: List[str],
                           turn_order_mode: str = "Round Robin",
                           custom_order: List[str] = None,
                           discussion_settings: Dict[str, Any] = None):
        """Configure discussion with selected participants and settings."""

        # Set selected participants
        self.selected_participants = [name for name in selected_participants
                                    if name in self.participants]

        # Configure turn order
        if turn_order_mode == "Custom Order" and custom_order:
            self.custom_speaking_order = [name for name in custom_order
                                        if name in self.selected_participants]
        elif turn_order_mode == "Moderator Controlled":
            # Put moderator first
            moderator_agents = [name for name in self.selected_participants
                              if 'moderator' in name.lower()]
            other_agents = [name for name in self.selected_participants
                          if 'moderator' not in name.lower()]
            self.custom_speaking_order = moderator_agents + other_agents
        else:
            # Round robin or free discussion
            self.custom_speaking_order = self.selected_participants.copy()

        # Apply discussion settings
        if discussion_settings:
            self.enforce_turn_order = discussion_settings.get('enforce_turn_order', True)
            self.allow_interruptions = discussion_settings.get('allow_interruptions', False)
            self.enable_side_conversations = discussion_settings.get('enable_side_conversations', False)
            self.max_consecutive_turns = discussion_settings.get('max_consecutive_turns', 1)
            self.turn_time_limit = discussion_settings.get('turn_time_limit', 120)
            self.discussion_timeout = discussion_settings.get('discussion_timeout', 1800)

        print(f"📋 Discussion configured:")
        print(f"  Selected participants: {len(self.selected_participants)}")
        print(f"  Speaking order: {self.custom_speaking_order}")
        print(f"  Turn order mode: {turn_order_mode}")
        print(f"  Enforce order: {self.enforce_turn_order}")

    def get_active_participants(self) -> List[str]:
        """Get list of currently active participants."""

        return self.selected_participants if self.selected_participants else list(self.participants.keys())

    def get_speaking_order(self) -> List[str]:
        """Get the current speaking order."""

        if self.custom_speaking_order:
            return self.custom_speaking_order
        else:
            return self.get_active_participants()
    
    def set_turn_order(self, turn_order: TurnOrder):
        """Set the turn order configuration."""
        self.turn_order = turn_order
        self._initialize_turn_queue()
    
    def _initialize_turn_queue(self):
        """Initialize the turn queue based on turn order configuration."""
        if not self.turn_order:
            self.turn_queue = list(self.participants.keys())
            return
        
        if self.turn_order.mode == "sequential":
            self.turn_queue = self.turn_order.participants.copy()
        elif self.turn_order.mode == "random":
            import random
            self.turn_queue = self.turn_order.participants.copy()
            random.shuffle(self.turn_queue)
        elif self.turn_order.mode == "dynamic":
            # Dynamic ordering based on relevance/context
            self.turn_queue = self._calculate_dynamic_order()
        else:
            self.turn_queue = self.turn_order.participants.copy()
    
    def _calculate_dynamic_order(self) -> List[str]:
        """Calculate dynamic turn order based on context and relevance."""
        # Simple implementation - could be enhanced with ML/relevance scoring
        base_order = self.turn_order.participants.copy()
        
        # Prioritize based on recent contribution relevance
        if self.speaking_history:
            recent_speakers = [entry['speaker'] for entry in self.speaking_history[-3:]]
            non_recent = [p for p in base_order if p not in recent_speakers]
            return non_recent + recent_speakers
        
        return base_order
    
    def start_discussion(self, scenario: Dict[str, Any], 
                        discussion_config: Dict[str, Any] = None) -> str:
        """Start a new strategic discussion."""
        
        if not self.participants:
            raise ValueError("No participants added to discussion")
        
        # Initialize discussion
        self.current_discussion = {
            'id': f"discussion_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'scenario': scenario,
            'config': discussion_config or {},
            'start_time': datetime.now(),
            'participants': list(self.participants.keys()),
            'rounds': []
        }
        
        # Set scenario context for all participants
        for agent in self.participants.values():
            agent.set_scenario_context(scenario)
        
        # Initialize turn order if not set
        if not self.turn_order:
            self.set_turn_order(TurnOrder(list(self.participants.keys())))
        
        # Start with opening phase
        self.current_phase = DiscussionPhase.OPENING
        self._trigger_event('discussion_start', self.current_discussion)
        
        # Get moderator to open the discussion
        moderator = self._get_moderator()
        if moderator:
            opening_statement = moderator.initialize_simulation(
                scenario, list(self.participants.keys())
            )
            
            self._record_contribution(moderator.name, opening_statement, "opening")
            return opening_statement
        
        return f"Strategic discussion started: {scenario.get('title', 'Unnamed Scenario')}"
    
    def run_discussion_round(self, round_config: Dict[str, Any] = None) -> DiscussionRound:
        """Run a single discussion round."""
        
        self.current_round += 1
        round_config = round_config or {}
        
        # Create round
        discussion_round = DiscussionRound(
            round_number=self.current_round,
            phase=self.current_phase,
            topic_focus=round_config.get('topic_focus', 'Strategic Analysis'),
            participants=round_config.get('participants', list(self.participants.keys())),
            contributions=[],
            start_time=datetime.now(),
            duration_limit=round_config.get('duration_limit', self.default_round_time_limit)
        )
        
        self._trigger_event('round_start', discussion_round)
        
        # Run turns for this round
        for participant_name in discussion_round.participants:
            if participant_name not in self.participants:
                continue
            
            # Check time limit
            if discussion_round.duration_limit:
                elapsed = (datetime.now() - discussion_round.start_time).seconds
                if elapsed > discussion_round.duration_limit:
                    print(f"Round {self.current_round} time limit reached")
                    break
            
            # Execute turn
            contribution = self._execute_turn(participant_name, discussion_round)
            if contribution:
                discussion_round.contributions.append(contribution)
        
        # Complete round
        discussion_round.end_time = datetime.now()
        self.current_discussion['rounds'].append(discussion_round)
        
        # Store round in memory
        self.memory_manager.store_discussion_round(
            self.current_discussion['id'],
            discussion_round
        )
        
        return discussion_round
    
    def _execute_turn(self, participant_name: str, 
                     discussion_round: DiscussionRound) -> Optional[Dict[str, Any]]:
        """Execute a single participant's turn."""
        
        if participant_name not in self.participants:
            return None
        
        agent = self.participants[participant_name]
        self.current_speaker = participant_name
        
        self._trigger_event('turn_start', {'participant': participant_name, 'round': discussion_round})
        
        # Check if this is a user participant
        if participant_name in [ua.name for ua in self.user_participants.values()]:
            contribution_text = self._handle_user_turn(agent, discussion_round)
        else:
            # Get previous contributions for context
            previous_contributions = [
                contrib['content'] for contrib in discussion_round.contributions
            ]
            
            # Agent generates contribution
            contribution_text = agent.contribute_to_discussion(
                discussion_round.topic_focus,
                previous_contributions
            )
        
        # Create contribution record
        contribution = {
            'participant': participant_name,
            'content': contribution_text,
            'timestamp': datetime.now().isoformat(),
            'round_number': discussion_round.round_number,
            'phase': discussion_round.phase.value,
            'turn_order': len(discussion_round.contributions) + 1
        }
        
        # Record contribution
        self._record_contribution(participant_name, contribution_text, discussion_round.phase.value)
        self._trigger_event('contribution_made', contribution)
        
        # Update speaking history
        self.speaking_history.append({
            'speaker': participant_name,
            'timestamp': datetime.now(),
            'round': discussion_round.round_number
        })
        
        return contribution
    
    def _handle_user_turn(self, user_agent: BaseStrategicAgent, 
                         discussion_round: DiscussionRound) -> str:
        """Handle a user participant's turn."""
        
        print(f"\n{'='*60}")
        print(f"YOUR TURN: {user_agent.name}")
        print(f"Round {discussion_round.round_number} - {discussion_round.topic_focus}")
        print(f"{'='*60}")
        
        # Show recent contributions
        if discussion_round.contributions:
            print("\nRecent contributions:")
            for contrib in discussion_round.contributions[-3:]:
                print(f"- {contrib['participant']}: {contrib['content'][:150]}...")
        
        print(f"\nAs {user_agent.name}, you can:")
        print("1. Provide your own input")
        print("2. Let the agent respond based on its persona")
        print("3. Give guidance to the agent")
        
        # In a real implementation, this would wait for user input
        # For now, simulate with agent response
        user_input = input("\nYour choice (1/2/3) or direct input: ").strip()
        
        if user_input == "1" or (user_input not in ["2", "3"] and len(user_input) > 5):
            # Direct user input
            if user_input not in ["1", "2", "3"]:
                return user_input
            else:
                direct_input = input("Enter your contribution: ").strip()
                return direct_input
        elif user_input == "3":
            # User guidance to agent
            guidance = input("Provide guidance to the agent: ").strip()
            user_agent.add_user_input(guidance, f"Round {discussion_round.round_number}")
            
            # Agent responds with guidance
            previous_contributions = [contrib['content'] for contrib in discussion_round.contributions]
            return user_agent.contribute_to_discussion(
                discussion_round.topic_focus,
                previous_contributions
            )
        else:
            # Agent autonomous response
            previous_contributions = [contrib['content'] for contrib in discussion_round.contributions]
            return user_agent.contribute_to_discussion(
                discussion_round.topic_focus,
                previous_contributions
            )
    
    def _record_contribution(self, participant: str, content: str, phase: str):
        """Record a contribution in memory."""
        self.memory_manager.store_contribution(
            participant, 
            self.current_discussion['scenario'].get('title', 'Discussion'),
            content,
            self.current_round
        )
    
    def change_phase(self, new_phase: DiscussionPhase, 
                    phase_config: Dict[str, Any] = None):
        """Change the discussion phase."""
        
        old_phase = self.current_phase
        self.current_phase = new_phase
        
        phase_change_event = {
            'old_phase': old_phase,
            'new_phase': new_phase,
            'config': phase_config or {},
            'timestamp': datetime.now()
        }
        
        self._trigger_event('phase_change', phase_change_event)
        
        # Phase-specific actions
        if new_phase == DiscussionPhase.DECISION_PHASE:
            self._prepare_decision_phase()
        elif new_phase == DiscussionPhase.DEBRIEF:
            self._prepare_debrief_phase()
    
    def _prepare_decision_phase(self):
        """Prepare for the decision phase."""
        # Typically involves Trump agent making final decision
        trump_agent = self._get_trump_agent()
        if trump_agent:
            self.turn_queue = [trump_agent.name]
    
    def _prepare_debrief_phase(self):
        """Prepare for the debrief phase."""
        # Typically involves moderator conducting review
        moderator = self._get_moderator()
        if moderator:
            self.turn_queue = [moderator.name]
    
    def _get_moderator(self) -> Optional[BaseStrategicAgent]:
        """Get the moderator agent."""
        for agent in self.participants.values():
            if agent.agent_type == 'simulation_controller':
                return agent
        return None
    
    def _get_trump_agent(self) -> Optional[BaseStrategicAgent]:
        """Get the Trump agent."""
        for agent in self.participants.values():
            if 'trump' in agent.name.lower():
                return agent
        return None
    
    def add_event_callback(self, event_type: str, callback: Callable):
        """Add an event callback."""
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)
    
    def _trigger_event(self, event_type: str, event_data: Any):
        """Trigger event callbacks."""
        if event_type in self.event_callbacks:
            for callback in self.event_callbacks[event_type]:
                try:
                    callback(event_data)
                except Exception as e:
                    print(f"Error in event callback: {e}")
    
    def get_discussion_summary(self) -> Dict[str, Any]:
        """Get a summary of the current discussion."""
        
        if not self.current_discussion:
            return {}
        
        total_contributions = sum(len(round_data.contributions) 
                                for round_data in self.current_discussion['rounds'])
        
        participant_stats = {}
        for participant in self.participants:
            contributions = sum(1 for round_data in self.current_discussion['rounds']
                              for contrib in round_data.contributions
                              if contrib['participant'] == participant)
            participant_stats[participant] = contributions
        
        return {
            'discussion_id': self.current_discussion['id'],
            'scenario': self.current_discussion['scenario']['title'],
            'current_phase': self.current_phase.value,
            'total_rounds': len(self.current_discussion['rounds']),
            'total_contributions': total_contributions,
            'participant_stats': participant_stats,
            'duration': str(datetime.now() - self.current_discussion['start_time']),
            'status': 'active' if self.current_phase != DiscussionPhase.COMPLETED else 'completed'
        }
