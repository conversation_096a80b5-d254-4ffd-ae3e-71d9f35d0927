"""
Simulation Controller

Main controller for strategic simulations, orchestrating the entire process
from initialization to completion and analysis.
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path

from .discussion_manager import DiscussionManager, DiscussionPhase, TurnOrder
from .decision_analyzer import DecisionAnalyzer
from ..agents import (
    TrumpAgent, ModeratorAgent, PoliticalExpert, 
    MilitaryExpert, EconomicExpert, CustomAgent
)
from ..memory.memory_manager import MemoryManager
from ..knowledge.rag_system import RAGSystem


class SimulationController:
    """
    Main controller for strategic simulations.
    
    Orchestrates:
    - Agent initialization and configuration
    - Discussion flow management
    - Decision making process
    - Analysis and reporting
    - Memory and knowledge integration
    """
    
    def __init__(self, config_path: str = None):
        """Initialize simulation controller."""
        
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Initialize core components
        self.memory_manager = MemoryManager(self.config.get('memory_settings', {}))
        self.discussion_manager = DiscussionManager(
            self.memory_manager, 
            self.config.get('discussion_flow', {})
        )
        self.decision_analyzer = DecisionAnalyzer(self.memory_manager)
        
        # Agent management
        self.agents = {}
        self.agent_configs = {}
        
        # Simulation state
        self.current_simulation = None
        self.simulation_history = []
        
        # Knowledge systems
        self.knowledge_systems = {}
        
        # Event logging
        self.event_log = []
    
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """Load simulation configuration."""
        
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # Default configuration
        return {
            "simulation_settings": {
                "max_discussion_rounds": 10,
                "default_round_time_limit": 300,
                "enable_user_participation": True,
                "auto_save_conversations": True,
                "enable_memory_persistence": True,
                "enable_rag_retrieval": True
            },
            "discussion_flow": {
                "phases": [
                    {"name": "opening", "duration": 60, "participants": ["moderator"]},
                    {"name": "initial_positions", "duration": 180, "participants": "all_except_moderator"},
                    {"name": "open_discussion", "duration": 600, "participants": "all"},
                    {"name": "decision_phase", "duration": 300, "participants": ["trump"]},
                    {"name": "debrief", "duration": 240, "participants": ["moderator"]}
                ]
            }
        }
    
    def initialize_agents(self, agent_configs: Dict[str, Any], 
                         knowledge_base_dir: str = None) -> Dict[str, Any]:
        """Initialize all agents for the simulation."""
        
        self.agent_configs = agent_configs
        knowledge_base_dir = knowledge_base_dir or "config/knowledge_base"
        
        # Initialize knowledge systems
        if knowledge_base_dir and Path(knowledge_base_dir).exists():
            self._initialize_knowledge_systems(knowledge_base_dir)
        
        # Create agents based on configuration
        for agent_id, config in agent_configs.get('agents', {}).items():
            agent = self._create_agent(agent_id, config, knowledge_base_dir)
            if agent:
                self.agents[agent_id] = agent
                self.discussion_manager.add_participant(agent)
        
        self._log_event('agents_initialized', {
            'total_agents': len(self.agents),
            'agent_types': [agent.agent_type for agent in self.agents.values()]
        })
        
        return {
            'initialized_agents': list(self.agents.keys()),
            'total_count': len(self.agents),
            'knowledge_systems': list(self.knowledge_systems.keys())
        }
    
    def _initialize_knowledge_systems(self, knowledge_base_dir: str):
        """Initialize RAG knowledge systems for agents."""
        
        knowledge_dir = Path(knowledge_base_dir)
        
        for knowledge_file in knowledge_dir.glob("*.txt"):
            agent_name = knowledge_file.stem.replace('_knowledge', '')
            
            try:
                rag_system = RAGSystem(str(knowledge_file))
                self.knowledge_systems[agent_name] = rag_system
                print(f"Initialized knowledge system for {agent_name}")
            except Exception as e:
                print(f"Failed to initialize knowledge system for {agent_name}: {e}")
    
    def _create_agent(self, agent_id: str, config: Dict[str, Any], 
                     knowledge_base_dir: str) -> Optional[Any]:
        """Create an agent instance based on configuration."""
        
        agent_type = config.get('type', 'generic')
        knowledge_base_path = None
        
        # Get knowledge base path if specified
        if 'knowledge_base' in config.get('persona', {}):
            kb_file = config['persona']['knowledge_base']
            knowledge_base_path = Path(knowledge_base_dir) / kb_file
            if not knowledge_base_path.exists():
                knowledge_base_path = None
        
        try:
            if agent_id == 'trump' or agent_type == 'political_leader':
                return TrumpAgent(config, str(knowledge_base_path) if knowledge_base_path else None, self.memory_manager)
            
            elif agent_id == 'moderator' or agent_type == 'simulation_controller':
                return ModeratorAgent(config, str(knowledge_base_path) if knowledge_base_path else None, self.memory_manager)
            
            elif agent_id == 'political_expert' or 'political' in agent_id:
                return PoliticalExpert(config, str(knowledge_base_path) if knowledge_base_path else None, self.memory_manager)
            
            elif agent_id == 'military_expert' or 'military' in agent_id:
                return MilitaryExpert(config, str(knowledge_base_path) if knowledge_base_path else None, self.memory_manager)
            
            elif agent_id == 'economic_expert' or 'economic' in agent_id:
                return EconomicExpert(config, str(knowledge_base_path) if knowledge_base_path else None, self.memory_manager)
            
            elif agent_type == 'custom':
                return CustomAgent(config.get('name', f'Custom_{agent_id}'), config, 
                                 str(knowledge_base_path) if knowledge_base_path else None, self.memory_manager)
            
            else:
                print(f"Unknown agent type: {agent_type} for {agent_id}")
                return None
                
        except Exception as e:
            print(f"Error creating agent {agent_id}: {e}")
            return None
    
    def add_custom_agent(self, name: str, config: Dict[str, Any]) -> bool:
        """Add a custom agent to the simulation."""
        
        try:
            custom_agent = CustomAgent(name, config, None, self.memory_manager)
            self.agents[name.lower().replace(' ', '_')] = custom_agent
            self.discussion_manager.add_participant(custom_agent)
            
            self._log_event('custom_agent_added', {'name': name, 'config': config})
            return True
            
        except Exception as e:
            print(f"Error adding custom agent {name}: {e}")
            return False
    
    def run_simulation(self, scenario: Dict[str, Any],
                      simulation_config: Dict[str, Any] = None,
                      selected_agents: List[str] = None,
                      turn_order_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Run a complete strategic simulation."""

        if not self.agents:
            raise ValueError("No agents initialized. Call initialize_agents() first.")

        # Determine participating agents
        if selected_agents:
            participating_agents = {name: agent for name, agent in self.agents.items()
                                  if name in selected_agents}
            if not participating_agents:
                raise ValueError("No valid agents selected from the provided list.")
        else:
            participating_agents = self.agents

        print(f"👥 Participating agents: {list(participating_agents.keys())}")

        # Configure discussion manager with selected agents
        if hasattr(self, 'discussion_manager') and self.discussion_manager:
            self.discussion_manager.configure_discussion(
                selected_participants=list(participating_agents.keys()),
                turn_order_mode=turn_order_config.get('mode', 'Round Robin') if turn_order_config else 'Round Robin',
                custom_order=turn_order_config.get('custom_order') if turn_order_config else None,
                discussion_settings=turn_order_config.get('settings') if turn_order_config else None
            )

        # Initialize simulation
        simulation_id = f"sim_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.current_simulation = {
            'id': simulation_id,
            'scenario': scenario,
            'config': simulation_config or {},
            'selected_agents': selected_agents or list(self.agents.keys()),
            'participating_agents': list(participating_agents.keys()),
            'turn_order_config': turn_order_config,
            'start_time': datetime.now(),
            'phases_completed': [],
            'final_decision': None,
            'analysis_results': {}
        }
        
        self._log_event('simulation_started', self.current_simulation)
        
        try:
            # Phase 1: Opening
            self._run_opening_phase(scenario)
            
            # Phase 2: Initial Positions
            self._run_initial_positions_phase()
            
            # Phase 3: Open Discussion
            self._run_open_discussion_phase()
            
            # Phase 4: Decision Phase
            final_decision = self._run_decision_phase()
            
            # Phase 5: Debrief
            debrief_results = self._run_debrief_phase(final_decision)
            
            # Complete simulation
            self.current_simulation.update({
                'end_time': datetime.now(),
                'final_decision': final_decision,
                'debrief_results': debrief_results,
                'status': 'completed'
            })
            
            # Analyze results
            analysis_results = self.decision_analyzer.analyze_simulation(self.current_simulation)
            self.current_simulation['analysis_results'] = analysis_results
            
            # Store in history
            self.simulation_history.append(self.current_simulation)
            
            self._log_event('simulation_completed', self.current_simulation)
            
            return self.current_simulation
            
        except Exception as e:
            self._log_event('simulation_error', {'error': str(e)})
            self.current_simulation['status'] = 'error'
            self.current_simulation['error'] = str(e)
            raise
    
    def _run_opening_phase(self, scenario: Dict[str, Any]):
        """Run the opening phase of the simulation."""
        
        self.discussion_manager.change_phase(DiscussionPhase.OPENING)
        
        # Start discussion with moderator opening
        opening_statement = self.discussion_manager.start_discussion(scenario)
        
        self.current_simulation['phases_completed'].append({
            'phase': 'opening',
            'timestamp': datetime.now().isoformat(),
            'output': opening_statement
        })
        
        self._log_event('phase_completed', {'phase': 'opening'})
    
    def _run_initial_positions_phase(self):
        """Run the initial positions phase."""
        
        self.discussion_manager.change_phase(DiscussionPhase.INITIAL_POSITIONS)
        
        # Each participant states their initial position
        participants = [name for name in self.agents.keys() if name != 'moderator']
        
        round_config = {
            'topic_focus': 'Initial Strategic Positions',
            'participants': participants,
            'duration_limit': 180
        }
        
        discussion_round = self.discussion_manager.run_discussion_round(round_config)
        
        self.current_simulation['phases_completed'].append({
            'phase': 'initial_positions',
            'timestamp': datetime.now().isoformat(),
            'round_data': discussion_round,
            'contributions': len(discussion_round.contributions)
        })
        
        self._log_event('phase_completed', {'phase': 'initial_positions'})
    
    def _run_open_discussion_phase(self):
        """Run the open discussion phase."""
        
        self.discussion_manager.change_phase(DiscussionPhase.OPEN_DISCUSSION)
        
        # Multiple rounds of open discussion
        max_rounds = self.config.get('simulation_settings', {}).get('max_discussion_rounds', 5)
        
        for round_num in range(max_rounds):
            round_config = {
                'topic_focus': f'Strategic Analysis Round {round_num + 1}',
                'participants': list(self.agents.keys()),
                'duration_limit': 600
            }
            
            discussion_round = self.discussion_manager.run_discussion_round(round_config)
            
            # Check if discussion is converging or time to move on
            if self._should_end_discussion(discussion_round, round_num):
                break
        
        self.current_simulation['phases_completed'].append({
            'phase': 'open_discussion',
            'timestamp': datetime.now().isoformat(),
            'total_rounds': self.discussion_manager.current_round,
            'total_contributions': sum(len(r.contributions) for r in self.discussion_manager.current_discussion['rounds'])
        })
        
        self._log_event('phase_completed', {'phase': 'open_discussion'})
    
    def _run_decision_phase(self) -> Dict[str, Any]:
        """Run the decision phase with Trump making final decision."""
        
        self.discussion_manager.change_phase(DiscussionPhase.DECISION_PHASE)
        
        # Get Trump agent
        trump_agent = None
        for agent in self.agents.values():
            if isinstance(agent, TrumpAgent):
                trump_agent = agent
                break
        
        if not trump_agent:
            raise ValueError("No Trump agent found for decision phase")
        
        # Prepare decision context
        discussion_summary = self._generate_discussion_summary()
        options = self._extract_strategic_options()
        
        # Trump makes final decision
        final_decision = trump_agent.make_final_decision(discussion_summary, options)
        
        self.current_simulation['phases_completed'].append({
            'phase': 'decision_phase',
            'timestamp': datetime.now().isoformat(),
            'decision_maker': trump_agent.name,
            'decision': final_decision
        })
        
        self._log_event('phase_completed', {'phase': 'decision_phase'})
        
        return final_decision
    
    def _run_debrief_phase(self, final_decision: Dict[str, Any]) -> Dict[str, Any]:
        """Run the debrief phase with moderator analysis."""
        
        self.discussion_manager.change_phase(DiscussionPhase.DEBRIEF)
        
        # Get moderator
        moderator = None
        for agent in self.agents.values():
            if isinstance(agent, ModeratorAgent):
                moderator = agent
                break
        
        if not moderator:
            raise ValueError("No moderator agent found for debrief phase")
        
        # Conduct after-action review
        simulation_data = {
            'scenario': self.current_simulation['scenario'],
            'discussion_rounds': self.discussion_manager.current_discussion['rounds'],
            'participants': list(self.agents.keys())
        }
        
        debrief_results = moderator.conduct_after_action_review(final_decision, simulation_data)
        
        self.current_simulation['phases_completed'].append({
            'phase': 'debrief',
            'timestamp': datetime.now().isoformat(),
            'moderator': moderator.name,
            'results': debrief_results
        })
        
        self._log_event('phase_completed', {'phase': 'debrief'})
        
        return {'debrief_analysis': debrief_results, 'moderator': moderator.name}
    
    def _should_end_discussion(self, discussion_round, round_num: int) -> bool:
        """Determine if discussion should end."""
        
        # Simple heuristics - could be enhanced
        if round_num >= 3:  # Minimum rounds
            # Check if contributions are getting repetitive or short
            if len(discussion_round.contributions) < len(self.agents) // 2:
                return True
            
            # Check average contribution length
            avg_length = sum(len(c['content']) for c in discussion_round.contributions) / len(discussion_round.contributions)
            if avg_length < 100:  # Very short contributions
                return True
        
        return False
    
    def _generate_discussion_summary(self) -> str:
        """Generate a summary of the discussion for decision making."""
        
        all_contributions = []
        for round_data in self.discussion_manager.current_discussion['rounds']:
            all_contributions.extend(round_data.contributions)
        
        # Simple summary - could be enhanced with NLP
        summary_points = []
        for contrib in all_contributions[-10:]:  # Last 10 contributions
            summary_points.append(f"{contrib['participant']}: {contrib['content'][:200]}...")
        
        return "\n".join(summary_points)
    
    def _extract_strategic_options(self) -> List[str]:
        """Extract strategic options from the discussion."""
        
        options = []
        all_contributions = []
        
        for round_data in self.discussion_manager.current_discussion['rounds']:
            all_contributions.extend(round_data.contributions)
        
        # Simple extraction - look for option-related keywords
        for contrib in all_contributions:
            content = contrib['content'].lower()
            if any(keyword in content for keyword in ['option', 'approach', 'strategy', 'recommend', 'should']):
                # Extract sentences with options
                sentences = contrib['content'].split('.')
                for sentence in sentences:
                    if any(keyword in sentence.lower() for keyword in ['option', 'approach', 'strategy']):
                        if len(sentence.strip()) > 20:
                            options.append(sentence.strip())
        
        # Return unique options, limited to reasonable number
        unique_options = list(set(options))[:5]
        
        if not unique_options:
            unique_options = ["Diplomatic engagement", "Economic measures", "Military options", "Multilateral approach"]
        
        return unique_options
    
    def _log_event(self, event_type: str, event_data: Any):
        """Log simulation events."""
        
        event = {
            'timestamp': datetime.now().isoformat(),
            'type': event_type,
            'data': event_data
        }
        
        self.event_log.append(event)
        
        # Also store in memory manager
        self.memory_manager.store_simulation_event(event_type, event_data)
    
    def get_simulation_status(self) -> Dict[str, Any]:
        """Get current simulation status."""
        
        if not self.current_simulation:
            return {'status': 'not_started'}
        
        return {
            'simulation_id': self.current_simulation['id'],
            'status': self.current_simulation.get('status', 'running'),
            'current_phase': self.discussion_manager.current_phase.value,
            'phases_completed': len(self.current_simulation['phases_completed']),
            'total_agents': len(self.agents),
            'discussion_summary': self.discussion_manager.get_discussion_summary()
        }
    
    def export_simulation_results(self, output_path: str = None) -> str:
        """Export simulation results to file."""
        
        if not self.current_simulation:
            raise ValueError("No simulation to export")
        
        output_path = output_path or f"simulation_results_{self.current_simulation['id']}.json"
        
        export_data = {
            'simulation': self.current_simulation,
            'event_log': self.event_log,
            'agent_summaries': {name: agent.get_agent_summary() for name, agent in self.agents.items()},
            'discussion_data': self.discussion_manager.current_discussion,
            'export_timestamp': datetime.now().isoformat()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return output_path
