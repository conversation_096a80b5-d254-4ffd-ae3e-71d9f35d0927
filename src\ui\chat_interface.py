"""
微信风格的聊天界面组件
"""

import streamlit as st
import time
from datetime import datetime
from typing import List, Dict, Any, Optional


def render_chat_message(message: Dict[str, Any], agents: Dict[str, Any]):
    """渲染单条聊天消息 - 微信风格"""
    
    speaker = message["speaker"]
    content = message["content"]
    timestamp = message["timestamp"]
    
    # 获取发言者信息
    if speaker in agents:
        agent_info = agents[speaker]
        display_name = agent_info["name"]
        avatar_emoji = get_agent_avatar(speaker)
        is_user = False
    elif speaker.startswith("user_"):
        display_name = speaker.replace("user_", "").replace("_", " ")
        avatar_emoji = "👤"
        is_user = True
    elif speaker == "user":
        display_name = "您"
        avatar_emoji = "👤"
        is_user = True
    else:
        display_name = speaker
        avatar_emoji = "🤖"
        is_user = False
    
    # 创建消息容器
    if is_user:
        # 用户消息 - 右对齐，蓝色气泡
        st.markdown(f"""
        <div style="display: flex; justify-content: flex-end; margin: 10px 0;">
            <div style="max-width: 70%; background: #007AFF; color: white; padding: 12px 16px; 
                        border-radius: 18px 18px 4px 18px; margin-right: 10px;">
                <div style="font-size: 14px; line-height: 1.4;">{content}</div>
                <div style="font-size: 11px; opacity: 0.8; margin-top: 4px; text-align: right;">{timestamp}</div>
            </div>
            <div style="width: 40px; height: 40px; background: #007AFF; border-radius: 50%; 
                        display: flex; align-items: center; justify-content: center; font-size: 20px;">
                {avatar_emoji}
            </div>
        </div>
        """, unsafe_allow_html=True)
    else:
        # AI消息 - 左对齐，灰色气泡
        st.markdown(f"""
        <div style="display: flex; justify-content: flex-start; margin: 10px 0;">
            <div style="width: 40px; height: 40px; background: #E5E5EA; border-radius: 50%; 
                        display: flex; align-items: center; justify-content: center; font-size: 20px; margin-right: 10px;">
                {avatar_emoji}
            </div>
            <div style="max-width: 70%;">
                <div style="font-size: 12px; color: #666; margin-bottom: 4px; font-weight: 500;">
                    {display_name}
                </div>
                <div style="background: #E5E5EA; color: #000; padding: 12px 16px; 
                            border-radius: 18px 18px 18px 4px;">
                    <div style="font-size: 14px; line-height: 1.4;">{content}</div>
                    <div style="font-size: 11px; color: #666; margin-top: 4px;">{timestamp}</div>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)


def get_agent_avatar(agent_id: str) -> str:
    """获取智能体头像emoji"""
    
    avatar_map = {
        "trump": "🇺🇸",
        "biden": "🏛️", 
        "moderator": "🎯",
        "military_expert": "🎖️",
        "political_expert": "🎓",
        "economic_expert": "💼",
        "ai_expert": "🤖"
    }
    
    return avatar_map.get(agent_id, "🤖")


def render_typing_indicator(agent_name: str, show_thinking: bool = False):
    """渲染正在输入指示器"""
    
    if show_thinking:
        indicator_text = f"{agent_name} 正在思考..."
    else:
        indicator_text = f"{agent_name} 正在输入..."
    
    st.markdown(f"""
    <div style="display: flex; justify-content: flex-start; margin: 10px 0;">
        <div style="width: 40px; height: 40px; background: #E5E5EA; border-radius: 50%; 
                    display: flex; align-items: center; justify-content: center; font-size: 20px; margin-right: 10px;">
            🤖
        </div>
        <div style="background: #E5E5EA; color: #666; padding: 8px 16px; 
                    border-radius: 18px; font-size: 13px; font-style: italic;">
            {indicator_text}
            <span class="typing-dots">
                <span>.</span><span>.</span><span>.</span>
            </span>
        </div>
    </div>
    
    <style>
    .typing-dots span {{
        animation: typing 1.4s infinite;
        animation-fill-mode: both;
    }}
    .typing-dots span:nth-child(2) {{
        animation-delay: 0.2s;
    }}
    .typing-dots span:nth-child(3) {{
        animation-delay: 0.4s;
    }}
    @keyframes typing {{
        0%, 60%, 100% {{
            opacity: 0.3;
        }}
        30% {{
            opacity: 1;
        }}
    }}
    </style>
    """, unsafe_allow_html=True)


def stream_message_display(content: str, agent_name: str, agent_id: str, 
                          show_thinking: bool = False, typing_speed: float = 0.05):
    """流式显示消息内容"""
    
    avatar_emoji = get_agent_avatar(agent_id)
    
    # 显示正在输入指示器
    typing_placeholder = st.empty()
    if show_thinking:
        typing_placeholder.markdown(f"""
        <div style="display: flex; justify-content: flex-start; margin: 10px 0;">
            <div style="width: 40px; height: 40px; background: #E5E5EA; border-radius: 50%; 
                        display: flex; align-items: center; justify-content: center; font-size: 20px; margin-right: 10px;">
                {avatar_emoji}
            </div>
            <div style="background: #E5E5EA; color: #666; padding: 8px 16px; 
                        border-radius: 18px; font-size: 13px; font-style: italic;">
                {agent_name} 正在思考...
            </div>
        </div>
        """, unsafe_allow_html=True)
        time.sleep(1)  # 思考时间
    
    # 创建消息占位符
    message_placeholder = st.empty()
    displayed_content = ""
    
    # 按字符流式显示
    for char in content:
        displayed_content += char
        
        # 更新显示
        message_placeholder.markdown(f"""
        <div style="display: flex; justify-content: flex-start; margin: 10px 0;">
            <div style="width: 40px; height: 40px; background: #E5E5EA; border-radius: 50%; 
                        display: flex; align-items: center; justify-content: center; font-size: 20px; margin-right: 10px;">
                {avatar_emoji}
            </div>
            <div style="max-width: 70%;">
                <div style="font-size: 12px; color: #666; margin-bottom: 4px; font-weight: 500;">
                    {agent_name}
                </div>
                <div style="background: #E5E5EA; color: #000; padding: 12px 16px; 
                            border-radius: 18px 18px 18px 4px;">
                    <div style="font-size: 14px; line-height: 1.4;">{displayed_content}<span style="animation: blink 1s infinite;">|</span></div>
                </div>
            </div>
        </div>
        
        <style>
        @keyframes blink {{
            0%, 50% {{ opacity: 1; }}
            51%, 100% {{ opacity: 0; }}
        }}
        </style>
        """, unsafe_allow_html=True)
        
        time.sleep(typing_speed)
    
    # 清除思考指示器
    typing_placeholder.empty()
    
    # 显示最终消息
    timestamp = datetime.now().strftime("%H:%M:%S")
    message_placeholder.markdown(f"""
    <div style="display: flex; justify-content: flex-start; margin: 10px 0;">
        <div style="width: 40px; height: 40px; background: #E5E5EA; border-radius: 50%; 
                    display: flex; align-items: center; justify-content: center; font-size: 20px; margin-right: 10px;">
            {avatar_emoji}
        </div>
        <div style="max-width: 70%;">
            <div style="font-size: 12px; color: #666; margin-bottom: 4px; font-weight: 500;">
                {agent_name}
            </div>
            <div style="background: #E5E5EA; color: #000; padding: 12px 16px; 
                        border-radius: 18px 18px 18px 4px;">
                <div style="font-size: 14px; line-height: 1.4;">{displayed_content}</div>
                <div style="font-size: 11px; color: #666; margin-top: 4px;">{timestamp}</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    return displayed_content


def render_user_input_area(agents: List[str], current_speaker: Optional[str] = None):
    """渲染用户输入区域"""
    
    st.markdown("""
    <div style="border-top: 1px solid #E5E5EA; padding: 16px 0; margin-top: 20px;">
    </div>
    """, unsafe_allow_html=True)
    
    if current_speaker and current_speaker.startswith("user_"):
        # 特定用户智能体的输入
        agent_name = current_speaker.replace("user_", "").replace("_", " ")
        st.markdown(f"### 💬 {agent_name} 的发言")
        
        user_input = st.text_area(
            f"请以 {agent_name} 的身份发言:",
            placeholder=f"输入 {agent_name} 的观点和建议...",
            height=100,
            key=f"input_{current_speaker}"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            send_btn = st.button("📤 发送", type="primary", use_container_width=True)
        with col2:
            ai_assist_btn = st.button("🤖 AI代为发言", use_container_width=True)
        
        return user_input, send_btn, ai_assist_btn
    
    else:
        # 普通用户输入
        st.markdown("### 💬 参与讨论")
        
        user_input = st.text_area(
            "输入您的观点:",
            placeholder="请输入您的分析和建议...",
            height=80,
            key="general_user_input"
        )
        
        send_btn = st.button("📤 发送消息", type="primary", use_container_width=True)
        
        return user_input, send_btn, None


def render_chat_controls(show_thinking: bool, enable_stream: bool, auto_mode: bool):
    """渲染聊天控制面板"""
    
    st.markdown("### ⚙️ 聊天设置")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        new_show_thinking = st.checkbox("🧠 显示思考过程", value=show_thinking)
    
    with col2:
        new_enable_stream = st.checkbox("🌊 流式输出", value=enable_stream)
    
    with col3:
        new_auto_mode = st.checkbox("🔄 自动模式", value=auto_mode)
    
    return new_show_thinking, new_enable_stream, new_auto_mode
