"""
CLI Interface

Command-line interface for the strategic simulation system.
Provides a text-based interface for running simulations and managing the system.
"""

import click
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.simulation.simulation_controller import SimulationController
from src.agents.custom_agent import CustomAgent


class CLIInterface:
    """
    Command-line interface for strategic simulations.
    
    Features:
    - Simulation setup and execution
    - Agent management
    - Configuration management
    - Results viewing and export
    """
    
    def __init__(self):
        """Initialize CLI interface."""
        
        self.controller = None
        self.config_dir = Path("config")
        self.data_dir = Path("data")
        
        # Ensure directories exist
        self.config_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
    
    def initialize_system(self, config_path: str = None, agents_config_path: str = None):
        """Initialize the simulation system."""
        
        config_path = config_path or "config/simulation_config.json"
        agents_config_path = agents_config_path or "config/agents_config.json"
        
        try:
            # Initialize simulation controller
            self.controller = SimulationController(config_path)
            
            # Load agent configurations
            if os.path.exists(agents_config_path):
                with open(agents_config_path, 'r', encoding='utf-8') as f:
                    agents_config = json.load(f)
                
                # Initialize agents
                result = self.controller.initialize_agents(agents_config, "config/knowledge_base")
                
                click.echo(f"✅ System initialized successfully!")
                click.echo(f"📊 Loaded {result['total_count']} agents:")
                for agent_name in result['initialized_agents']:
                    click.echo(f"  • {agent_name}")
                
                return True
            else:
                click.echo(f"❌ Agent configuration file not found: {agents_config_path}")
                return False
        
        except Exception as e:
            click.echo(f"❌ Failed to initialize system: {str(e)}")
            return False
    
    def create_scenario_interactive(self) -> Dict[str, Any]:
        """Create a scenario interactively."""
        
        click.echo("\n🎯 Create New Scenario")
        click.echo("=" * 50)
        
        title = click.prompt("Scenario Title")
        description = click.prompt("Scenario Description")
        initial_context = click.prompt("Initial Context/Situation")
        
        click.echo("\nKey Considerations (press Enter to finish):")
        considerations = []
        i = 1
        while True:
            consideration = click.prompt(f"Consideration {i}", default="", show_default=False)
            if not consideration:
                break
            considerations.append(consideration)
            i += 1
        
        scenario = {
            "title": title,
            "description": description,
            "initial_context": initial_context,
            "key_considerations": considerations
        }
        
        return scenario
    
    def select_agents_interactive(self) -> Tuple[List[str], Dict[str, Any]]:
        """Select agents and configure turn order interactively."""

        if not self.controller:
            click.echo("❌ System not initialized")
            return [], {}

        available_agents = list(self.controller.agents.keys())

        click.echo("\n🤖 Available Agents:")
        for i, agent_name in enumerate(available_agents, 1):
            agent = self.controller.agents[agent_name]
            click.echo(f"  {i}. {agent_name} - {agent.role}")

        click.echo("\nSelect agents (comma-separated numbers, or 'all' for all agents):")
        selection = click.prompt("Selection")

        if selection.lower() == 'all':
            selected_agents = available_agents
        else:
            try:
                indices = [int(x.strip()) - 1 for x in selection.split(',')]
                selected_agents = [available_agents[i] for i in indices if 0 <= i < len(available_agents)]
            except (ValueError, IndexError):
                click.echo("❌ Invalid selection")
                return [], {}

        if not selected_agents:
            click.echo("❌ No agents selected")
            return [], {}

        # Configure turn order
        click.echo(f"\n📋 Selected {len(selected_agents)} agents: {', '.join(selected_agents)}")

        turn_order_config = self.configure_turn_order_interactive(selected_agents)

        return selected_agents, turn_order_config

    def configure_turn_order_interactive(self, selected_agents: List[str]) -> Dict[str, Any]:
        """Configure turn order interactively."""

        click.echo("\n⚙️ Turn Order Configuration")
        click.echo("=" * 40)

        # Turn order mode
        modes = ["Round Robin", "Moderator Controlled", "Free Discussion", "Custom Order"]
        click.echo("Turn order modes:")
        for i, mode in enumerate(modes, 1):
            click.echo(f"  {i}. {mode}")

        mode_choice = click.prompt("Select mode (1-4)", type=int, default=1)
        if 1 <= mode_choice <= len(modes):
            turn_order_mode = modes[mode_choice - 1]
        else:
            turn_order_mode = "Round Robin"

        custom_order = None
        if turn_order_mode == "Custom Order":
            click.echo("\n📝 Configure Custom Speaking Order:")
            custom_order = []
            remaining_agents = selected_agents.copy()

            for i in range(len(selected_agents)):
                if remaining_agents:
                    click.echo(f"\nRemaining agents: {', '.join(remaining_agents)}")
                    agent_choice = click.prompt(f"Position {i+1}")

                    if agent_choice in remaining_agents:
                        custom_order.append(agent_choice)
                        remaining_agents.remove(agent_choice)
                    else:
                        click.echo(f"⚠️ '{agent_choice}' not in remaining agents, skipping")

            click.echo(f"\n📋 Final speaking order: {' → '.join(custom_order)}")

        # Advanced settings
        if click.confirm("\nConfigure advanced discussion settings?", default=False):
            enforce_order = click.confirm("Enforce turn order?", default=True)
            allow_interruptions = click.confirm("Allow interruptions?", default=False)
            max_consecutive = click.prompt("Max consecutive turns per agent", type=int, default=1)
            turn_time_limit = click.prompt("Turn time limit (seconds)", type=int, default=120)
        else:
            enforce_order = True
            allow_interruptions = False
            max_consecutive = 1
            turn_time_limit = 120

        return {
            "mode": turn_order_mode,
            "custom_order": custom_order,
            "settings": {
                "enforce_turn_order": enforce_order,
                "allow_interruptions": allow_interruptions,
                "max_consecutive_turns": max_consecutive,
                "turn_time_limit": turn_time_limit,
                "discussion_timeout": 1800
            }
        }
    
    def configure_simulation_interactive(self) -> Dict[str, Any]:
        """Configure simulation settings interactively."""
        
        click.echo("\n⚙️ Simulation Configuration")
        click.echo("=" * 50)
        
        max_rounds = click.prompt("Maximum Discussion Rounds", default=8, type=int)
        round_time_limit = click.prompt("Round Time Limit (minutes)", default=10, type=int)
        enable_user_participation = click.confirm("Enable User Participation?", default=True)
        auto_save = click.confirm("Auto-save Results?", default=True)
        
        config = {
            "max_discussion_rounds": max_rounds,
            "round_time_limit": round_time_limit * 60,  # Convert to seconds
            "enable_user_participation": enable_user_participation,
            "auto_save_conversations": auto_save
        }
        
        return config
    
    def run_simulation_interactive(self):
        """Run a simulation interactively."""
        
        if not self.controller:
            click.echo("❌ System not initialized. Please run 'init' command first.")
            return
        
        # Create or select scenario
        click.echo("\n📋 Scenario Setup")
        use_predefined = click.confirm("Use predefined scenario?", default=False)
        
        if use_predefined:
            predefined_scenarios = {
                "1": {
                    "title": "Taiwan Strait Crisis Response",
                    "description": "Strategic response to escalating tensions in Taiwan Strait",
                    "initial_context": "Intelligence reports indicate increased military activity around Taiwan. How should the US respond?",
                    "key_considerations": ["Military options", "Economic implications", "Alliance coordination", "Escalation risks"]
                },
                "2": {
                    "title": "US-China Trade War Escalation",
                    "description": "Response to new Chinese trade restrictions",
                    "initial_context": "China has imposed new tariffs on US agricultural products. What should be the US response?",
                    "key_considerations": ["Economic impact", "Farmer support", "WTO implications", "Negotiation strategy"]
                }
            }
            
            click.echo("\nPredefined Scenarios:")
            for key, scenario in predefined_scenarios.items():
                click.echo(f"  {key}. {scenario['title']}")
            
            choice = click.prompt("Select scenario", type=click.Choice(list(predefined_scenarios.keys())))
            scenario = predefined_scenarios[choice]
        else:
            scenario = self.create_scenario_interactive()
        
        # Select agents and configure turn order
        selected_agents, turn_order_config = self.select_agents_interactive()
        if not selected_agents:
            click.echo("❌ No agents selected. Aborting simulation.")
            return
        
        # Configure simulation
        simulation_config = self.configure_simulation_interactive()
        
        # Confirm and start
        click.echo("\n📊 Simulation Summary")
        click.echo("=" * 50)
        click.echo(f"Scenario: {scenario['title']}")
        click.echo(f"Agents: {', '.join(selected_agents)}")
        click.echo(f"Turn Order: {turn_order_config.get('mode', 'Round Robin')}")
        if turn_order_config.get('custom_order'):
            click.echo(f"Speaking Order: {' → '.join(turn_order_config['custom_order'])}")
        click.echo(f"Max Rounds: {simulation_config['max_discussion_rounds']}")
        click.echo(f"User Participation: {'Yes' if simulation_config['enable_user_participation'] else 'No'}")

        if not click.confirm("\nStart simulation?"):
            click.echo("❌ Simulation cancelled.")
            return
        
        # Run simulation
        try:
            click.echo("\n🚀 Starting simulation...")
            
            with click.progressbar(length=100, label="Running simulation") as bar:
                # This is a simplified progress simulation
                # In reality, you'd integrate with the actual simulation progress
                import time
                for i in range(100):
                    time.sleep(0.1)  # Simulate work
                    bar.update(1)
            
            # Actually run the simulation with dynamic configuration
            simulation_result = self.controller.run_simulation(
                scenario=scenario,
                simulation_config=simulation_config,
                selected_agents=selected_agents,
                turn_order_config=turn_order_config
            )
            
            click.echo("✅ Simulation completed successfully!")
            
            # Show results summary
            self.show_simulation_results(simulation_result)
            
            # Save results
            output_path = self.controller.export_simulation_results()
            click.echo(f"📄 Results saved to: {output_path}")
            
        except Exception as e:
            click.echo(f"❌ Simulation failed: {str(e)}")
    
    def show_simulation_results(self, simulation_result: Dict[str, Any]):
        """Show simulation results summary."""
        
        click.echo("\n📊 Simulation Results")
        click.echo("=" * 50)
        
        # Basic info
        click.echo(f"Scenario: {simulation_result['scenario']['title']}")
        click.echo(f"Status: {simulation_result.get('status', 'Unknown')}")
        click.echo(f"Duration: {self._calculate_duration(simulation_result)}")
        click.echo(f"Phases Completed: {len(simulation_result.get('phases_completed', []))}")
        
        # Decision summary
        if 'final_decision' in simulation_result:
            decision = simulation_result['final_decision']
            click.echo(f"\n🎯 Final Decision by {decision.get('decision_maker', 'Unknown')}:")
            decision_text = decision.get('decision', '')
            # Show first 200 characters
            if len(decision_text) > 200:
                click.echo(f"  {decision_text[:200]}...")
            else:
                click.echo(f"  {decision_text}")
        
        # Analysis scores
        if 'analysis_results' in simulation_result:
            analysis = simulation_result['analysis_results']
            if 'overall_scores' in analysis:
                scores = analysis['overall_scores']
                click.echo(f"\n📈 Analysis Scores:")
                click.echo(f"  Decision Quality: {scores.get('decision_quality', 0):.1f}/10")
                click.echo(f"  Process Effectiveness: {scores.get('process_effectiveness', 0):.1f}/10")
                click.echo(f"  Overall Score: {scores.get('overall_simulation', 0):.1f}/10")
    
    def _calculate_duration(self, simulation_result: Dict[str, Any]) -> str:
        """Calculate simulation duration."""
        
        try:
            start_time = datetime.fromisoformat(simulation_result['start_time'])
            end_time = datetime.fromisoformat(simulation_result.get('end_time', datetime.now().isoformat()))
            duration = end_time - start_time
            
            hours = duration.seconds // 3600
            minutes = (duration.seconds % 3600) // 60
            
            if hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"
        except:
            return "Unknown"
    
    def list_simulations(self):
        """List previous simulations."""
        
        results_dir = Path("data/results")
        if not results_dir.exists():
            click.echo("No simulation results found.")
            return
        
        result_files = list(results_dir.glob("simulation_results_*.json"))
        
        if not result_files:
            click.echo("No simulation results found.")
            return
        
        click.echo("\n📋 Previous Simulations")
        click.echo("=" * 50)
        
        for i, result_file in enumerate(result_files, 1):
            try:
                with open(result_file, 'r', encoding='utf-8') as f:
                    result = json.load(f)
                
                scenario_title = result.get('scenario', {}).get('title', 'Unknown Scenario')
                start_time = result.get('start_time', 'Unknown Time')
                status = result.get('status', 'Unknown')
                
                click.echo(f"  {i}. {scenario_title}")
                click.echo(f"     Time: {start_time}")
                click.echo(f"     Status: {status}")
                click.echo(f"     File: {result_file.name}")
                click.echo()
                
            except Exception as e:
                click.echo(f"  Error reading {result_file.name}: {e}")
    
    def show_agent_info(self, agent_name: str = None):
        """Show information about agents."""
        
        if not self.controller:
            click.echo("❌ System not initialized")
            return
        
        if agent_name:
            # Show specific agent
            if agent_name in self.controller.agents:
                agent = self.controller.agents[agent_name]
                click.echo(f"\n🤖 Agent: {agent_name}")
                click.echo("=" * 50)
                click.echo(f"Type: {agent.agent_type}")
                click.echo(f"Role: {agent.role}")
                
                if hasattr(agent, 'get_agent_summary'):
                    summary = agent.get_agent_summary()
                    click.echo(f"Expertise: {', '.join(summary.get('expertise', []))}")
                    click.echo(f"Contributions: {summary.get('total_contributions', 0)}")
                    click.echo(f"Decisions Made: {summary.get('decisions_made', 0)}")
                    click.echo(f"Has Knowledge Base: {'Yes' if summary.get('has_knowledge_base') else 'No'}")
            else:
                click.echo(f"❌ Agent '{agent_name}' not found")
        else:
            # Show all agents
            click.echo("\n🤖 Available Agents")
            click.echo("=" * 50)
            
            for agent_name, agent in self.controller.agents.items():
                click.echo(f"• {agent_name} - {agent.role}")
                click.echo(f"  Type: {agent.agent_type}")
                
                if hasattr(agent, 'get_agent_summary'):
                    summary = agent.get_agent_summary()
                    expertise = summary.get('expertise', [])
                    if expertise:
                        click.echo(f"  Expertise: {', '.join(expertise[:3])}{'...' if len(expertise) > 3 else ''}")
                click.echo()
    
    def create_custom_agent_interactive(self):
        """Create a custom agent interactively."""
        
        click.echo("\n👤 Create Custom Agent")
        click.echo("=" * 50)
        
        name = click.prompt("Agent Name")
        role = click.prompt("Agent Role")
        background = click.prompt("Background/Experience")
        
        click.echo("\nExpertise areas (press Enter to finish):")
        expertise = []
        i = 1
        while True:
            area = click.prompt(f"Expertise {i}", default="", show_default=False)
            if not area:
                break
            expertise.append(area)
            i += 1
        
        click.echo("\nCore values (press Enter to finish):")
        values = []
        i = 1
        while True:
            value = click.prompt(f"Value {i}", default="", show_default=False)
            if not value:
                break
            values.append(value)
            i += 1
        
        communication_style = click.prompt("Communication Style", default="Professional and analytical")
        
        # Create agent configuration
        custom_config = {
            "name": name,
            "role": role,
            "type": "custom",
            "persona": {
                "background_summary": background,
                "expertise": expertise,
                "values": values,
                "communication_style": communication_style
            }
        }
        
        # Add to controller if available
        if self.controller:
            success = self.controller.add_custom_agent(name, custom_config)
            if success:
                click.echo(f"✅ Custom agent '{name}' created and added to system!")
            else:
                click.echo(f"❌ Failed to add custom agent to system")
        
        # Save to file
        custom_agents_file = self.config_dir / "custom_agents.json"
        
        if custom_agents_file.exists():
            with open(custom_agents_file, 'r', encoding='utf-8') as f:
                custom_agents = json.load(f)
        else:
            custom_agents = []
        
        custom_agents.append(custom_config)
        
        with open(custom_agents_file, 'w', encoding='utf-8') as f:
            json.dump(custom_agents, f, indent=2, ensure_ascii=False)
        
        click.echo(f"💾 Custom agent saved to {custom_agents_file}")

    def create_agent_from_template_cli(self, template_name: str):
        """Create agent from predefined template via CLI."""

        template_path = Path(f"config/agent_templates/{template_name}_template.json")

        try:
            if not template_path.exists():
                click.echo(f"❌ Template not found: {template_path}")
                click.echo("Available templates: biden, business, academic")
                return

            with open(template_path, 'r', encoding='utf-8') as f:
                template_config = json.load(f)

            click.echo(f"\n🎯 Creating agent from {template_name} template")
            click.echo("=" * 50)

            # Show template details
            click.echo(f"Name: {template_config['name']}")
            click.echo(f"Role: {template_config['role']}")

            persona = template_config.get('persona', {})
            if 'expertise' in persona:
                click.echo(f"Expertise: {', '.join(persona['expertise'][:5])}")

            if 'current_position' in persona:
                click.echo(f"Position: {persona['current_position']}")

            # Confirm creation
            if not click.confirm(f"\nCreate {template_config['name']} agent?"):
                click.echo("❌ Agent creation cancelled")
                return

            # Add to controller if available
            if self.controller:
                success = self.controller.add_custom_agent(template_config['name'], template_config)
                if success:
                    click.echo(f"✅ {template_config['name']} added to active simulation!")
                else:
                    click.echo(f"❌ Failed to add {template_config['name']} to simulation")

            # Save to file
            custom_agents_file = self.config_dir / "custom_agents.json"

            if custom_agents_file.exists():
                with open(custom_agents_file, 'r', encoding='utf-8') as f:
                    custom_agents = json.load(f)
            else:
                custom_agents = []

            custom_agents.append(template_config)

            with open(custom_agents_file, 'w', encoding='utf-8') as f:
                json.dump(custom_agents, f, indent=2, ensure_ascii=False)

            click.echo(f"✅ Successfully created {template_config['name']} from template!")
            click.echo(f"💾 Configuration saved to {custom_agents_file}")

            # Show knowledge base info
            if persona.get('has_dedicated_kb'):
                kb_file = f"config/knowledge_base/{template_config['name'].lower().replace(' ', '_')}_knowledge.txt"
                if Path(kb_file).exists():
                    click.echo(f"📚 Knowledge base available: {kb_file}")
                else:
                    click.echo(f"⚠️ Knowledge base not found: {kb_file}")

        except Exception as e:
            click.echo(f"❌ Error creating agent from template: {e}")


# CLI Commands using Click
@click.group()
@click.pass_context
def cli(ctx):
    """Strategic Simulation System CLI"""
    ctx.ensure_object(dict)
    ctx.obj['interface'] = CLIInterface()


@cli.command()
@click.option('--config', '-c', help='Configuration file path')
@click.option('--agents', '-a', help='Agents configuration file path')
@click.pass_context
def init(ctx, config, agents):
    """Initialize the simulation system"""
    interface = ctx.obj['interface']
    success = interface.initialize_system(config, agents)
    if not success:
        ctx.exit(1)


@cli.command()
@click.pass_context
def run(ctx):
    """Run a new simulation interactively"""
    interface = ctx.obj['interface']
    interface.run_simulation_interactive()


@cli.command()
@click.pass_context
def list(ctx):
    """List previous simulations"""
    interface = ctx.obj['interface']
    interface.list_simulations()


@cli.command()
@click.option('--agent', '-a', help='Specific agent name to show')
@click.pass_context
def agents(ctx, agent):
    """Show information about agents"""
    interface = ctx.obj['interface']
    interface.show_agent_info(agent)


@cli.command()
@click.option('--template', '-t', help='Use predefined template (biden, business, academic)')
@click.pass_context
def create_agent(ctx, template):
    """Create a custom agent interactively"""
    interface = ctx.obj['interface']

    if template:
        interface.create_agent_from_template_cli(template)
    else:
        interface.create_custom_agent_interactive()


@cli.command()
@click.option('--agent', '-a', help='Target agent name')
@click.option('--directory', '-d', help='Directory containing documents')
@click.option('--files', '-f', help='Comma-separated list of files')
@click.option('--patterns', '-p', default='*.txt,*.md', help='File patterns to match')
@click.option('--mode', type=click.Choice(['append', 'replace', 'prepend']),
              default='append', help='How to merge with existing knowledge')
@click.pass_context
def upload_knowledge(ctx, agent, directory, files, patterns, mode):
    """Upload documents to agent knowledge base"""
    interface = ctx.obj['interface']

    if not interface.controller:
        click.echo("❌ System not initialized. Please run 'init' command first.")
        return

    # Find documents
    documents = []

    if files:
        documents = [f.strip() for f in files.split(',')]
    elif directory:
        import glob
        pattern_list = [p.strip() for p in patterns.split(',')]
        for pattern in pattern_list:
            documents.extend(glob.glob(f"{directory}/**/{pattern}", recursive=True))
    else:
        click.echo("❌ Must specify either --directory or --files")
        return

    if not documents:
        click.echo("❌ No documents found")
        return

    click.echo(f"📄 Found {len(documents)} documents:")
    for doc in documents:
        click.echo(f"  • {doc}")

    if not click.confirm("Upload these documents?"):
        click.echo("❌ Upload cancelled")
        return

    # Process upload
    try:
        # Read and combine all documents
        combined_content = []

        for doc_path in documents:
            try:
                with open(doc_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                header = f"\n\n## From {doc_path}\n\nUploaded: {datetime.now().isoformat()}\n\n"
                combined_content.append(header + content)

                click.echo(f"✅ Processed: {doc_path}")

            except Exception as e:
                click.echo(f"❌ Error reading {doc_path}: {e}")

        if combined_content:
            full_content = "\n".join(combined_content)

            # In a real implementation, this would update the knowledge base
            click.echo(f"✅ Successfully processed {len(combined_content)} documents")
            click.echo(f"📊 Total content: {len(full_content)} characters")
            click.echo(f"🎯 Target agent: {agent}")
            click.echo(f"🔄 Merge mode: {mode}")

            # TODO: Implement actual knowledge base update
            # knowledge_manager.update_agent_knowledge(agent, full_content, mode)
        else:
            click.echo("❌ No content to upload")

    except Exception as e:
        click.echo(f"❌ Upload failed: {e}")


@cli.command()
@click.argument('simulation_file')
@click.pass_context
def show(ctx, simulation_file):
    """Show detailed results of a simulation"""
    try:
        with open(simulation_file, 'r', encoding='utf-8') as f:
            simulation_result = json.load(f)
        
        interface = ctx.obj['interface']
        interface.show_simulation_results(simulation_result)
        
    except FileNotFoundError:
        click.echo(f"❌ File not found: {simulation_file}")
    except json.JSONDecodeError:
        click.echo(f"❌ Invalid JSON file: {simulation_file}")
    except Exception as e:
        click.echo(f"❌ Error reading file: {e}")


if __name__ == '__main__':
    cli()
