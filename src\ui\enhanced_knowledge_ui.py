#!/usr/bin/env python3
"""
Enhanced Knowledge Management UI

支持大规模文档处理、embedding和智能检索的知识库管理界面。
"""

import streamlit as st
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

from ..knowledge.enhanced_knowledge_manager import EnhancedKnowledgeManager


class EnhancedKnowledgeUI:
    """
    增强的知识库管理界面
    
    特性:
    - 批量文档上传和处理
    - 实时处理进度显示
    - 智能文档去重检测
    - 向量检索和相似性分析
    - 知识库统计和可视化
    - 文档版本管理
    """
    
    def __init__(self):
        """初始化界面"""
        
        # 初始化知识库管理器
        if 'enhanced_knowledge_manager' not in st.session_state:
            st.session_state.enhanced_knowledge_manager = EnhancedKnowledgeManager()
        
        self.km = st.session_state.enhanced_knowledge_manager
    
    def show_main_page(self):
        """显示主页面"""
        
        st.title("🧠 增强知识库管理系统")
        st.markdown("### 支持大规模文档处理、embedding和智能检索")
        
        # 侧边栏导航
        page = st.sidebar.selectbox(
            "功能导航",
            ["📊 概览", "📤 文档上传", "🔍 知识检索", "📈 统计分析", "⚙️ 系统设置"]
        )
        
        if page == "📊 概览":
            self.show_overview_page()
        elif page == "📤 文档上传":
            self.show_upload_page()
        elif page == "🔍 知识检索":
            self.show_search_page()
        elif page == "📈 统计分析":
            self.show_analytics_page()
        elif page == "⚙️ 系统设置":
            self.show_settings_page()
    
    def show_overview_page(self):
        """显示概览页面"""
        
        st.header("📊 知识库概览")
        
        # 获取统计信息
        stats = self.km.get_knowledge_stats()
        
        # 显示总体统计
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("📄 总文档数", stats['total_documents'])
        
        with col2:
            st.metric("🧩 总块数", stats['total_chunks'])
        
        with col3:
            st.metric("🔢 总Embedding数", stats['total_embeddings'])
        
        with col4:
            agent_count = len(stats['agents'])
            st.metric("🤖 智能体数", agent_count)
        
        # 显示智能体详细统计
        st.subheader("🤖 智能体知识库详情")
        
        if stats['agents']:
            # 创建数据表格
            agent_data = []
            for agent_name, agent_stats in stats['agents'].items():
                agent_data.append({
                    '智能体': agent_name,
                    '文档数': agent_stats['documents'],
                    '块数': agent_stats['chunks'],
                    'Embedding数': agent_stats['embeddings'],
                    '文件类型': len(agent_stats['file_types']),
                    '标签数': len(agent_stats['tags'])
                })
            
            df = pd.DataFrame(agent_data)
            st.dataframe(df, use_container_width=True)
            
            # 可视化图表
            col1, col2 = st.columns(2)
            
            with col1:
                # 文档分布饼图
                fig_docs = px.pie(
                    df, 
                    values='文档数', 
                    names='智能体',
                    title='文档分布'
                )
                st.plotly_chart(fig_docs, use_container_width=True)
            
            with col2:
                # 块数分布柱状图
                fig_chunks = px.bar(
                    df,
                    x='智能体',
                    y='块数',
                    title='块数分布'
                )
                st.plotly_chart(fig_chunks, use_container_width=True)
        else:
            st.info("📝 暂无知识库数据，请先上传文档")
    
    def show_upload_page(self):
        """显示文档上传页面"""
        
        st.header("📤 文档上传与管理")
        
        # 选择智能体
        agent_names = list(self.km.get_knowledge_stats()['agents'].keys())
        if not agent_names:
            agent_names = ['trump', 'moderator', 'political_expert', 'military_expert', 'economic_expert']
        
        selected_agent = st.selectbox("选择目标智能体", agent_names)
        
        # 文件上传
        st.subheader("📁 上传文档")
        
        uploaded_files = st.file_uploader(
            "选择要上传的文档",
            type=['txt', 'md', 'pdf', 'docx', 'json', 'csv'],
            accept_multiple_files=True,
            help="支持多种文档格式，可以批量上传"
        )
        
        if uploaded_files:
            st.info(f"📋 已选择 {len(uploaded_files)} 个文件")
            
            # 显示文件信息
            file_info = []
            for file in uploaded_files:
                file_info.append({
                    '文件名': file.name,
                    '大小': f"{len(file.read()) / 1024:.1f} KB",
                    '类型': Path(file.name).suffix
                })
                file.seek(0)  # 重置文件指针
            
            st.dataframe(pd.DataFrame(file_info))
            
            # 处理选项
            st.subheader("⚙️ 处理选项")
            
            col1, col2 = st.columns(2)
            
            with col1:
                tags = st.text_input(
                    "文档标签",
                    placeholder="用逗号分隔，如：政策,经济,军事",
                    help="为文档添加标签，便于分类和检索"
                )
                tag_list = [tag.strip() for tag in tags.split(',')] if tags else []
            
            with col2:
                process_async = st.checkbox(
                    "异步处理",
                    value=True,
                    help="启用异步处理以提高性能"
                )
            
            # 开始处理
            if st.button("🚀 开始处理文档", type="primary"):
                with st.spinner("正在处理文档..."):
                    # 显示进度条
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    try:
                        # 处理文档
                        results = self.km.add_documents_to_agent(
                            selected_agent,
                            uploaded_files,
                            tags=tag_list,
                            process_async=process_async
                        )
                        
                        # 显示结果
                        st.success("✅ 文档处理完成！")
                        
                        # 结果统计
                        col1, col2, col3 = st.columns(3)
                        
                        with col1:
                            st.metric("处理文件", results['processed_files'])
                        
                        with col2:
                            st.metric("生成块数", results['total_chunks'])
                        
                        with col3:
                            st.metric("生成Embedding", results['total_embeddings'])
                        
                        # 显示处理时间
                        st.info(f"⏱️ 处理时间: {results['processing_time']:.2f} 秒")
                        
                        # 显示错误信息
                        if results['errors']:
                            st.warning("⚠️ 处理过程中出现以下错误:")
                            for error in results['errors']:
                                st.write(f"• {error}")
                    
                    except Exception as e:
                        st.error(f"❌ 处理失败: {str(e)}")
        
        # 文档管理
        st.subheader("📋 文档管理")
        
        # 显示当前智能体的文档
        agent_stats = self.km.get_knowledge_stats(selected_agent)
        if agent_stats:
            st.write(f"**{selected_agent}** 的知识库包含 {agent_stats['documents']} 个文档")
            
            # 文件类型分布
            if agent_stats['file_types']:
                fig_types = px.pie(
                    values=list(agent_stats['file_types'].values()),
                    names=list(agent_stats['file_types'].keys()),
                    title=f'{selected_agent} 文档类型分布'
                )
                st.plotly_chart(fig_types, use_container_width=True)
            
            # 标签云
            if agent_stats['tags']:
                st.write("**标签:**")
                for tag in agent_stats['tags']:
                    st.write(f"🏷️ {tag}")
            
            # 清空知识库按钮
            if st.button("🗑️ 清空知识库", type="secondary"):
                if st.session_state.get('confirm_clear', False):
                    if self.km.clear_agent_knowledge(selected_agent):
                        st.success("✅ 知识库已清空")
                        st.rerun()
                else:
                    st.session_state.confirm_clear = True
                    st.warning("⚠️ 点击确认清空知识库")
        else:
            st.info(f"📝 {selected_agent} 暂无知识库数据")
    
    def show_search_page(self):
        """显示知识检索页面"""
        
        st.header("🔍 知识检索")
        
        # 选择智能体
        agent_names = list(self.km.get_knowledge_stats()['agents'].keys())
        if not agent_names:
            st.info("📝 暂无知识库数据，请先上传文档")
            return
        
        selected_agent = st.selectbox("选择智能体", agent_names)
        
        # 搜索界面
        st.subheader("🔎 智能检索")
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            query = st.text_input(
                "输入搜索查询",
                placeholder="例如：特朗普的经济政策",
                help="输入您想要搜索的内容"
            )
        
        with col2:
            top_k = st.number_input(
                "返回结果数",
                min_value=1,
                max_value=20,
                value=5,
                help="设置返回的相关结果数量"
            )
        
        if query and st.button("🔍 开始搜索"):
            with st.spinner("正在搜索..."):
                try:
                    results = self.km.search_knowledge(selected_agent, query, top_k)
                    
                    if results:
                        st.success(f"✅ 找到 {len(results)} 个相关结果")
                        
                        # 显示搜索结果
                        for i, result in enumerate(results, 1):
                            with st.expander(f"结果 {i} (相似度: {result['score']:.3f})"):
                                st.write("**内容:**")
                                st.write(result['content'])
                                
                                if 'metadata' in result:
                                    st.write("**元数据:**")
                                    st.json(result['metadata'])
                    else:
                        st.info("📝 未找到相关结果")
                
                except Exception as e:
                    st.error(f"❌ 搜索失败: {str(e)}")
        
        # 批量搜索
        st.subheader("📋 批量搜索")
        
        batch_queries = st.text_area(
            "批量查询",
            placeholder="每行一个查询\n例如：\n特朗普的经济政策\n军事战略\n外交关系",
            help="输入多个查询，每行一个"
        )
        
        if batch_queries and st.button("🔍 批量搜索"):
            queries = [q.strip() for q in batch_queries.split('\n') if q.strip()]
            
            with st.spinner(f"正在处理 {len(queries)} 个查询..."):
                all_results = {}
                
                for query in queries:
                    try:
                        results = self.km.search_knowledge(selected_agent, query, 3)
                        all_results[query] = results
                    except Exception as e:
                        all_results[query] = [{'error': str(e)}]
                
                # 显示批量结果
                for query, results in all_results.items():
                    with st.expander(f"查询: {query}"):
                        if results and 'error' not in results[0]:
                            st.write(f"找到 {len(results)} 个结果")
                            for i, result in enumerate(results, 1):
                                st.write(f"**{i}.** {result['content'][:100]}... (相似度: {result['score']:.3f})")
                        else:
                            st.write("❌ 搜索失败")
    
    def show_analytics_page(self):
        """显示统计分析页面"""
        
        st.header("📈 统计分析")
        
        stats = self.km.get_knowledge_stats()
        
        if not stats['agents']:
            st.info("📝 暂无数据可分析")
            return
        
        # 总体趋势
        st.subheader("📊 总体趋势")
        
        # 创建时间序列数据（模拟）
        dates = pd.date_range(start='2024-01-01', end=datetime.now(), freq='D')
        trend_data = pd.DataFrame({
            '日期': dates,
            '文档数': [stats['total_documents'] * (i/len(dates)) for i in range(len(dates))],
            '块数': [stats['total_chunks'] * (i/len(dates)) for i in range(len(dates))]
        })
        
        fig_trend = px.line(
            trend_data,
            x='日期',
            y=['文档数', '块数'],
            title='知识库增长趋势'
        )
        st.plotly_chart(fig_trend, use_container_width=True)
        
        # 智能体对比
        st.subheader("🤖 智能体对比")
        
        agent_data = []
        for agent_name, agent_stats in stats['agents'].items():
            agent_data.append({
                '智能体': agent_name,
                '文档数': agent_stats['documents'],
                '块数': agent_stats['chunks'],
                'Embedding数': agent_stats['embeddings']
            })
        
        df = pd.DataFrame(agent_data)
        
        # 雷达图
        fig_radar = go.Figure()
        
        for _, row in df.iterrows():
            fig_radar.add_trace(go.Scatterpolar(
                r=[row['文档数'], row['块数'], row['Embedding数']],
                theta=['文档数', '块数', 'Embedding数'],
                fill='toself',
                name=row['智能体']
            ))
        
        fig_radar.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, max(df[['文档数', '块数', 'Embedding数']].max())]
                )),
            showlegend=True,
            title="智能体知识库能力对比"
        )
        
        st.plotly_chart(fig_radar, use_container_width=True)
        
        # 文件类型分析
        st.subheader("📄 文件类型分析")
        
        all_types = {}
        for agent_stats in stats['agents'].values():
            for file_type, count in agent_stats['file_types'].items():
                all_types[file_type] = all_types.get(file_type, 0) + count
        
        if all_types:
            fig_types = px.bar(
                x=list(all_types.keys()),
                y=list(all_types.values()),
                title="文件类型分布",
                labels={'x': '文件类型', 'y': '数量'}
            )
            st.plotly_chart(fig_types, use_container_width=True)
    
    def show_settings_page(self):
        """显示系统设置页面"""
        
        st.header("⚙️ 系统设置")
        
        # 知识库管理器配置
        st.subheader("🔧 知识库配置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            chunk_size = st.number_input(
                "文档块大小",
                min_value=100,
                max_value=1000,
                value=self.km.chunk_size,
                help="设置文档分块的大小（字符数）"
            )
            
            chunk_overlap = st.number_input(
                "块重叠大小",
                min_value=0,
                max_value=200,
                value=self.km.chunk_overlap,
                help="设置相邻块之间的重叠字符数"
            )
        
        with col2:
            max_workers = st.number_input(
                "最大工作线程",
                min_value=1,
                max_value=16,
                value=self.km.max_workers,
                help="设置异步处理的最大线程数"
            )
            
            similarity_threshold = st.slider(
                "相似度阈值",
                min_value=0.0,
                max_value=1.0,
                value=self.km.similarity_threshold,
                step=0.1,
                help="设置搜索结果的最小相似度阈值"
            )
        
        # 保存配置
        if st.button("💾 保存配置"):
            self.km.chunk_size = chunk_size
            self.km.chunk_overlap = chunk_overlap
            self.km.max_workers = max_workers
            self.km.similarity_threshold = similarity_threshold
            st.success("✅ 配置已保存")
        
        # 系统状态
        st.subheader("📊 系统状态")
        
        # 检查组件状态
        status_data = {
            '组件': ['Embedding模型', 'ChromaDB', 'FAISS', '向量数据库'],
            '状态': [
                '✅ 可用' if self.km.embedding_model else '❌ 不可用',
                '✅ 可用' if CHROMADB_AVAILABLE else '❌ 不可用',
                '✅ 可用' if FAISS_AVAILABLE else '❌ 不可用',
                '✅ 可用' if self.km.vector_db else '❌ 不可用'
            ]
        }
        
        st.dataframe(pd.DataFrame(status_data), use_container_width=True)
        
        # 存储路径
        st.subheader("📁 存储路径")
        st.info(f"**知识库路径:** {self.km.base_path}")
        st.info(f"**元数据文件:** {self.km.base_path / 'metadata.json'}")
        
        # 维护操作
        st.subheader("🔧 维护操作")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🔄 重新索引"):
                with st.spinner("正在重新索引..."):
                    # 这里可以添加重新索引的逻辑
                    st.success("✅ 索引重建完成")
        
        with col2:
            if st.button("🧹 清理缓存"):
                with st.spinner("正在清理缓存..."):
                    # 这里可以添加清理缓存的逻辑
                    st.success("✅ 缓存清理完成")


def main():
    """主函数"""
    
    # 设置页面配置
    st.set_page_config(
        page_title="增强知识库管理系统",
        page_icon="🧠",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 创建并显示界面
    ui = EnhancedKnowledgeUI()
    ui.show_main_page()


if __name__ == "__main__":
    main() 