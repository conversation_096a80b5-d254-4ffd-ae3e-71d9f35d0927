"""
Web Interface

Streamlit-based web interface for the strategic simulation system.
Provides an intuitive GUI for configuring simulations, monitoring progress,
and viewing results.
"""

import streamlit as st
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.simulation.simulation_controller import SimulationController
from src.agents.custom_agent import CustomAgent


class WebInterface:
    """
    Streamlit-based web interface for strategic simulations.
    
    Features:
    - Simulation configuration and setup
    - Real-time simulation monitoring
    - Agent configuration and management
    - Results visualization and export
    - User participation in discussions
    """
    
    def __init__(self):
        """Initialize web interface."""
        
        # Initialize session state
        if 'simulation_controller' not in st.session_state:
            st.session_state.simulation_controller = None
        
        if 'current_simulation' not in st.session_state:
            st.session_state.current_simulation = None
        
        if 'custom_agents' not in st.session_state:
            st.session_state.custom_agents = []
        
        if 'simulation_history' not in st.session_state:
            st.session_state.simulation_history = []
    
    def run(self):
        """Run the web interface."""
        
        st.set_page_config(
            page_title="Strategic Simulation System",
            page_icon="🎯",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        st.title("🎯 Strategic Simulation System")
        st.markdown("*Military Strategic Simulation with AI Agents*")
        
        # Sidebar navigation
        page = st.sidebar.selectbox(
            "Navigation",
            ["Home", "Setup Simulation", "Run Simulation", "Agent Management", "Results & Analysis", "Settings"]
        )
        
        # Route to appropriate page
        if page == "Home":
            self.show_home_page()
        elif page == "Setup Simulation":
            self.show_setup_page()
        elif page == "Run Simulation":
            self.show_simulation_page()
        elif page == "Agent Management":
            self.show_agent_management_page()
        elif page == "Results & Analysis":
            self.show_results_page()
        elif page == "Settings":
            self.show_settings_page()
    
    def show_home_page(self):
        """Show the home page with system overview."""
        
        st.header("Welcome to Strategic Simulation System")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🎯 System Overview")
            st.markdown("""
            This system enables strategic military simulations with AI agents:
            
            **Key Features:**
            - 🤖 Multiple AI agents (Trump, experts, custom agents)
            - 💬 Group discussion simulation
            - 🧠 Memory management and knowledge bases
            - 📊 Decision analysis and reporting
            - 👤 User participation in discussions
            
            **Agents Available:**
            - **Donald Trump**: Decision maker with America First perspective
            - **Moderator**: Simulation director and facilitator
            - **Political Expert**: Policy and international relations analysis
            - **Military Expert**: Strategic and operational military advice
            - **Economic Expert**: Trade and economic impact analysis
            - **Custom Agents**: User-defined participants
            """)
        
        with col2:
            st.subheader("📈 System Status")
            
            # Check if simulation controller is initialized
            if st.session_state.simulation_controller:
                st.success("✅ Simulation Controller: Ready")
                
                # Show agent status
                agents = st.session_state.simulation_controller.agents
                st.info(f"🤖 Agents Loaded: {len(agents)}")
                
                for agent_name in agents.keys():
                    st.text(f"  • {agent_name}")
                
                # Show simulation status
                status = st.session_state.simulation_controller.get_simulation_status()
                if status['status'] != 'not_started':
                    st.info(f"🎮 Current Simulation: {status['status']}")
                else:
                    st.info("🎮 No active simulation")
            
            else:
                st.warning("⚠️ Simulation Controller: Not initialized")
                if st.button("Initialize System"):
                    self.initialize_system()
            
            # Quick actions
            st.subheader("🚀 Quick Actions")
            
            col_a, col_b = st.columns(2)
            with col_a:
                if st.button("🎯 New Simulation", use_container_width=True):
                    st.switch_page("Setup Simulation")
            
            with col_b:
                if st.button("👤 Manage Agents", use_container_width=True):
                    st.switch_page("Agent Management")
        
        # Recent simulations
        if st.session_state.simulation_history:
            st.subheader("📋 Recent Simulations")
            
            for sim in st.session_state.simulation_history[-5:]:
                with st.expander(f"🎯 {sim.get('scenario', {}).get('title', 'Unknown Scenario')} - {sim.get('start_time', 'Unknown Time')}"):
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Status", sim.get('status', 'Unknown'))
                    
                    with col2:
                        phases = len(sim.get('phases_completed', []))
                        st.metric("Phases Completed", phases)
                    
                    with col3:
                        if 'analysis_results' in sim:
                            overall_score = sim['analysis_results'].get('overall_scores', {}).get('overall_simulation', 0)
                            st.metric("Overall Score", f"{overall_score:.1f}/10")
    
    def show_setup_page(self):
        """Show simulation setup page."""
        
        st.header("🎯 Setup New Simulation")
        
        # Initialize system if needed
        if not st.session_state.simulation_controller:
            st.warning("⚠️ System not initialized. Please initialize first.")
            if st.button("Initialize System"):
                self.initialize_system()
            return
        
        # Scenario selection/creation
        st.subheader("📋 Scenario Configuration")
        
        # Predefined scenarios
        predefined_scenarios = {
            "Taiwan Strait Crisis": {
                "title": "Taiwan Strait Crisis Response",
                "description": "Strategic response to escalating tensions in Taiwan Strait",
                "initial_context": "Intelligence reports indicate increased military activity around Taiwan. How should the US respond?",
                "key_considerations": ["Military options", "Economic implications", "Alliance coordination", "Escalation risks"]
            },
            "Trade War Escalation": {
                "title": "US-China Trade War Escalation", 
                "description": "Response to new Chinese trade restrictions",
                "initial_context": "China has imposed new tariffs on US agricultural products. What should be the US response?",
                "key_considerations": ["Economic impact", "Farmer support", "WTO implications", "Negotiation strategy"]
            },
            "Custom Scenario": {
                "title": "",
                "description": "",
                "initial_context": "",
                "key_considerations": []
            }
        }
        
        scenario_choice = st.selectbox("Select Scenario", list(predefined_scenarios.keys()))
        
        if scenario_choice == "Custom Scenario":
            st.subheader("📝 Custom Scenario Details")
            
            scenario_title = st.text_input("Scenario Title")
            scenario_description = st.text_area("Scenario Description")
            initial_context = st.text_area("Initial Context/Situation")
            
            # Key considerations
            st.write("Key Considerations:")
            considerations = []
            for i in range(4):
                consideration = st.text_input(f"Consideration {i+1}", key=f"consideration_{i}")
                if consideration:
                    considerations.append(consideration)
            
            scenario = {
                "title": scenario_title,
                "description": scenario_description,
                "initial_context": initial_context,
                "key_considerations": considerations
            }
        else:
            scenario = predefined_scenarios[scenario_choice].copy()
            
            # Allow editing of predefined scenario
            st.subheader("📝 Scenario Details")
            scenario["title"] = st.text_input("Title", value=scenario["title"])
            scenario["description"] = st.text_area("Description", value=scenario["description"])
            scenario["initial_context"] = st.text_area("Initial Context", value=scenario["initial_context"])
            
            # Edit considerations
            st.write("Key Considerations:")
            new_considerations = []
            for i, consideration in enumerate(scenario["key_considerations"]):
                new_consideration = st.text_input(f"Consideration {i+1}", value=consideration, key=f"edit_consideration_{i}")
                if new_consideration:
                    new_considerations.append(new_consideration)
            
            # Add new considerations
            for i in range(len(scenario["key_considerations"]), 6):
                new_consideration = st.text_input(f"Consideration {i+1}", key=f"new_consideration_{i}")
                if new_consideration:
                    new_considerations.append(new_consideration)
            
            scenario["key_considerations"] = new_considerations
        
        # Agent selection and configuration
        st.subheader("🤖 Agent Selection & Configuration")

        available_agents = list(st.session_state.simulation_controller.agents.keys())

        # Add custom agents to available list
        custom_agent_names = []
        if st.session_state.custom_agents:
            custom_agent_names = [agent['name'] for agent in st.session_state.custom_agents]
            available_agents.extend(custom_agent_names)

        col1, col2 = st.columns(2)

        with col1:
            st.write("**Available Agents:**")

            # Agent selection with detailed info
            selected_agents = []

            for agent_name in available_agents:
                # Get agent info
                if agent_name in st.session_state.simulation_controller.agents:
                    agent = st.session_state.simulation_controller.agents[agent_name]
                    agent_role = agent.role
                    agent_type = agent.agent_type
                elif agent_name in custom_agent_names:
                    custom_agent = next(a for a in st.session_state.custom_agents if a['name'] == agent_name)
                    agent_role = custom_agent['role']
                    agent_type = custom_agent.get('type', 'custom')
                else:
                    agent_role = "Unknown"
                    agent_type = "unknown"

                # Checkbox with detailed info
                is_selected = st.checkbox(
                    f"**{agent_name}**",
                    key=f"select_{agent_name}",
                    help=f"Role: {agent_role}\nType: {agent_type}"
                )

                if is_selected:
                    selected_agents.append(agent_name)
                    st.caption(f"📋 {agent_role} ({agent_type})")

        with col2:
            st.write("**Discussion Order Configuration:**")

            if selected_agents:
                st.write(f"Selected {len(selected_agents)} agents")

                # Turn order configuration
                turn_order_mode = st.selectbox(
                    "Turn Order Mode",
                    ["Round Robin", "Moderator Controlled", "Free Discussion", "Custom Order"],
                    help="How agents will take turns speaking"
                )

                if turn_order_mode == "Custom Order":
                    st.write("**Drag to reorder agents:**")

                    # Custom order selection
                    ordered_agents = []
                    remaining_agents = selected_agents.copy()

                    for i in range(len(selected_agents)):
                        if remaining_agents:
                            agent_choice = st.selectbox(
                                f"Position {i+1}:",
                                remaining_agents,
                                key=f"order_{i}"
                            )
                            ordered_agents.append(agent_choice)
                            remaining_agents.remove(agent_choice)

                    selected_agents = ordered_agents

                elif turn_order_mode == "Moderator Controlled":
                    # Ensure moderator is first
                    if 'moderator' in selected_agents:
                        selected_agents.remove('moderator')
                        selected_agents.insert(0, 'moderator')

                # Show final order
                st.write("**Final Speaking Order:**")
                for i, agent in enumerate(selected_agents, 1):
                    st.write(f"{i}. {agent}")

                # Advanced discussion settings
                with st.expander("🔧 Advanced Discussion Settings"):
                    col_a, col_b = st.columns(2)

                    with col_a:
                        enforce_turn_order = st.checkbox("Enforce Turn Order", value=True)
                        allow_interruptions = st.checkbox("Allow Interruptions", value=False)
                        enable_side_conversations = st.checkbox("Enable Side Conversations", value=False)

                    with col_b:
                        max_consecutive_turns = st.slider("Max Consecutive Turns per Agent", 1, 5, 1)
                        turn_time_limit = st.slider("Turn Time Limit (seconds)", 30, 300, 120)
                        discussion_timeout = st.slider("Discussion Timeout (minutes)", 5, 60, 30)

            else:
                st.info("Select agents to configure discussion order")
        
        # Simulation settings
        st.subheader("⚙️ Simulation Settings")
        
        col1, col2 = st.columns(2)
        
        with col1:
            max_rounds = st.slider("Maximum Discussion Rounds", 3, 15, 8)
            round_time_limit = st.slider("Round Time Limit (minutes)", 3, 30, 10)
        
        with col2:
            enable_user_participation = st.checkbox("Enable User Participation", value=True)
            auto_save = st.checkbox("Auto-save Results", value=True)
        
        # Start simulation
        st.subheader("🚀 Launch Simulation")

        if st.button("Start Simulation", type="primary", use_container_width=True):
            if not scenario["title"]:
                st.error("Please provide a scenario title")
                return

            if not selected_agents:
                st.error("Please select at least one agent")
                return

            # Configure simulation
            simulation_config = {
                "max_discussion_rounds": max_rounds,
                "round_time_limit": round_time_limit * 60,  # Convert to seconds
                "enable_user_participation": enable_user_participation,
                "auto_save_conversations": auto_save
            }

            # Configure turn order
            turn_order_config = {
                "mode": turn_order_mode,
                "custom_order": selected_agents if turn_order_mode == "Custom Order" else None,
                "settings": {
                    "enforce_turn_order": enforce_turn_order,
                    "allow_interruptions": allow_interruptions,
                    "enable_side_conversations": enable_side_conversations,
                    "max_consecutive_turns": max_consecutive_turns,
                    "turn_time_limit": turn_time_limit,
                    "discussion_timeout": discussion_timeout * 60  # Convert to seconds
                }
            }

            # Store in session state
            st.session_state.current_scenario = scenario
            st.session_state.current_config = simulation_config
            st.session_state.selected_agents = selected_agents
            st.session_state.turn_order_config = turn_order_config

            st.success("✅ Simulation configured successfully!")
            st.info("👉 Go to 'Run Simulation' page to start the discussion.")

            # Show configuration summary
            with st.expander("📋 Configuration Summary"):
                st.write(f"**Scenario:** {scenario['title']}")
                st.write(f"**Selected Agents:** {', '.join(selected_agents)}")
                st.write(f"**Turn Order:** {turn_order_mode}")
                st.write(f"**Max Rounds:** {max_rounds}")
                st.write(f"**User Participation:** {'Yes' if enable_user_participation else 'No'}")

                if turn_order_mode == "Custom Order":
                    st.write("**Speaking Order:**")
                    for i, agent in enumerate(selected_agents, 1):
                        st.write(f"  {i}. {agent}")
    
    def show_simulation_page(self):
        """Show active simulation page."""
        
        st.header("🎮 Run Simulation")
        
        if not st.session_state.simulation_controller:
            st.error("❌ System not initialized")
            return
        
        if not hasattr(st.session_state, 'current_scenario'):
            st.warning("⚠️ No scenario configured. Please setup a simulation first.")
            return
        
        # Simulation status
        status = st.session_state.simulation_controller.get_simulation_status()
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Status", status.get('status', 'Not Started'))
        
        with col2:
            st.metric("Current Phase", status.get('current_phase', 'N/A'))
        
        with col3:
            st.metric("Agents", status.get('total_agents', 0))
        
        # Control buttons
        col_a, col_b, col_c = st.columns(3)
        
        with col_a:
            if st.button("▶️ Start Simulation", type="primary"):
                self.start_simulation()
        
        with col_b:
            if st.button("⏸️ Pause Simulation"):
                st.info("Pause functionality would be implemented here")
        
        with col_c:
            if st.button("🛑 Stop Simulation"):
                st.info("Stop functionality would be implemented here")
        
        # Real-time updates
        if status.get('status') != 'not_started':
            st.subheader("📊 Simulation Progress")
            
            # Progress bar
            phases_completed = status.get('discussion_summary', {}).get('phases_completed', 0)
            total_phases = 5  # Opening, Initial Positions, Discussion, Decision, Debrief
            progress = phases_completed / total_phases
            st.progress(progress)
            
            # Live discussion feed
            st.subheader("💬 Live Discussion")
            
            # This would show real-time updates from the simulation
            discussion_container = st.container()
            
            with discussion_container:
                if hasattr(st.session_state, 'simulation_messages'):
                    for message in st.session_state.simulation_messages[-10:]:  # Show last 10 messages
                        with st.chat_message(message.get('speaker', 'System')):
                            st.write(message.get('content', ''))
                            st.caption(message.get('timestamp', ''))
                else:
                    st.info("Waiting for simulation to start...")
            
            # User participation
            if st.session_state.current_config.get('enable_user_participation'):
                st.subheader("👤 Your Participation")
                
                user_input = st.text_area("Your contribution to the discussion:")
                
                col_x, col_y = st.columns(2)
                with col_x:
                    if st.button("💬 Submit Contribution"):
                        if user_input:
                            self.submit_user_contribution(user_input)
                        else:
                            st.warning("Please enter your contribution")
                
                with col_y:
                    if st.button("🤖 Let Agent Respond"):
                        st.info("Agent will respond automatically")
    
    def show_agent_management_page(self):
        """Show agent management page."""

        st.header("👤 Agent Management")

        # Existing agents
        if st.session_state.simulation_controller:
            st.subheader("🤖 System Agents")

            agents = st.session_state.simulation_controller.agents

            for agent_name, agent in agents.items():
                with st.expander(f"🤖 {agent_name} - {agent.role}"):
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write(f"**Type:** {agent.agent_type}")
                        st.write(f"**Role:** {agent.role}")

                        if hasattr(agent, 'get_agent_summary'):
                            summary = agent.get_agent_summary()
                            st.write(f"**Expertise:** {', '.join(summary.get('expertise', []))}")
                            st.write(f"**Contributions:** {summary.get('total_contributions', 0)}")

                    with col2:
                        if st.button(f"View Details - {agent_name}"):
                            st.info(f"Detailed view for {agent_name} would be shown here")

                        # Knowledge base upload for this agent
                        st.write("**📚 Upload Knowledge:**")
                        uploaded_files = st.file_uploader(
                            f"Upload documents for {agent_name}",
                            type=['txt', 'md', 'json', 'csv'],
                            accept_multiple_files=True,
                            key=f"upload_{agent_name}"
                        )

                        if uploaded_files and st.button(f"Add to {agent_name} KB", key=f"add_{agent_name}"):
                            self.upload_knowledge_to_agent(agent_name, uploaded_files)
        
        # Batch Knowledge Upload
        st.subheader("📚 Batch Knowledge Upload")

        with st.expander("Upload Documents to Knowledge Base"):
            col1, col2 = st.columns(2)

            with col1:
                target_agent = st.selectbox(
                    "Target Agent",
                    ["All Agents"] + list(agents.keys()) if agents else ["No agents available"]
                )

                merge_mode = st.selectbox(
                    "Merge Mode",
                    ["append", "replace", "prepend"],
                    help="How to merge with existing knowledge"
                )

            with col2:
                create_shared = st.checkbox("Create Shared Knowledge Base")
                if create_shared:
                    shared_topic = st.text_input("Shared Topic Name")

            # File upload
            uploaded_files = st.file_uploader(
                "Upload Knowledge Documents",
                type=['txt', 'md', 'json', 'csv'],
                accept_multiple_files=True,
                help="Upload multiple documents to add to knowledge base"
            )

            if uploaded_files:
                st.write(f"📄 Selected {len(uploaded_files)} files:")
                for file in uploaded_files:
                    st.write(f"  • {file.name} ({file.size} bytes)")

                if st.button("Upload Knowledge", type="primary"):
                    if create_shared and shared_topic:
                        self.upload_shared_knowledge(shared_topic, uploaded_files)
                    elif target_agent != "All Agents":
                        self.upload_knowledge_to_agent(target_agent, uploaded_files, merge_mode)
                    else:
                        self.upload_knowledge_to_all_agents(uploaded_files, merge_mode)

        # Custom agents
        st.subheader("➕ Custom Agents")

        # Agent templates
        st.subheader("📋 Agent Templates")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🇺🇸 Create Biden Agent", use_container_width=True):
                self.load_agent_template("biden")

        with col2:
            if st.button("👔 Create Business Expert", use_container_width=True):
                self.load_agent_template("business")

        with col3:
            if st.button("🎓 Create Academic Expert", use_container_width=True):
                self.load_agent_template("academic")

        # Create new custom agent
        with st.expander("🆕 Create New Custom Agent", expanded=False):
            st.markdown("### Basic Information")

            col1, col2 = st.columns(2)
            with col1:
                agent_name = st.text_input("Agent Name", placeholder="e.g., Joe Biden")
                agent_role = st.text_input("Agent Role", placeholder="e.g., US President")
                agent_type = st.selectbox("Agent Type", ["political_leader", "expert", "advisor", "custom"])

            with col2:
                age = st.number_input("Age", min_value=18, max_value=100, value=50)
                nationality = st.text_input("Nationality", value="American")
                party_affiliation = st.text_input("Party/Affiliation", placeholder="e.g., Democratic Party")

            st.markdown("### Professional Background")

            col1, col2 = st.columns(2)
            with col1:
                current_position = st.text_input("Current Position", placeholder="e.g., 46th President of the United States")
                previous_positions = st.text_area("Previous Positions", placeholder="e.g., Vice President (2009-2017), Senator from Delaware (1973-2009)")
                education = st.text_area("Education", placeholder="e.g., University of Delaware (BA), Syracuse University (JD)")

            with col2:
                career_highlights = st.text_area("Career Highlights", placeholder="Key achievements and milestones")
                expertise = st.text_input("Expertise Areas (comma-separated)", placeholder="e.g., Foreign Policy, Healthcare, Climate Change")
                specializations = st.text_input("Specializations", placeholder="e.g., International Relations, Bipartisan Legislation")

            st.markdown("### Personality & Values")

            col1, col2 = st.columns(2)
            with col1:
                personality_traits = st.text_input("Personality Traits (comma-separated)",
                                                 placeholder="e.g., empathetic, experienced, collaborative")
                core_values = st.text_input("Core Values (comma-separated)",
                                           placeholder="e.g., unity, democracy, human dignity")
                political_philosophy = st.text_area("Political Philosophy",
                                                   placeholder="Core political beliefs and principles")

            with col2:
                communication_style = st.text_area("Communication Style",
                                                  placeholder="e.g., Empathetic, measured, focuses on unity and healing")
                decision_making_style = st.text_area("Decision Making Style",
                                                    placeholder="e.g., Consultative, evidence-based, seeks consensus")
                leadership_approach = st.text_area("Leadership Approach",
                                                  placeholder="e.g., Collaborative, experienced-based, institutionalist")

            st.markdown("### Custom Prompts & Instructions")

            system_prompt = st.text_area(
                "System Prompt",
                placeholder="Custom system prompt that defines how this agent should behave...",
                height=100,
                help="This prompt will be used to instruct the AI on how to roleplay as this character"
            )

            analysis_prompt_template = st.text_area(
                "Analysis Prompt Template",
                placeholder="Template for how this agent should analyze situations...",
                height=100,
                help="Template for strategic analysis responses"
            )

            discussion_prompt_template = st.text_area(
                "Discussion Prompt Template",
                placeholder="Template for how this agent should participate in discussions...",
                height=100,
                help="Template for discussion contributions"
            )

            st.markdown("### Knowledge Base Configuration")

            col1, col2 = st.columns(2)
            with col1:
                create_knowledge_base = st.checkbox("Create Dedicated Knowledge Base", value=True)
                knowledge_sources = st.text_area("Knowledge Sources",
                                                placeholder="List sources of information for this agent...")

            with col2:
                if create_knowledge_base:
                    kb_files = st.file_uploader(
                        "Upload Knowledge Base Files",
                        type=['txt', 'md', 'json', 'csv'],
                        accept_multiple_files=True,
                        help="Upload documents that this agent should have knowledge of"
                    )

            # Create agent button
            if st.button("🚀 Create Custom Agent", type="primary"):
                if agent_name and agent_role:
                    success = self.create_advanced_custom_agent(
                        agent_name, agent_role, agent_type, age, nationality, party_affiliation,
                        current_position, previous_positions, education, career_highlights,
                        expertise, specializations, personality_traits, core_values,
                        political_philosophy, communication_style, decision_making_style,
                        leadership_approach, system_prompt, analysis_prompt_template,
                        discussion_prompt_template, create_knowledge_base, knowledge_sources,
                        kb_files if create_knowledge_base else None
                    )

                    if success:
                        st.success(f"✅ Successfully created custom agent: {agent_name}")
                        st.balloons()
                    else:
                        st.error("❌ Failed to create custom agent")
                else:
                    st.error("Please provide at least agent name and role")
        
        # Existing custom agents
        if st.session_state.custom_agents:
            st.subheader("📋 Your Custom Agents")
            
            for i, custom_agent in enumerate(st.session_state.custom_agents):
                with st.expander(f"👤 {custom_agent['name']} - {custom_agent['role']}"):
                    st.write(f"**Background:** {custom_agent['persona'].get('background_summary', 'N/A')}")
                    st.write(f"**Expertise:** {', '.join(custom_agent['persona'].get('expertise', []))}")
                    st.write(f"**Values:** {', '.join(custom_agent['persona'].get('values', []))}")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button(f"Edit {custom_agent['name']}", key=f"edit_{i}"):
                            st.info("Edit functionality would be implemented here")
                    
                    with col2:
                        if st.button(f"Delete {custom_agent['name']}", key=f"delete_{i}"):
                            st.session_state.custom_agents.pop(i)
                            st.rerun()
    
    def show_results_page(self):
        """Show results and analysis page."""
        
        st.header("📊 Results & Analysis")
        
        if not st.session_state.simulation_history:
            st.info("No simulation results available yet.")
            return
        
        # Simulation selector
        sim_titles = [f"{sim.get('scenario', {}).get('title', 'Unknown')} - {sim.get('start_time', 'Unknown')}" 
                     for sim in st.session_state.simulation_history]
        
        selected_sim_idx = st.selectbox("Select Simulation", range(len(sim_titles)), 
                                       format_func=lambda x: sim_titles[x])
        
        selected_sim = st.session_state.simulation_history[selected_sim_idx]
        
        # Overview metrics
        st.subheader("📈 Overview")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Status", selected_sim.get('status', 'Unknown'))
        
        with col2:
            phases = len(selected_sim.get('phases_completed', []))
            st.metric("Phases Completed", phases)
        
        with col3:
            if 'analysis_results' in selected_sim:
                decision_score = selected_sim['analysis_results'].get('overall_scores', {}).get('decision_quality', 0)
                st.metric("Decision Quality", f"{decision_score:.1f}/10")
        
        with col4:
            if 'analysis_results' in selected_sim:
                process_score = selected_sim['analysis_results'].get('overall_scores', {}).get('process_effectiveness', 0)
                st.metric("Process Score", f"{process_score:.1f}/10")
        
        # Detailed analysis
        if 'analysis_results' in selected_sim:
            analysis = selected_sim['analysis_results']
            
            # Decision analysis
            st.subheader("🎯 Decision Analysis")
            
            if 'decision_analysis' in analysis:
                decision_analysis = analysis['decision_analysis']
                
                # Criteria scores
                if 'criterion_scores' in decision_analysis:
                    st.write("**Decision Quality by Criteria:**")
                    
                    criteria_data = []
                    for criterion, data in decision_analysis['criterion_scores'].items():
                        criteria_data.append({
                            'Criterion': criterion.replace('_', ' ').title(),
                            'Score': data['score'],
                            'Description': data['description']
                        })
                    
                    st.dataframe(criteria_data, use_container_width=True)
            
            # Process analysis
            st.subheader("⚙️ Process Analysis")
            
            if 'process_analysis' in analysis:
                process_analysis = analysis['process_analysis']
                
                col1, col2 = st.columns(2)
                
                with col1:
                    if 'timing_analysis' in process_analysis:
                        st.write("**Phase Durations:**")
                        timing = process_analysis['timing_analysis']
                        for phase, duration in timing.get('phase_durations', {}).items():
                            st.write(f"- {phase.title()}: {duration:.1f} minutes")
                
                with col2:
                    if 'discussion_flow' in process_analysis:
                        flow = process_analysis['discussion_flow']
                        st.write("**Discussion Flow:**")
                        st.write(f"- Total Rounds: {flow.get('total_rounds', 0)}")
                        st.write(f"- Engagement Trend: {flow.get('engagement_trend', 'N/A')}")
                        st.write(f"- Momentum: {flow.get('discussion_momentum', 'N/A')}")
        
        # Export options
        st.subheader("📤 Export Results")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📄 Export PDF Report"):
                st.info("PDF export would be implemented here")
        
        with col2:
            if st.button("📊 Export Data (JSON)"):
                st.download_button(
                    label="Download JSON",
                    data=json.dumps(selected_sim, indent=2),
                    file_name=f"simulation_{selected_sim_idx}.json",
                    mime="application/json"
                )
        
        with col3:
            if st.button("📈 Export Analysis"):
                if 'analysis_results' in selected_sim:
                    st.download_button(
                        label="Download Analysis",
                        data=json.dumps(selected_sim['analysis_results'], indent=2),
                        file_name=f"analysis_{selected_sim_idx}.json",
                        mime="application/json"
                    )
    
    def show_settings_page(self):
        """Show settings and configuration page."""
        
        st.header("⚙️ Settings")
        
        # System settings
        st.subheader("🖥️ System Configuration")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Model Settings:**")
            model_provider = st.selectbox("LLM Provider", ["OpenAI", "Anthropic", "Local"])
            model_name = st.text_input("Model Name", value="gpt-4")
            temperature = st.slider("Temperature", 0.0, 2.0, 0.7, 0.1)
        
        with col2:
            st.write("**Memory Settings:**")
            memory_limit = st.slider("Conversation Memory Limit", 10, 100, 50)
            enable_mem0 = st.checkbox("Enable mem0 Integration", value=True)
            cross_session_memory = st.checkbox("Cross-session Memory", value=True)
        
        # Knowledge base settings
        st.subheader("📚 Knowledge Base Configuration")
        
        kb_dir = st.text_input("Knowledge Base Directory", value="config/knowledge_base")
        
        if st.button("Reload Knowledge Bases"):
            st.info("Knowledge bases would be reloaded here")
        
        # Export/Import settings
        st.subheader("💾 Data Management")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📤 Export All Data"):
                st.info("Data export would be implemented here")
        
        with col2:
            uploaded_file = st.file_uploader("📥 Import Data", type=['json'])
            if uploaded_file:
                st.info("Data import would be implemented here")
        
        # Reset options
        st.subheader("🔄 Reset Options")
        
        st.warning("⚠️ These actions cannot be undone!")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🗑️ Clear Simulation History"):
                st.session_state.simulation_history = []
                st.success("Simulation history cleared")
        
        with col2:
            if st.button("🤖 Reset Custom Agents"):
                st.session_state.custom_agents = []
                st.success("Custom agents cleared")
        
        with col3:
            if st.button("🔄 Reset All Settings"):
                # Reset all session state
                for key in list(st.session_state.keys()):
                    del st.session_state[key]
                st.success("All settings reset")
                st.rerun()
    
    def initialize_system(self):
        """Initialize the simulation system."""
        
        try:
            # Load configuration
            config_path = "config/simulation_config.json"
            agents_config_path = "config/agents_config.json"
            
            # Initialize simulation controller
            controller = SimulationController(config_path)
            
            # Load agent configurations
            if os.path.exists(agents_config_path):
                with open(agents_config_path, 'r', encoding='utf-8') as f:
                    agents_config = json.load(f)
                
                # Initialize agents
                result = controller.initialize_agents(agents_config, "config/knowledge_base")
                
                st.session_state.simulation_controller = controller
                st.success(f"✅ System initialized successfully! Loaded {result['total_count']} agents.")
            else:
                st.error(f"❌ Agent configuration file not found: {agents_config_path}")
        
        except Exception as e:
            st.error(f"❌ Failed to initialize system: {str(e)}")
    
    def start_simulation(self):
        """Start the configured simulation."""

        try:
            controller = st.session_state.simulation_controller
            scenario = st.session_state.current_scenario
            config = st.session_state.current_config
            selected_agents = st.session_state.selected_agents
            turn_order_config = st.session_state.get('turn_order_config')

            # Run simulation with dynamic configuration
            simulation_result = controller.run_simulation(
                scenario=scenario,
                simulation_config=config,
                selected_agents=selected_agents,
                turn_order_config=turn_order_config
            )

            # Store result
            st.session_state.simulation_history.append(simulation_result)
            st.session_state.current_simulation = simulation_result

            st.success("✅ Simulation completed successfully!")

            # Show participation summary
            st.info(f"👥 Participated: {', '.join(selected_agents)}")
            if turn_order_config and turn_order_config.get('mode') == 'Custom Order':
                st.info(f"📋 Speaking order was: {' → '.join(selected_agents)}")

        except Exception as e:
            st.error(f"❌ Simulation failed: {str(e)}")
    
    def submit_user_contribution(self, contribution: str):
        """Submit user contribution to the simulation."""

        # This would integrate with the simulation system
        # to add user input to the discussion

        if not hasattr(st.session_state, 'simulation_messages'):
            st.session_state.simulation_messages = []

        message = {
            'speaker': 'User',
            'content': contribution,
            'timestamp': datetime.now().isoformat()
        }

        st.session_state.simulation_messages.append(message)
        st.success("✅ Your contribution has been submitted!")

    def upload_knowledge_to_agent(self, agent_name: str, uploaded_files, merge_mode: str = "append"):
        """Upload knowledge files to a specific agent."""

        try:
            # Get knowledge manager from simulation controller
            if not st.session_state.simulation_controller:
                st.error("❌ System not initialized")
                return

            # Process uploaded files
            combined_content = []

            for uploaded_file in uploaded_files:
                # Read file content
                content = uploaded_file.read().decode('utf-8')

                # Add file header
                file_header = f"\n\n## From {uploaded_file.name}\n\nUploaded: {datetime.now().isoformat()}\nSize: {len(content)} characters\n\n"
                combined_content.append(file_header + content)

            # Combine all content
            full_content = "\n".join(combined_content)

            # Update agent knowledge (this would need to be implemented in the controller)
            # For now, we'll show success message
            st.success(f"✅ Uploaded {len(uploaded_files)} files to {agent_name} knowledge base")
            st.info(f"📊 Added {len(full_content)} characters of knowledge")

            # In a real implementation, you would call:
            # knowledge_manager = st.session_state.simulation_controller.knowledge_manager
            # knowledge_manager.update_agent_knowledge(agent_name, full_content, merge_mode)

        except Exception as e:
            st.error(f"❌ Failed to upload knowledge: {str(e)}")

    def upload_knowledge_to_all_agents(self, uploaded_files, merge_mode: str = "append"):
        """Upload knowledge files to all agents."""

        if not st.session_state.simulation_controller:
            st.error("❌ System not initialized")
            return

        agents = st.session_state.simulation_controller.agents

        for agent_name in agents.keys():
            self.upload_knowledge_to_agent(agent_name, uploaded_files, merge_mode)

    def upload_shared_knowledge(self, topic: str, uploaded_files):
        """Create shared knowledge base from uploaded files."""

        try:
            # Process uploaded files
            combined_content = []

            for uploaded_file in uploaded_files:
                content = uploaded_file.read().decode('utf-8')
                file_header = f"\n\n## From {uploaded_file.name}\n\n"
                combined_content.append(file_header + content)

            full_content = f"# Shared Knowledge: {topic}\n\nCreated: {datetime.now().isoformat()}\n\n" + "\n".join(combined_content)

            st.success(f"✅ Created shared knowledge base: {topic}")
            st.info(f"📊 Added {len(uploaded_files)} files, {len(full_content)} characters")

            # In a real implementation, you would call:
            # knowledge_manager = st.session_state.simulation_controller.knowledge_manager
            # knowledge_manager.create_shared_knowledge_base(topic, full_content)

        except Exception as e:
            st.error(f"❌ Failed to create shared knowledge: {str(e)}")

    def create_advanced_custom_agent(self, agent_name, agent_role, agent_type, age, nationality,
                                   party_affiliation, current_position, previous_positions,
                                   education, career_highlights, expertise, specializations,
                                   personality_traits, core_values, political_philosophy,
                                   communication_style, decision_making_style, leadership_approach,
                                   system_prompt, analysis_prompt_template, discussion_prompt_template,
                                   create_knowledge_base, knowledge_sources, kb_files):
        """Create an advanced custom agent with detailed configuration."""

        try:
            # Build comprehensive agent configuration
            custom_config = {
                "name": agent_name,
                "role": agent_role,
                "type": agent_type,
                "persona": {
                    # Basic information
                    "age": age,
                    "nationality": nationality,
                    "party_affiliation": party_affiliation,

                    # Professional background
                    "current_position": current_position,
                    "previous_positions": previous_positions,
                    "education": education,
                    "career_highlights": career_highlights,
                    "expertise": [e.strip() for e in expertise.split(",") if e.strip()],
                    "specializations": specializations,

                    # Personality and values
                    "personality_traits": [t.strip() for t in personality_traits.split(",") if t.strip()],
                    "core_values": [v.strip() for v in core_values.split(",") if v.strip()],
                    "political_philosophy": political_philosophy,
                    "communication_style": communication_style,
                    "decision_making_style": decision_making_style,
                    "leadership_approach": leadership_approach,

                    # Custom prompts
                    "system_prompt": system_prompt,
                    "analysis_prompt_template": analysis_prompt_template,
                    "discussion_prompt_template": discussion_prompt_template,

                    # Knowledge configuration
                    "knowledge_sources": knowledge_sources,
                    "has_dedicated_kb": create_knowledge_base
                }
            }

            # Create knowledge base if requested
            if create_knowledge_base and kb_files:
                kb_content = self.process_knowledge_base_files(agent_name, kb_files, knowledge_sources)
                custom_config["persona"]["knowledge_base_content"] = kb_content

            # Add to session state
            st.session_state.custom_agents.append(custom_config)

            # If simulation controller is available, add the agent
            if st.session_state.simulation_controller:
                success = st.session_state.simulation_controller.add_custom_agent(agent_name, custom_config)
                if not success:
                    st.warning("⚠️ Agent created but not added to active simulation")

            # Save to persistent storage
            self.save_custom_agent_config(custom_config)

            return True

        except Exception as e:
            st.error(f"❌ Error creating custom agent: {str(e)}")
            return False

    def process_knowledge_base_files(self, agent_name, kb_files, knowledge_sources):
        """Process uploaded knowledge base files for the custom agent."""

        kb_content = f"# {agent_name} Knowledge Base\n\n"
        kb_content += f"Created: {datetime.now().isoformat()}\n\n"

        if knowledge_sources:
            kb_content += f"## Knowledge Sources\n\n{knowledge_sources}\n\n"

        kb_content += "## Uploaded Documents\n\n"

        for kb_file in kb_files:
            try:
                content = kb_file.read().decode('utf-8')
                kb_content += f"### {kb_file.name}\n\n{content}\n\n---\n\n"
                st.success(f"✅ Processed: {kb_file.name}")
            except Exception as e:
                st.warning(f"⚠️ Could not process {kb_file.name}: {e}")

        # Save knowledge base file
        kb_filename = f"{agent_name.lower().replace(' ', '_')}_knowledge.txt"
        kb_path = Path("config/knowledge_base") / kb_filename

        try:
            with open(kb_path, 'w', encoding='utf-8') as f:
                f.write(kb_content)
            st.info(f"💾 Knowledge base saved to: {kb_path}")
        except Exception as e:
            st.warning(f"⚠️ Could not save knowledge base file: {e}")

        return kb_content

    def save_custom_agent_config(self, custom_config):
        """Save custom agent configuration to persistent storage."""

        try:
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)

            custom_agents_file = config_dir / "custom_agents.json"

            # Load existing custom agents
            if custom_agents_file.exists():
                with open(custom_agents_file, 'r', encoding='utf-8') as f:
                    existing_agents = json.load(f)
            else:
                existing_agents = []

            # Add new agent
            existing_agents.append(custom_config)

            # Save updated list
            with open(custom_agents_file, 'w', encoding='utf-8') as f:
                json.dump(existing_agents, f, indent=2, ensure_ascii=False)

            st.info(f"💾 Agent configuration saved to: {custom_agents_file}")

        except Exception as e:
            st.warning(f"⚠️ Could not save agent configuration: {e}")

    def load_agent_template(self, template_name: str):
        """Load and create agent from template."""

        template_path = Path(f"config/agent_templates/{template_name}_template.json")

        try:
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_config = json.load(f)

                # Create agent from template
                success = self.create_agent_from_template(template_config)

                if success:
                    st.success(f"✅ Successfully created {template_config['name']} from template!")
                    st.balloons()
                else:
                    st.error(f"❌ Failed to create agent from template")
            else:
                st.error(f"❌ Template not found: {template_path}")

        except Exception as e:
            st.error(f"❌ Error loading template: {str(e)}")

    def create_agent_from_template(self, template_config: Dict[str, Any]) -> bool:
        """Create agent from template configuration."""

        try:
            # Add to session state
            st.session_state.custom_agents.append(template_config)

            # If simulation controller is available, add the agent
            if st.session_state.simulation_controller:
                success = st.session_state.simulation_controller.add_custom_agent(
                    template_config['name'], template_config
                )
                if not success:
                    st.warning("⚠️ Agent created but not added to active simulation")

            # Save to persistent storage
            self.save_custom_agent_config(template_config)

            # Show agent details
            st.info(f"📋 Created: {template_config['name']} - {template_config['role']}")

            persona = template_config.get('persona', {})
            if 'expertise' in persona:
                st.info(f"🎯 Expertise: {', '.join(persona['expertise'][:3])}...")

            return True

        except Exception as e:
            st.error(f"❌ Error creating agent from template: {str(e)}")
            return False


def main():
    """Main function to run the web interface."""
    
    interface = WebInterface()
    interface.run()


if __name__ == "__main__":
    main()
