"""
LLM Configuration Manager

Handles different LLM providers and API configurations
"""

import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class LLMConfig:
    """LLM configuration manager for different providers."""
    
    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY')
        self.base_url = os.getenv('OPENAI_BASE_URL')
        self.model_name = os.getenv('OPENAI_MODEL', 'Qwen/Qwen3-8B')
        self.provider = os.getenv('MODEL_PROVIDER', 'siliconflow')
        self.temperature = float(os.getenv('TEMPERATURE', '0.7'))
        self.max_tokens = int(os.getenv('MAX_TOKENS', '2000'))
    
    def get_openai_config(self) -> Dict[str, Any]:
        """Get OpenAI-compatible configuration."""
        
        config = {
            'api_key': self.api_key,
            'model': self.model_name,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
        }
        
        # Add base_url if using custom API
        if self.base_url:
            config['base_url'] = self.base_url
        
        return config
    
    def get_client_kwargs(self) -> Dict[str, Any]:
        """Get client initialization kwargs."""
        
        kwargs = {
            'api_key': self.api_key,
        }
        
        if self.base_url:
            kwargs['base_url'] = self.base_url
        
        return kwargs
    
    def get_chat_completion_kwargs(self) -> Dict[str, Any]:
        """Get chat completion kwargs."""
        
        return {
            'model': self.model_name,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
        }
    
    def is_configured(self) -> bool:
        """Check if LLM is properly configured."""
        
        return bool(self.api_key and self.api_key != 'your_openai_api_key_here')
    
    def get_provider_info(self) -> Dict[str, str]:
        """Get provider information."""
        
        return {
            'provider': self.provider,
            'model': self.model_name,
            'base_url': self.base_url or 'https://api.openai.com/v1',
            'configured': str(self.is_configured())
        }


# Global LLM config instance
llm_config = LLMConfig()


def get_llm_client():
    """Get configured LLM client."""
    
    try:
        from openai import OpenAI
        
        client = OpenAI(**llm_config.get_client_kwargs())
        return client
        
    except ImportError:
        raise ImportError("OpenAI package not installed. Run: pip install openai")
    except Exception as e:
        raise Exception(f"Failed to initialize LLM client: {e}")


def test_llm_connection():
    """Test LLM connection."""
    
    try:
        client = get_llm_client()
        
        # Test with a simple message
        response = client.chat.completions.create(
            messages=[
                {"role": "user", "content": "Hello, please respond with 'Connection successful'"}
            ],
            **llm_config.get_chat_completion_kwargs()
        )
        
        return {
            'success': True,
            'response': response.choices[0].message.content,
            'model': response.model,
            'provider_info': llm_config.get_provider_info()
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'provider_info': llm_config.get_provider_info()
        }


def generate_response(messages: list, **kwargs) -> str:
    """Generate response using configured LLM."""
    
    try:
        client = get_llm_client()
        
        # Merge default config with provided kwargs
        completion_kwargs = llm_config.get_chat_completion_kwargs()
        completion_kwargs.update(kwargs)
        
        response = client.chat.completions.create(
            messages=messages,
            **completion_kwargs
        )
        
        return response.choices[0].message.content
        
    except Exception as e:
        return f"Error generating response: {str(e)}"


if __name__ == "__main__":
    # Test configuration
    print("🔧 LLM Configuration Test")
    print("=" * 40)
    
    config_info = llm_config.get_provider_info()
    for key, value in config_info.items():
        print(f"{key}: {value}")
    
    print("\n🧪 Testing connection...")
    result = test_llm_connection()
    
    if result['success']:
        print("✅ Connection successful!")
        print(f"Response: {result['response']}")
        print(f"Model: {result['model']}")
    else:
        print("❌ Connection failed!")
        print(f"Error: {result['error']}")
