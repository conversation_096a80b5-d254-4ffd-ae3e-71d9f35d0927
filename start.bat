@echo off
echo 🎯 Strategic Simulation System - Quick Start
echo ================================================

echo 📦 Installing dependencies...
pip install streamlit openai python-dotenv click pandas pydantic requests

echo 📝 Creating .env file...
if not exist .env (
    echo # SiliconFlow API Configuration > .env
    echo OPENAI_API_KEY=sk-xxdbfsgbcrcapwbjdaohlxkilvbmwvxsurallxrunlrrggbw >> .env
    echo OPENAI_BASE_URL=https://api.siliconflow.cn/v1 >> .env
    echo OPENAI_MODEL=Qwen/Qwen3-8B >> .env
    echo. >> .env
    echo # Model Configuration >> .env
    echo MODEL_PROVIDER=siliconflow >> .env
    echo MODEL_NAME=Qwen/Qwen3-8B >> .env
    echo TEMPERATURE=0.7 >> .env
    echo MAX_TOKENS=2000 >> .env
    echo. >> .env
    echo # System Configuration >> .env
    echo DEMO_MODE=false >> .env
    echo LOG_LEVEL=INFO >> .env
    echo ✅ .env file created with Qwen/Qwen3-8B configuration
) else (
    echo ✅ .env file already exists
)

echo 🚀 Starting demo application...
echo 📱 Browser will open at http://localhost:8501
echo 🛑 Press Ctrl+C to stop

streamlit run demo_app.py

pause
