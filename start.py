#!/usr/bin/env python3
"""
Strategic Simulation System - 启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def install_dependencies():
    """安装必要的依赖"""
    print("📦 安装依赖包...")
    
    # 基础依赖
    packages = [
        "streamlit",
        "openai", 
        "python-dotenv",
        "click",
        "pandas",
        "pydantic",
        "requests"
    ]
    
    for package in packages:
        try:
            print(f"  安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError as e:
            print(f"⚠️ 安装 {package} 失败: {e}")

def setup_env():
    """设置环境变量"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("📝 创建 .env 文件...")
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write("""# Strategic Simulation System
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
DEMO_MODE=true
""")
        print("✅ .env 文件已创建")
    else:
        print("✅ .env 文件已存在")

def start_web():
    """启动Web界面"""
    print("🚀 启动Web界面...")
    print("📱 浏览器将打开 http://localhost:8501")
    
    try:
        # 使用简化的Streamlit启动
        os.system(f"{sys.executable} -m streamlit run main.py web")
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

def main():
    print("🎯 Strategic Simulation System")
    print("=" * 50)
    
    # 安装依赖
    install_dependencies()
    
    # 设置环境
    setup_env()
    
    # 启动Web界面
    start_web()

if __name__ == "__main__":
    main()
