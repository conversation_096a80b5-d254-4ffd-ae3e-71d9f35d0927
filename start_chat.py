#!/usr/bin/env python3
"""
聊天室启动脚本 - 可选择不同版本
"""

import subprocess
import sys
import os

def main():
    """主启动函数"""
    
    print("🎯 战略推演聊天室启动器")
    print("=" * 50)
    
    # 检查是否在正确目录
    if not os.path.exists("wechat_style_chat.py"):
        print("❌ 找不到聊天室文件")
        print("请确保在 strategic_simulation 目录中运行此脚本")
        input("按回车键退出...")
        return
    
    print("请选择要启动的聊天室版本:")
    print()
    print("1. 🌊 简化流式聊天室 (simple_stream_chat.py)")
    print("   - 专门测试流式输出效果")
    print("   - 功能简单，流式效果明显")
    print()
    print("2. 💬 微信风格聊天室 (wechat_style_chat.py)")
    print("   - 完整的推演功能")
    print("   - 微信风格界面")
    print("   - 支持总结和决策")
    print()
    print("3. 📋 原始推演系统 (demo_app.py)")
    print("   - 传统的推演界面")
    print("   - 完整功能但无流式输出")
    print()
    
    while True:
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            app_file = "simple_stream_chat.py"
            app_name = "简化流式聊天室"
            break
        elif choice == "2":
            app_file = "wechat_style_chat.py"
            app_name = "微信风格聊天室"
            break
        elif choice == "3":
            app_file = "demo_app.py"
            app_name = "原始推演系统"
            break
        else:
            print("❌ 无效选择，请输入 1、2 或 3")
    
    print(f"\n🚀 启动 {app_name}...")
    print("📱 浏览器将打开: http://localhost:8501")
    print("🛑 按 Ctrl+C 停止系统")
    print()
    
    try:
        # 启动选择的应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", app_file,
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ])
    except KeyboardInterrupt:
        print(f"\n👋 {app_name} 已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
