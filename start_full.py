#!/usr/bin/env python3
"""
完整功能启动脚本 - 包含所有页面和流式输出
"""

import subprocess
import sys
import os

def main():
    """完整功能启动"""
    
    print("🎯 战略推演系统 - 完整版启动")
    print("=" * 50)
    print()
    print("✅ 包含功能:")
    print("  📱 首页 - 系统介绍和状态")
    print("  🤖 智能体管理 - 查看和创建智能体")
    print("  📋 场景管理 - 查看和创建推演场景")
    print("  🚀 开始推演 - 配置和启动推演")
    print("  💬 AI聊天室 - 带流式输出的推演对话")
    print("  📊 推演结果 - 查看历史推演记录")
    print()
    print("🌊 特色功能:")
    print("  ✨ 流式输出 - AI回复时逐词显示")
    print("  👤 用户智能体 - 支持用户扮演角色")
    print("  🎯 智能总结 - AI自动总结讨论要点")
    print("  ⚖️ 决策支持 - 生成最终决策方案")
    print()
    
    # 检查文件
    if not os.path.exists("demo_app.py"):
        print("❌ 找不到 demo_app.py 文件")
        print("请确保在 strategic_simulation 目录中运行此脚本")
        input("按回车键退出...")
        return
    
    # 检查环境变量文件
    if not os.path.exists(".env"):
        print("📝 创建 .env 配置文件...")
        with open(".env", "w", encoding="utf-8") as f:
            f.write("""# SiliconFlow API Configuration
OPENAI_API_KEY=sk-xxdbfsgbcrcapwbjdaohlxkilvbmwvxsurallxrunlrrggbw
OPENAI_BASE_URL=https://api.siliconflow.cn/v1
OPENAI_MODEL=Qwen/Qwen3-8B

# Model Configuration
MODEL_PROVIDER=siliconflow
MODEL_NAME=Qwen/Qwen3-8B
TEMPERATURE=0.7
MAX_TOKENS=2000

# System Configuration
DEMO_MODE=false
LOG_LEVEL=INFO
""")
        print("✅ .env 文件已创建")
    
    print("🚀 启动完整推演系统...")
    print("📱 浏览器将打开: http://localhost:8501")
    print("🛑 按 Ctrl+C 停止系统")
    print()
    print("💡 使用提示:")
    print("  1. 首先在'智能体管理'页面查看可用智能体")
    print("  2. 在'场景管理'页面选择推演场景")
    print("  3. 在'开始推演'页面配置参数并启动")
    print("  4. 在推演界面勾选'🌊 启用流式输出'")
    print("  5. 点击'🎬 开始推演'观看AI对话")
    print()
    
    try:
        # 启动完整系统
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "demo_app.py",
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ])
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
