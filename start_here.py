#!/usr/bin/env python3
"""
在strategic_simulation目录中的启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """显示启动横幅"""
    print("=" * 60)
    print("🎯 战略推演系统 - 智囊团辩论平台")
    print("=" * 60)
    print()

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查当前目录
    current_dir = Path.cwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查主应用文件
    demo_app = Path("demo_app.py")
    if not demo_app.exists():
        print("❌ 找不到 demo_app.py 文件")
        return False
    print("✅ 找到主应用文件")
    
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = ["streamlit", "openai", "python-dotenv", "requests"]
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 安装 {package}...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], check=True, capture_output=True)
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"⚠️ {package} 安装失败，但可能不影响运行")
    
    return True

def setup_config():
    """设置配置文件"""
    print("\n⚙️ 设置配置文件...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 创建 .env 配置文件...")
        env_content = """# SiliconFlow API Configuration
OPENAI_API_KEY=sk-xxdbfsgbcrcapwbjdaohlxkilvbmwvxsurallxrunlrrggbw
OPENAI_BASE_URL=https://api.siliconflow.cn/v1
OPENAI_MODEL=Qwen/Qwen2.5-7B-Instruct

# Model Configuration
MODEL_PROVIDER=siliconflow
MODEL_NAME=Qwen/Qwen2.5-7B-Instruct
TEMPERATURE=0.8
MAX_TOKENS=2000

# System Configuration
DEMO_MODE=false
LOG_LEVEL=INFO
"""
        with open(env_file, "w", encoding="utf-8") as f:
            f.write(env_content)
        print("✅ .env 文件已创建")
    else:
        print("✅ .env 文件已存在")
    
    return True

def show_menu():
    """显示启动菜单"""
    print("\n🎮 选择启动模式:")
    print("1. 🏠 项目主页 (推荐) - 完整的项目启动页面")
    print("2. 🎯 完整推演系统 - 主应用，包含所有功能")
    print("3. 🔥 递进式研讨 - 专门的递进式智囊团研讨")
    print("4. 🎪 智囊团辩论 - 专门的辩论模式")
    print("5. 🧪 自动轮流测试 - 测试自动发言功能")
    print("0. ❌ 退出")
    print()

def launch_app(choice):
    """启动选择的应用"""
    app_files = {
        "1": "main_app.py",  # 主启动页面
        "2": "demo_app.py",
        "3": "progressive_debate.py",
        "4": "debate_simulation.py",
        "5": "auto_speak_test.py"
    }
    
    if choice not in app_files:
        print("❌ 无效选择")
        return False
    
    app_file = app_files[choice]
    app_path = Path(app_file)
    
    if not app_path.exists():
        print(f"❌ 找不到文件: {app_file}")
        return False
    
    print(f"\n🚀 启动 {app_file}...")
    print("📱 浏览器将自动打开: http://localhost:8501")
    print("🛑 按 Ctrl+C 停止应用")
    print()
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", app_file,
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
    
    return True

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        input("\n按回车键退出...")
        return
    
    # 安装依赖
    if not install_dependencies():
        input("\n按回车键退出...")
        return
    
    # 设置配置
    if not setup_config():
        input("\n按回车键退出...")
        return
    
    print("\n✅ 环境检查完成，系统准备就绪！")
    
    # 显示菜单并启动
    while True:
        show_menu()
        choice = input("请选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 再见！")
            break
        elif choice in ["1", "2", "3", "4", "5"]:
            launch_app(choice)
            print("\n" + "="*50)
            print("应用已停止，返回主菜单")
        else:
            print("❌ 无效选择，请输入 0-5")
        
        print()

if __name__ == "__main__":
    main()
