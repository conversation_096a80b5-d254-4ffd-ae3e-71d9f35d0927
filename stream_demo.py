#!/usr/bin/env python3
"""
流式输出和用户智能体演示
"""

import streamlit as st
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 设置页面配置
st.set_page_config(
    page_title="流式输出演示",
    page_icon="🌊",
    layout="wide"
)

def test_stream_output():
    """测试流式输出"""
    
    try:
        from utils.llm_config import generate_response
        
        st.title("🌊 流式输出演示")
        
        # 用户输入
        user_input = st.text_input("输入您的问题:", placeholder="例如: 请分析台海局势")
        
        if st.button("🚀 开始流式回复"):
            if user_input:
                messages = [
                    {"role": "system", "content": "你是一个专业的战略分析师，请用中文详细回答问题。"},
                    {"role": "user", "content": user_input}
                ]
                
                # 创建占位符
                response_placeholder = st.empty()
                full_response = ""
                
                try:
                    # 获取流式响应
                    stream = generate_response(messages, stream=True, max_tokens=500)
                    
                    # 处理流式输出
                    for chunk in stream:
                        if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                            delta = chunk.choices[0].delta
                            if hasattr(delta, 'content') and delta.content:
                                full_response += delta.content
                                # 实时更新显示
                                response_placeholder.markdown(f"**AI专家** 正在回复...\n\n{full_response}▋")
                    
                    # 完成后移除光标
                    response_placeholder.markdown(f"**AI专家** - {datetime.now().strftime('%H:%M:%S')}\n\n{full_response}")
                    
                except Exception as e:
                    st.error(f"流式输出失败: {e}")
            else:
                st.warning("请输入问题")
    
    except ImportError:
        st.error("AI模块未正确导入，请检查配置")

def test_user_agents():
    """测试用户智能体功能"""
    
    st.title("👤 用户智能体演示")
    
    # 初始化用户智能体
    if 'demo_user_agents' not in st.session_state:
        st.session_state.demo_user_agents = ["张三", "李四", "王五"]
    
    if 'demo_messages' not in st.session_state:
        st.session_state.demo_messages = []
    
    # 显示用户智能体
    st.subheader("可用的用户智能体:")
    for agent in st.session_state.demo_user_agents:
        st.write(f"👤 {agent}")
    
    # 添加新智能体
    new_agent = st.text_input("添加新智能体:")
    if st.button("➕ 添加"):
        if new_agent and new_agent not in st.session_state.demo_user_agents:
            st.session_state.demo_user_agents.append(new_agent)
            st.success(f"✅ 已添加: {new_agent}")
            st.rerun()
    
    # 模拟发言
    st.subheader("模拟发言:")
    
    selected_agent = st.selectbox("选择发言人:", st.session_state.demo_user_agents)
    user_input = st.text_area(f"以 {selected_agent} 的身份发言:", height=100)
    
    if st.button("📝 提交发言"):
        if user_input:
            message = {
                "speaker": selected_agent,
                "content": user_input,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.demo_messages.append(message)
            st.success(f"✅ {selected_agent} 的发言已提交")
            st.rerun()
    
    # 显示发言历史
    if st.session_state.demo_messages:
        st.subheader("发言历史:")
        for msg in st.session_state.demo_messages:
            with st.chat_message("user"):
                st.write(f"**{msg['speaker']}** - {msg['timestamp']}")
                st.write(msg['content'])

def main():
    """主函数"""
    
    st.sidebar.title("🎮 演示选择")
    
    demo_type = st.sidebar.selectbox(
        "选择演示类型:",
        ["🌊 流式输出", "👤 用户智能体", "🎯 完整演示"]
    )
    
    if demo_type == "🌊 流式输出":
        test_stream_output()
    elif demo_type == "👤 用户智能体":
        test_user_agents()
    else:
        st.title("🎯 完整功能演示")
        st.info("请使用主应用体验完整功能: `streamlit run demo_app.py`")
        
        st.markdown("""
        ### 🌊 流式输出功能
        - AI回复时逐字显示，更有真实感
        - 支持实时显示生成过程
        - 可以看到AI"思考"的过程
        
        ### 👤 用户智能体功能
        - 可以添加自定义的用户控制智能体
        - 用户可以扮演特定角色参与讨论
        - 支持AI代为发言功能
        
        ### 🔄 自动轮流发言
        - AI智能体自动轮流发言
        - 遇到用户智能体时自动暂停
        - 支持手动控制和自动模式切换
        """)

if __name__ == "__main__":
    main()
