#!/usr/bin/env python3
"""
测试 agent_prompts 错误修复
"""

def test_agent_prompts_fix():
    """测试修复后的智能体提示词生成"""
    
    print("🧪 测试 agent_prompts 错误修复")
    print("=" * 50)
    
    # 模拟场景数据
    scenario = {
        'title': '台海危机应对策略',
        'context': '情报显示台海周边军事活动增加，美国应如何应对？'
    }
    
    # 模拟智能体信息
    agent_info = {
        'name': '唐纳德·特朗普',
        'role': '美国总统'
    }
    
    # 测试标准智能体列表
    standard_agents = ["trump", "biden", "military_expert", "political_expert", "moderator"]
    
    print("✅ 标准智能体列表定义成功:")
    for agent in standard_agents:
        print(f"  - {agent}")
    
    # 测试标准智能体检查
    test_agent_id = "trump"
    if test_agent_id in standard_agents:
        print(f"✅ {test_agent_id} 是标准智能体")
    else:
        print(f"❌ {test_agent_id} 不是标准智能体")
    
    # 测试自定义智能体检查
    custom_agent_id = "custom_expert"
    if custom_agent_id not in standard_agents:
        print(f"✅ {custom_agent_id} 是自定义智能体")
        
        # 模拟自定义提示词生成
        custom_prompt = f"你是{agent_info['name']}，{agent_info['role']}。"
        print(f"✅ 自定义提示词生成成功: {custom_prompt[:50]}...")
    else:
        print(f"❌ {custom_agent_id} 被误认为标准智能体")
    
    print()
    print("🎉 agent_prompts 错误修复测试通过！")
    print("现在可以正常使用流式显示功能了。")

if __name__ == "__main__":
    test_agent_prompts_fix()
