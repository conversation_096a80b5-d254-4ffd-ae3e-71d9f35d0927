#!/usr/bin/env python3
"""
测试智能体本地存储功能
"""

import streamlit as st
from datetime import datetime

# 注意：不在这里设置页面配置，避免与主应用冲突

def main():
    """测试智能体存储功能"""
    
    st.title("💾 智能体本地存储测试")
    st.markdown("**测试自定义智能体的本地JSON文件存储功能**")
    
    # 测试存储模块
    try:
        from agent_storage import get_agent_storage, AgentStorage
        storage = get_agent_storage()
        st.success("✅ 智能体存储模块加载成功")
    except ImportError as e:
        st.error(f"❌ 存储模块加载失败: {e}")
        return
    
    # 存储状态
    st.markdown("### 📊 存储状态")
    
    storage_info = storage.get_storage_info()
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("智能体数量", storage_info.get("agent_count", 0))
    
    with col2:
        file_size = storage_info.get("file_size", 0)
        st.metric("文件大小", f"{file_size} 字节")
    
    with col3:
        file_exists = storage_info.get("file_exists", False)
        st.metric("文件状态", "✅ 存在" if file_exists else "❌ 不存在")
    
    # 显示存储详情
    with st.expander("📋 存储详情", expanded=False):
        st.json(storage_info)
    
    # 功能测试
    st.markdown("### 🧪 功能测试")
    
    tab1, tab2, tab3, tab4 = st.tabs(["➕ 添加测试", "📋 查看管理", "🔄 导入导出", "🧹 清理测试"])
    
    with tab1:
        st.markdown("#### ➕ 添加测试智能体")
        
        if st.button("🚀 添加测试智能体", type="primary"):
            # 创建测试智能体
            test_agents = [
                {
                    "agent_id": "test_zhang_san",
                    "name": "张三",
                    "role": "测试经济专家",
                    "type": "domain_expert",
                    "nationality": "美国",
                    "background": "这是一个测试用的智能体",
                    "expertise": ["经济学", "测试"],
                    "personality": ["理性", "测试"],
                    "communication_style": "测试风格",
                    "core_values": "测试价值观"
                },
                {
                    "agent_id": "test_li_si",
                    "name": "李四",
                    "role": "测试军事专家",
                    "type": "military_leader",
                    "nationality": "英国",
                    "background": "这是另一个测试用的智能体",
                    "expertise": ["军事", "测试"],
                    "personality": ["果断", "测试"],
                    "communication_style": "军事风格",
                    "core_values": "军事价值观"
                }
            ]
            
            success_count = 0
            for agent_data in test_agents:
                agent_id = agent_data.pop("agent_id")
                if storage.add_agent(agent_id, agent_data):
                    success_count += 1
            
            st.success(f"✅ 成功添加 {success_count} 个测试智能体")
            st.rerun()
        
        # 手动添加
        st.markdown("**手动添加智能体**")
        
        with st.form("add_agent_form"):
            name = st.text_input("名称", placeholder="王五")
            role = st.text_input("角色", placeholder="测试专家")
            
            if st.form_submit_button("💾 添加"):
                if name and role:
                    agent_id = f"manual_{name.lower().replace(' ', '_')}"
                    agent_data = {
                        "name": name,
                        "role": role,
                        "type": "custom",
                        "nationality": "测试",
                        "background": "手动添加的测试智能体",
                        "expertise": ["测试"],
                        "personality": ["测试"],
                        "communication_style": "测试风格",
                        "core_values": "测试价值观"
                    }
                    
                    if storage.add_agent(agent_id, agent_data):
                        st.success(f"✅ 智能体 '{name}' 添加成功")
                        st.rerun()
                    else:
                        st.error("❌ 添加失败")
                else:
                    st.warning("⚠️ 请填写名称和角色")
    
    with tab2:
        st.markdown("#### 📋 查看和管理智能体")
        
        all_agents = storage.get_all_agents()
        
        if not all_agents:
            st.info("📝 暂无智能体，请先添加一些测试数据")
        else:
            for agent_id, agent_data in all_agents.items():
                with st.container():
                    col1, col2, col3 = st.columns([3, 1, 1])
                    
                    with col1:
                        st.write(f"**{agent_data['name']}** - {agent_data['role']}")
                        st.caption(f"ID: {agent_id} | 类型: {agent_data.get('type', 'unknown')}")
                    
                    with col2:
                        if st.button("👁️", key=f"view_{agent_id}", help="查看详情"):
                            st.session_state[f"show_details_{agent_id}"] = not st.session_state.get(f"show_details_{agent_id}", False)
                    
                    with col3:
                        if st.button("🗑️", key=f"delete_{agent_id}", help="删除"):
                            if storage.delete_agent(agent_id):
                                st.success(f"✅ 已删除 {agent_data['name']}")
                                st.rerun()
                            else:
                                st.error("❌ 删除失败")
                    
                    # 显示详情
                    if st.session_state.get(f"show_details_{agent_id}", False):
                        with st.expander(f"📄 {agent_data['name']} 详情", expanded=True):
                            st.json(agent_data)
                    
                    st.divider()
    
    with tab3:
        st.markdown("#### 🔄 导入导出功能")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**📤 导出智能体**")
            
            export_filename = st.text_input("导出文件名", value=f"agents_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            if st.button("📤 执行导出"):
                if storage.export_agents(export_filename):
                    st.success(f"✅ 已导出到 {export_filename}")
                else:
                    st.error("❌ 导出失败")
            
            # 备份功能
            if st.button("💾 创建备份"):
                backup_file = storage.backup_agents()
                if backup_file:
                    st.success(f"✅ 备份已创建: {backup_file}")
                else:
                    st.error("❌ 备份失败")
        
        with col2:
            st.markdown("**📥 导入智能体**")
            
            uploaded_file = st.file_uploader("选择导入文件", type=['json'])
            
            if uploaded_file is not None:
                overwrite = st.checkbox("覆盖现有智能体", value=False)
                
                if st.button("📥 执行导入"):
                    try:
                        # 保存上传的文件
                        import tempfile
                        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
                            tmp_file.write(uploaded_file.getvalue().decode('utf-8'))
                            tmp_filename = tmp_file.name
                        
                        # 执行导入
                        imported_count = storage.import_agents(tmp_filename, overwrite)
                        
                        if imported_count > 0:
                            st.success(f"✅ 成功导入 {imported_count} 个智能体")
                            st.rerun()
                        else:
                            st.warning("⚠️ 没有导入任何智能体")
                        
                        # 清理临时文件
                        import os
                        os.unlink(tmp_filename)
                        
                    except Exception as e:
                        st.error(f"❌ 导入失败: {e}")
    
    with tab4:
        st.markdown("#### 🧹 清理测试数据")
        
        st.warning("⚠️ 以下操作将永久删除数据，请谨慎操作")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🗑️ 删除所有测试智能体", type="secondary"):
                all_agents = storage.get_all_agents()
                deleted_count = 0
                
                for agent_id, agent_data in all_agents.items():
                    if "测试" in agent_data.get("role", "") or agent_id.startswith("test_") or agent_id.startswith("manual_"):
                        if storage.delete_agent(agent_id):
                            deleted_count += 1
                
                if deleted_count > 0:
                    st.success(f"✅ 已删除 {deleted_count} 个测试智能体")
                    st.rerun()
                else:
                    st.info("ℹ️ 没有找到测试智能体")
        
        with col2:
            if st.button("💥 清空所有智能体", type="secondary"):
                st.warning("⚠️ 这将删除所有智能体数据！")
                
                if st.button("确认清空", key="confirm_clear_all"):
                    all_agents = list(storage.get_all_agents().keys())
                    deleted_count = 0
                    
                    for agent_id in all_agents:
                        if storage.delete_agent(agent_id):
                            deleted_count += 1
                    
                    st.success(f"✅ 已清空 {deleted_count} 个智能体")
                    st.rerun()
    
    # 性能测试
    st.markdown("### ⚡ 性能测试")
    
    if st.button("🧪 运行性能测试"):
        import time
        
        # 测试添加性能
        start_time = time.time()
        
        test_data = {
            "name": "性能测试智能体",
            "role": "测试专家",
            "type": "test",
            "nationality": "测试",
            "background": "性能测试用智能体",
            "expertise": ["性能测试"],
            "personality": ["高效"],
            "communication_style": "简洁",
            "core_values": "效率至上"
        }
        
        # 添加测试
        add_success = storage.add_agent("performance_test", test_data)
        add_time = time.time() - start_time
        
        # 读取测试
        start_time = time.time()
        read_data = storage.get_agent("performance_test")
        read_time = time.time() - start_time
        
        # 删除测试
        start_time = time.time()
        delete_success = storage.delete_agent("performance_test")
        delete_time = time.time() - start_time
        
        # 显示结果
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("添加耗时", f"{add_time:.4f}秒")
            st.write("✅ 成功" if add_success else "❌ 失败")
        
        with col2:
            st.metric("读取耗时", f"{read_time:.4f}秒")
            st.write("✅ 成功" if read_data else "❌ 失败")
        
        with col3:
            st.metric("删除耗时", f"{delete_time:.4f}秒")
            st.write("✅ 成功" if delete_success else "❌ 失败")

if __name__ == "__main__":
    main()
