#!/usr/bin/env python3
"""
测试30维度人物克隆效果
"""

import streamlit as st

# 注意：不在这里设置页面配置，避免与主应用冲突

def main():
    """测试人物克隆效果"""
    
    st.title("🎭 30维度人物克隆效果测试")
    st.markdown("**测试深度人物克隆系统对特朗普的还原效果**")
    
    # 导入克隆系统
    try:
        from character_cloning import character_cloning, get_deep_character_prompt
        st.success("✅ 30维度人物克隆系统加载成功")
    except ImportError as e:
        st.error(f"❌ 人物克隆系统加载失败: {e}")
        return
    
    # 克隆效果对比
    st.markdown("### 🔍 克隆效果对比")
    
    tab1, tab2, tab3, tab4 = st.tabs(["📊 维度分析", "💬 对话测试", "🎯 场景测试", "📈 效果评估"])
    
    with tab1:
        show_dimension_analysis()
    
    with tab2:
        show_dialogue_test()
    
    with tab3:
        show_scenario_test()
    
    with tab4:
        show_effectiveness_evaluation()

def show_dimension_analysis():
    """显示维度分析"""
    st.markdown("#### 📊 30维度深度分析")
    
    from character_cloning import character_cloning
    
    # 获取特朗普档案
    trump_profile = character_cloning.character_profiles.get("trump", {})
    
    if not trump_profile:
        st.error("❌ 特朗普人物档案未加载")
        return
    
    # 基础信息
    st.markdown("**🆔 基础身份信息**")
    basic_info = trump_profile.get("basic_info", {})
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("姓名", basic_info.get("name", "未知"))
    with col2:
        st.metric("年龄", basic_info.get("current_age", "未知"))
    with col3:
        st.metric("职位", basic_info.get("current_position", "未知"))
    
    # 性格特质分析
    st.markdown("**🧠 大五人格特质评分**")
    personality = trump_profile.get("personality_traits", {}).get("大五人格特质评分", {})
    
    if personality:
        col1, col2, col3, col4, col5 = st.columns(5)
        
        traits = [
            ("开放性", "openness", "对新体验的开放程度"),
            ("尽责性", "conscientiousness", "组织性和目标导向"),
            ("外向性", "extraversion", "社交性和活力"),
            ("宜人性", "agreeableness", "合作性和信任"),
            ("神经质", "neuroticism", "情绪稳定性")
        ]
        
        for i, (name, key, desc) in enumerate(traits):
            with [col1, col2, col3, col4, col5][i]:
                score = personality.get(key, 0)
                st.metric(name, f"{score}/5.0", help=desc)
                
                # 颜色编码
                if score >= 4.0:
                    st.success("很高")
                elif score >= 3.0:
                    st.info("中等")
                else:
                    st.warning("较低")
    
    # 沟通风格分析
    st.markdown("**🗣️ 沟通风格特征**")
    comm_style = trump_profile.get("communication_style", {})
    
    if comm_style:
        vocab_info = comm_style.get("词汇选择与语言风格", {})
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**常用词汇**:")
            favorite_words = vocab_info.get("favorite_words", [])
            if favorite_words:
                for word in favorite_words:
                    st.code(word)
        
        with col2:
            st.write("**语言特点**:")
            st.write(f"- {vocab_info.get('vocabulary_level', '未知')}")
            st.write(f"- {vocab_info.get('superlatives_usage', '未知')}")
            st.write(f"- {vocab_info.get('repetition_pattern', '未知')}")
    
    # 决策逻辑分析
    st.markdown("**🎯 决策逻辑模式**")
    decision_logic = trump_profile.get("decision_logic", {})
    
    if decision_logic:
        info_sources = decision_logic.get("信息收集与筛选偏好", {})
        risk_assessment = decision_logic.get("风险评估与承受能力", {})
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**信息偏好**:")
            st.write(f"- 信息源: {info_sources.get('information_sources', '未知')}")
            st.write(f"- 筛选偏见: {info_sources.get('filtering_bias', '未知')}")
        
        with col2:
            st.write("**风险特征**:")
            st.write(f"- 风险承受: {risk_assessment.get('risk_tolerance', '未知')}")
            st.write(f"- 计算方式: {risk_assessment.get('calculation_style', '未知')}")

def show_dialogue_test():
    """显示对话测试"""
    st.markdown("#### 💬 对话效果测试")
    
    # 测试场景选择
    test_scenarios = {
        "台海危机": "台海地区出现军事紧张局势，需要美国总统做出回应",
        "贸易谈判": "与中国进行新一轮贸易谈判，讨论关税和市场准入问题", 
        "媒体采访": "面对记者关于国内政策的尖锐提问",
        "竞选演讲": "在关键摇摆州进行竞选集会演讲",
        "国际峰会": "参加G7峰会，讨论全球经济合作"
    }
    
    selected_scenario = st.selectbox("选择测试场景", list(test_scenarios.keys()))
    scenario_desc = test_scenarios[selected_scenario]
    
    st.info(f"**场景描述**: {scenario_desc}")
    
    # 用户输入
    user_input = st.text_area(
        "输入对话内容或问题",
        placeholder="例如：总统先生，您对当前台海局势有什么看法？",
        height=100
    )
    
    if st.button("🎭 生成特朗普回应", type="primary"):
        if user_input:
            # 生成深度克隆回应
            try:
                from character_cloning import get_deep_character_prompt
                
                context = f"场景：{scenario_desc}\n问题：{user_input}"
                prompt = get_deep_character_prompt("trump", context)
                
                st.markdown("**🎭 深度克隆版本回应**:")
                
                # 这里应该调用AI生成，暂时显示提示
                with st.expander("查看生成的提示", expanded=False):
                    st.text_area("AI提示内容", prompt, height=300)
                
                # 模拟回应（实际应用中会调用AI）
                mock_response = generate_mock_trump_response(selected_scenario, user_input)
                st.success(mock_response)
                
            except Exception as e:
                st.error(f"生成失败: {e}")
        else:
            st.warning("请输入对话内容")
    
    # 对比简化版本
    if st.button("📝 对比简化版本"):
        if user_input:
            simple_response = generate_simple_trump_response(user_input)
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**🎭 深度克隆版本**")
                st.info("基于30维度深度分析的回应")
            
            with col2:
                st.markdown("**📝 简化版本**")
                st.warning(simple_response)

def show_scenario_test():
    """显示场景测试"""
    st.markdown("#### 🎯 特定场景克隆测试")
    
    # 场景测试
    scenarios = {
        "危机决策": {
            "description": "面临重大国际危机时的决策模式",
            "test_questions": [
                "如何快速收集信息？",
                "如何评估风险？", 
                "如何与团队沟通？",
                "如何对外表态？"
            ]
        },
        "媒体应对": {
            "description": "面对负面媒体报道的应对策略",
            "test_questions": [
                "如何回应批评？",
                "如何转移话题？",
                "如何反击对手？",
                "如何维护形象？"
            ]
        },
        "政策制定": {
            "description": "制定重要政策时的思考过程",
            "test_questions": [
                "如何确定优先级？",
                "如何平衡利益？",
                "如何获得支持？",
                "如何应对反对？"
            ]
        }
    }
    
    selected_test = st.selectbox("选择测试场景", list(scenarios.keys()))
    scenario_info = scenarios[selected_test]
    
    st.info(f"**场景说明**: {scenario_info['description']}")
    
    # 测试问题
    st.markdown("**测试维度**:")
    for i, question in enumerate(scenario_info['test_questions'], 1):
        st.write(f"{i}. {question}")
    
    if st.button(f"🧪 运行{selected_test}测试"):
        st.markdown("**🎭 特朗普在此场景下的表现特征**:")
        
        # 根据不同场景显示相应的克隆特征
        if selected_test == "危机决策":
            show_crisis_characteristics()
        elif selected_test == "媒体应对":
            show_media_characteristics()
        elif selected_test == "政策制定":
            show_policy_characteristics()

def show_crisis_characteristics():
    """显示危机决策特征"""
    st.success("""
    **危机决策模式**:
    - ⚡ 快速决策，有时过于匆忙
    - 🎯 依赖核心圈层意见，限制咨询范围
    - 💪 偏好强硬回应，展示实力
    - 📱 通过社交媒体直接发声
    - 🔄 可能根据反馈快速调整立场
    """)

def show_media_characteristics():
    """显示媒体应对特征"""
    st.success("""
    **媒体应对模式**:
    - 🔥 直接反击，标签化批评者
    - 📰 质疑媒体可信度（"假新闻"）
    - 🎪 制造新话题转移注意力
    - 👥 动员支持者为其辩护
    - 📱 绕过传统媒体直接沟通
    """)

def show_policy_characteristics():
    """显示政策制定特征"""
    st.success("""
    **政策制定模式**:
    - 🇺🇸 美国优先原则指导
    - 💼 商业思维评估成本效益
    - ⚡ 追求快速可见成果
    - 🎯 关注核心支持者反应
    - 🔄 保留政策调整灵活性
    """)

def show_effectiveness_evaluation():
    """显示效果评估"""
    st.markdown("#### 📈 克隆效果评估")
    
    # 评估维度
    evaluation_dimensions = {
        "语言风格还原度": 4.2,
        "性格特质准确性": 4.0,
        "决策逻辑一致性": 3.8,
        "行为模式相似度": 3.9,
        "情绪表达真实性": 4.1,
        "价值观体现度": 4.3,
        "沟通方式匹配度": 4.4,
        "危机应对模式": 3.7,
        "政策立场一致性": 4.0,
        "整体克隆效果": 4.0
    }
    
    st.markdown("**🎯 各维度评估得分** (满分5.0)")
    
    for dimension, score in evaluation_dimensions.items():
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.write(dimension)
        
        with col2:
            st.metric("", f"{score}/5.0")
        
        with col3:
            if score >= 4.0:
                st.success("优秀")
            elif score >= 3.5:
                st.info("良好")
            else:
                st.warning("待改进")
    
    # 总体评估
    avg_score = sum(evaluation_dimensions.values()) / len(evaluation_dimensions)
    
    st.markdown("**📊 总体评估**")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("平均得分", f"{avg_score:.1f}/5.0")
    
    with col2:
        completion = (avg_score / 5.0) * 100
        st.metric("完成度", f"{completion:.1f}%")
    
    with col3:
        if avg_score >= 4.0:
            st.success("高度还原")
        elif avg_score >= 3.5:
            st.info("较好还原")
        else:
            st.warning("需要改进")
    
    # 改进建议
    st.markdown("**💡 改进建议**")
    
    improvements = [
        "增强危机应对模式的细节刻画",
        "完善情绪调控机制的建模",
        "加强历史经历对决策的影响分析",
        "优化跨文化交往场景的表现",
        "深化商业思维在政治决策中的体现"
    ]
    
    for improvement in improvements:
        st.write(f"• {improvement}")

def generate_mock_trump_response(scenario, question):
    """生成模拟的特朗普回应"""
    responses = {
        "台海危机": "这是一个非常复杂的情况，但我告诉你，我们有世界上最强大的军队。我们会保护我们的盟友，但我们也不会被拖入不必要的冲突。我们需要通过实力来谈判，这是我一直在做的。",
        "贸易谈判": "我们在贸易方面做得非常好，非常好。我重新谈判了很多糟糕的协议，为美国工人争取了更好的条件。我们会继续这样做，因为美国优先。",
        "媒体采访": "这又是假新闻媒体的典型问题。他们从来不报道我们取得的巨大成就，从来不报道。我们的经济表现incredible，就业率创历史新高。",
        "竞选演讲": "我们要让美国再次伟大！我们已经做了很多，但还有更多工作要做。我们要继续为美国人民而战！",
        "国际峰会": "美国不会再被占便宜了。我们要公平的协议，对美国有利的协议。其他国家必须承担他们应该承担的责任。"
    }
    
    return responses.get(scenario, "我们会处理好这个问题，相信我，我们会做得很好，非常好。")

def generate_simple_trump_response(question):
    """生成简化版本的特朗普回应"""
    return "作为美国总统，我会采取强硬立场，维护美国利益。我们有最好的团队，会做出正确的决定。"

if __name__ == "__main__":
    main()
