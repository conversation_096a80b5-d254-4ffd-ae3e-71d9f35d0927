#!/usr/bin/env python3
"""
测试聊天功能的简化版本
"""

import streamlit as st
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 设置页面配置
st.set_page_config(
    page_title="聊天测试",
    page_icon="💬",
    layout="wide"
)

def test_ai_import():
    """测试AI模块导入"""
    try:
        from utils.llm_config import llm_config, test_llm_connection, generate_response
        return llm_config, test_llm_connection, generate_response
    except ImportError as e:
        st.error(f"AI模块导入失败: {e}")
        return None, None, None

def main():
    st.title("💬 AI聊天功能测试")
    
    # 测试AI导入
    llm_config, test_llm_connection, generate_response = test_ai_import()
    
    if llm_config is None:
        st.error("❌ AI模块未正确导入")
        st.info("请检查 src/utils/llm_config.py 文件是否存在")
        return
    
    # 显示配置状态
    st.subheader("🔧 AI配置状态")
    
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"API Key: {llm_config.api_key[:20]}..." if llm_config.api_key else "❌ 未配置")
        st.info(f"模型: {llm_config.model_name}")
    
    with col2:
        st.info(f"Base URL: {llm_config.base_url}")
        st.info(f"配置状态: {'✅ 已配置' if llm_config.is_configured() else '❌ 未配置'}")
    
    # 测试连接
    if st.button("🧪 测试AI连接"):
        with st.spinner("测试中..."):
            result = test_llm_connection()
            
            if result['success']:
                st.success("✅ AI连接成功!")
                st.write(f"响应: {result['response']}")
                st.write(f"模型: {result['model']}")
            else:
                st.error("❌ AI连接失败!")
                st.write(f"错误: {result['error']}")
    
    # 聊天测试
    st.subheader("💬 聊天测试")
    
    # 初始化聊天历史
    if 'chat_messages' not in st.session_state:
        st.session_state.chat_messages = []
    
    # 显示聊天历史
    for msg in st.session_state.chat_messages:
        with st.chat_message(msg["role"]):
            st.write(f"**{msg['role']}** - {msg['timestamp']}")
            st.write(msg["content"])
    
    # 用户输入
    user_input = st.chat_input("输入您的消息...")
    
    if user_input:
        # 添加用户消息
        user_msg = {
            "role": "用户",
            "content": user_input,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        st.session_state.chat_messages.append(user_msg)
        
        # 显示用户消息
        with st.chat_message("user"):
            st.write(f"**用户** - {user_msg['timestamp']}")
            st.write(user_input)
        
        # 生成AI回复
        if generate_response:
            with st.spinner("AI思考中..."):
                try:
                    messages = [
                        {"role": "system", "content": "你是一个友好的AI助手，请用中文回复。"},
                        {"role": "user", "content": user_input}
                    ]
                    
                    ai_response = generate_response(messages)
                    
                    # 添加AI消息
                    ai_msg = {
                        "role": "AI助手",
                        "content": ai_response,
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    }
                    st.session_state.chat_messages.append(ai_msg)
                    
                    # 显示AI消息
                    with st.chat_message("assistant"):
                        st.write(f"**AI助手** - {ai_msg['timestamp']}")
                        st.write(ai_response)
                    
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"AI生成失败: {e}")
        else:
            st.error("❌ AI功能不可用")
    
    # 清除聊天
    if st.button("🗑️ 清除聊天记录"):
        st.session_state.chat_messages = []
        st.rerun()

if __name__ == "__main__":
    main()
