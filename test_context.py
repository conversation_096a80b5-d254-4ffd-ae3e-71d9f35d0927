#!/usr/bin/env python3
"""
上下文管理测试页面
"""

import streamlit as st
import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 注意：不在这里设置页面配置，避免与主应用冲突

def main():
    """测试上下文管理"""
    
    st.title("🧠 上下文管理系统测试")
    st.markdown("**测试长对话中的上下文长度限制解决方案**")
    
    # 导入上下文管理器
    try:
        from context_manager import ContextManager, get_context_statistics
        context_manager = ContextManager(max_tokens=4000)
        st.success("✅ 上下文管理器加载成功")
    except ImportError as e:
        st.error(f"❌ 上下文管理器加载失败: {e}")
        return
    
    # 模拟长对话数据
    if 'test_messages' not in st.session_state:
        st.session_state.test_messages = []
    
    # 控制面板
    st.markdown("### 🎮 测试控制面板")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📝 生成测试对话", type="primary"):
            generate_test_conversation()
    
    with col2:
        if st.button("🧠 测试上下文管理"):
            test_context_management()
    
    with col3:
        if st.button("🔄 清空测试数据"):
            st.session_state.test_messages = []
            st.rerun()
    
    # 显示当前统计
    if st.session_state.test_messages:
        st.markdown("### 📊 当前对话统计")
        
        stats = get_context_statistics(st.session_state.test_messages)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("消息总数", stats.get("total_messages", 0))
        
        with col2:
            st.metric("总Token数", stats.get("total_tokens", 0))
        
        with col3:
            ratio = stats.get("estimated_context_ratio", 0)
            st.metric("上下文使用率", f"{ratio:.1%}")
        
        with col4:
            if ratio > 1.0:
                st.error("🚨 超出限制")
            elif ratio > 0.8:
                st.warning("⚠️ 接近限制")
            else:
                st.success("✅ 正常范围")
        
        # 详细统计
        with st.expander("📈 详细统计信息", expanded=False):
            st.json(stats)
    
    # 上下文管理策略测试
    if st.session_state.test_messages:
        st.markdown("### 🔧 上下文管理策略测试")
        
        system_prompt = "你是台海危机应对专家，请分析当前形势。"
        current_prompt = "请基于以上讨论，提出你的最新建议。"
        
        try:
            managed_messages, management_stats = context_manager.manage_context(
                system_prompt, 
                st.session_state.test_messages, 
                current_prompt
            )
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**🎯 管理策略**")
                strategy_names = {
                    "full_history": "完整历史",
                    "sliding_window": "滑动窗口", 
                    "summary_recent": "摘要+最近",
                    "none": "无需管理"
                }
                strategy = management_stats.get("strategy", "none")
                st.info(f"策略: {strategy_names.get(strategy, strategy)}")
                
                st.metric("原始消息数", management_stats.get("total_history_messages", 0))
                st.metric("使用消息数", management_stats.get("used_messages", "N/A"))
                
            with col2:
                st.markdown("**📊 Token使用情况**")
                st.metric("基础Token", management_stats.get("base_tokens", 0))
                st.metric("最终Token", management_stats.get("final_tokens", 0))
                
                usage_ratio = management_stats.get("token_usage_ratio", 0)
                st.metric("Token使用率", f"{usage_ratio:.1%}")
            
            # 显示管理后的消息
            with st.expander("📝 管理后的消息内容", expanded=False):
                for i, msg in enumerate(managed_messages):
                    st.write(f"**{msg['role']}**: {msg['content'][:200]}...")
                    
        except Exception as e:
            st.error(f"上下文管理测试失败: {e}")
    
    # 显示测试对话
    if st.session_state.test_messages:
        st.markdown("### 💬 测试对话内容")
        
        # 分页显示
        page_size = 10
        total_pages = (len(st.session_state.test_messages) + page_size - 1) // page_size
        
        if total_pages > 1:
            page = st.selectbox("选择页面", range(1, total_pages + 1))
            start_idx = (page - 1) * page_size
            end_idx = min(start_idx + page_size, len(st.session_state.test_messages))
            messages_to_show = st.session_state.test_messages[start_idx:end_idx]
        else:
            messages_to_show = st.session_state.test_messages
        
        for i, msg in enumerate(messages_to_show):
            with st.chat_message("assistant"):
                st.write(f"**{msg['speaker']}** - {msg.get('timestamp', '')}")
                st.write(msg['content'])

def generate_test_conversation():
    """生成测试对话"""
    
    agents = ["特朗普", "拜登", "军事专家", "政治专家", "经济专家"]
    
    # 生成多轮对话
    test_messages = []
    
    for round_num in range(1, 6):  # 5轮对话
        for agent in agents:
            content = f"第{round_num}轮发言：我是{agent}，针对台海危机，我认为需要从{agent}的专业角度进行深入分析。" + \
                     f"基于前面{len(test_messages)}条讨论，我提出以下观点：" + \
                     f"1. 当前形势分析；2. 具体应对策略；3. 风险评估；4. 实施建议。" + \
                     f"这是一个复杂的地缘政治问题，需要综合考虑军事、外交、经济等多个维度。" * 3
            
            test_messages.append({
                "speaker": agent,
                "content": content,
                "timestamp": f"19:3{round_num}:{len(test_messages):02d}"
            })
    
    st.session_state.test_messages = test_messages
    st.success(f"✅ 生成了 {len(test_messages)} 条测试对话")
    st.rerun()

def test_context_management():
    """测试上下文管理功能"""
    
    if not st.session_state.test_messages:
        st.warning("请先生成测试对话")
        return
    
    try:
        from context_manager import ContextManager
        
        # 测试不同的token限制
        test_limits = [1000, 2000, 4000, 8000]
        
        st.markdown("### 🧪 不同Token限制下的管理效果")
        
        for limit in test_limits:
            manager = ContextManager(max_tokens=limit)
            
            system_prompt = "你是台海危机专家。"
            current_prompt = "请分析形势。"
            
            managed_messages, stats = manager.manage_context(
                system_prompt,
                st.session_state.test_messages,
                current_prompt
            )
            
            with st.expander(f"Token限制: {limit}", expanded=False):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"策略: {stats.get('strategy', 'none')}")
                    st.write(f"原始消息: {stats.get('total_history_messages', 0)}")
                
                with col2:
                    st.write(f"最终Token: {stats.get('final_tokens', 0)}")
                    st.write(f"使用率: {stats.get('token_usage_ratio', 0):.1%}")
                
                with col3:
                    if stats.get('token_usage_ratio', 0) <= 1.0:
                        st.success("✅ 在限制内")
                    else:
                        st.error("❌ 超出限制")
        
        st.success("✅ 上下文管理测试完成")
        
    except Exception as e:
        st.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
