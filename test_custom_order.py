#!/usr/bin/env python3
"""
测试自定义发言顺序功能
"""

import streamlit as st

# 注意：不在这里设置页面配置，避免与主应用冲突

def main():
    """测试自定义发言顺序"""
    
    st.title("🔄 自定义发言顺序测试")
    st.markdown("**测试智能体发言顺序的自定义和调整功能**")
    
    # 模拟智能体数据
    demo_agents = {
        "trump": {"name": "唐纳德·特朗普", "role": "美国现任总统"},
        "biden": {"name": "乔·拜登", "role": "美国前任总统"},
        "moderator": {"name": "推演导调", "role": "推演主持人"},
        "military_expert": {"name": "军事专家", "role": "军事战略顾问"},
        "political_expert": {"name": "政治专家", "role": "政治战略顾问"}
    }
    
    # 初始化
    if 'test_selected_agents' not in st.session_state:
        st.session_state.test_selected_agents = list(demo_agents.keys())
    
    if 'test_custom_order' not in st.session_state:
        st.session_state.test_custom_order = st.session_state.test_selected_agents.copy()
    
    # 智能体选择
    st.markdown("### 🎭 选择参与智能体")
    
    selected_agents = []
    for agent_id, agent_info in demo_agents.items():
        if st.checkbox(f"{agent_info['name']} ({agent_info['role']})", 
                      value=agent_id in st.session_state.test_selected_agents,
                      key=f"select_{agent_id}"):
            selected_agents.append(agent_id)
    
    # 更新选择
    if selected_agents != st.session_state.test_selected_agents:
        st.session_state.test_selected_agents = selected_agents
        # 更新自定义顺序
        st.session_state.test_custom_order = [
            agent for agent in st.session_state.test_custom_order 
            if agent in selected_agents
        ]
        # 添加新选中的智能体
        for agent in selected_agents:
            if agent not in st.session_state.test_custom_order:
                st.session_state.test_custom_order.append(agent)
        st.rerun()
    
    if not selected_agents:
        st.warning("⚠️ 请至少选择一个智能体")
        return
    
    # 发言顺序设置
    st.markdown("### 🔄 自定义发言顺序")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("**当前发言顺序**:")
        
        # 显示当前顺序并提供调整功能
        for i, agent_id in enumerate(st.session_state.test_custom_order):
            agent_name = demo_agents[agent_id]['name']
            
            col_name, col_up, col_down, col_top = st.columns([3, 1, 1, 1])
            
            with col_name:
                st.write(f"**{i+1}. {agent_name}**")
            
            with col_up:
                if st.button("⬆️", key=f"up_{agent_id}", help="上移", disabled=i==0):
                    # 交换位置
                    st.session_state.test_custom_order[i], st.session_state.test_custom_order[i-1] = \
                        st.session_state.test_custom_order[i-1], st.session_state.test_custom_order[i]
                    st.rerun()
            
            with col_down:
                if st.button("⬇️", key=f"down_{agent_id}", help="下移", 
                           disabled=i==len(st.session_state.test_custom_order)-1):
                    # 交换位置
                    st.session_state.test_custom_order[i], st.session_state.test_custom_order[i+1] = \
                        st.session_state.test_custom_order[i+1], st.session_state.test_custom_order[i]
                    st.rerun()
            
            with col_top:
                if st.button("🔝", key=f"top_{agent_id}", help="置顶", disabled=i==0):
                    # 移动到顶部
                    agent = st.session_state.test_custom_order.pop(i)
                    st.session_state.test_custom_order.insert(0, agent)
                    st.rerun()
    
    with col2:
        st.markdown("**快速操作**:")
        
        if st.button("🎯 重要性排序", help="按重要性排序", use_container_width=True):
            # 按重要性排序：总统 → 导调 → 专家
            priority_order = ["trump", "biden", "moderator", "military_expert", "political_expert"]
            sorted_agents = []
            
            # 先添加优先级高的
            for priority_agent in priority_order:
                if priority_agent in st.session_state.test_custom_order:
                    sorted_agents.append(priority_agent)
            
            # 再添加其他的
            for agent in st.session_state.test_custom_order:
                if agent not in sorted_agents:
                    sorted_agents.append(agent)
            
            st.session_state.test_custom_order = sorted_agents
            st.rerun()
        
        if st.button("🔀 随机排序", help="随机打乱顺序", use_container_width=True):
            import random
            random.shuffle(st.session_state.test_custom_order)
            st.rerun()
        
        if st.button("🔄 重置顺序", help="重置为默认顺序", use_container_width=True):
            st.session_state.test_custom_order = st.session_state.test_selected_agents.copy()
            st.rerun()
        
        if st.button("🔁 反向排序", help="反转当前顺序", use_container_width=True):
            st.session_state.test_custom_order.reverse()
            st.rerun()
    
    # 模拟发言测试
    st.markdown("### 🎤 模拟发言测试")
    
    if 'test_round' not in st.session_state:
        st.session_state.test_round = 0
    
    if 'test_messages' not in st.session_state:
        st.session_state.test_messages = []
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("▶️ 下一位发言", type="primary"):
            # 模拟下一位发言
            current_speaker_index = st.session_state.test_round % len(st.session_state.test_custom_order)
            current_speaker = st.session_state.test_custom_order[current_speaker_index]
            speaker_name = demo_agents[current_speaker]['name']
            
            # 添加模拟发言
            message = f"我是{speaker_name}，这是我的第{(st.session_state.test_round // len(st.session_state.test_custom_order)) + 1}轮发言。"
            st.session_state.test_messages.append({
                "round": st.session_state.test_round + 1,
                "speaker": current_speaker,
                "speaker_name": speaker_name,
                "content": message
            })
            
            st.session_state.test_round += 1
            st.rerun()
    
    with col2:
        if st.button("🔄 重置测试"):
            st.session_state.test_round = 0
            st.session_state.test_messages = []
            st.rerun()
    
    with col3:
        st.metric("当前轮次", st.session_state.test_round)
    
    # 显示发言记录
    if st.session_state.test_messages:
        st.markdown("### 💬 发言记录")
        
        for msg in st.session_state.test_messages[-10:]:  # 显示最近10条
            with st.chat_message("assistant"):
                st.write(f"**第{msg['round']}轮 - {msg['speaker_name']}**")
                st.write(msg['content'])
    
    # 顺序验证
    st.markdown("### ✅ 顺序验证")
    
    if st.session_state.test_messages:
        # 分析发言顺序是否正确
        expected_order = []
        actual_order = []
        
        for i, msg in enumerate(st.session_state.test_messages):
            expected_speaker_index = i % len(st.session_state.test_custom_order)
            expected_speaker = st.session_state.test_custom_order[expected_speaker_index]
            expected_order.append(demo_agents[expected_speaker]['name'])
            actual_order.append(msg['speaker_name'])
        
        # 检查是否匹配
        order_correct = expected_order == actual_order
        
        if order_correct:
            st.success("✅ 发言顺序完全正确！")
        else:
            st.error("❌ 发言顺序有误")
            
            # 显示对比
            with st.expander("🔍 顺序对比", expanded=True):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**预期顺序**:")
                    for i, name in enumerate(expected_order):
                        st.write(f"{i+1}. {name}")
                
                with col2:
                    st.write("**实际顺序**:")
                    for i, name in enumerate(actual_order):
                        color = "🟢" if i < len(expected_order) and name == expected_order[i] else "🔴"
                        st.write(f"{color} {i+1}. {name}")
    
    # 使用说明
    st.markdown("### 📝 使用说明")
    
    with st.expander("💡 如何使用自定义顺序", expanded=False):
        st.markdown("""
        **调整顺序的方法**:
        1. **⬆️ 上移**: 将智能体向上移动一位
        2. **⬇️ 下移**: 将智能体向下移动一位  
        3. **🔝 置顶**: 将智能体移动到第一位
        
        **快速排序选项**:
        - **🎯 重要性排序**: 按照总统→导调→专家的重要性排序
        - **🔀 随机排序**: 随机打乱发言顺序
        - **🔄 重置顺序**: 恢复到默认选择顺序
        - **🔁 反向排序**: 将当前顺序完全反转
        
        **测试功能**:
        - 点击"▶️ 下一位发言"模拟按顺序发言
        - 系统会验证发言顺序是否正确
        - 可以重置测试重新开始
        """)

if __name__ == "__main__":
    main()
