#!/usr/bin/env python3
"""
测试数据存储功能
"""

import streamlit as st
from datetime import datetime

# 注意：不在这里设置页面配置，避免与主应用冲突

def main():
    """测试数据存储功能"""
    
    st.title("💾 数据存储功能测试")
    st.markdown("**测试所有数据的本地存储和CRUD操作**")
    
    # 测试存储模块
    try:
        from data_storage import get_data_storage, DataStorage
        storage = get_data_storage()
        st.success("✅ 数据存储模块加载成功")
    except ImportError as e:
        st.error(f"❌ 存储模块加载失败: {e}")
        return
    
    # 存储状态总览
    st.markdown("### 📊 存储状态总览")
    
    storage_info = storage.get_storage_info()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("自定义场景", storage_info["files"]["scenarios"]["items"])
    
    with col2:
        st.metric("推演记录", storage_info["files"]["simulations"]["items"])
    
    with col3:
        st.metric("推演配置", storage_info["files"]["configs"]["items"])
    
    with col4:
        total_size = sum(f["size"] for f in storage_info["files"].values())
        st.metric("总存储", f"{total_size} 字节")
    
    # 功能测试
    st.markdown("### 🧪 功能测试")
    
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📋 场景测试", "🎬 推演测试", "⚙️ 配置测试", "🔄 导入导出", "🧹 清理测试"])
    
    with tab1:
        test_scenarios_storage(storage)
    
    with tab2:
        test_simulations_storage(storage)
    
    with tab3:
        test_configs_storage(storage)
    
    with tab4:
        test_import_export(storage)
    
    with tab5:
        test_cleanup(storage)

def test_scenarios_storage(storage):
    """测试场景存储"""
    st.markdown("#### 📋 自定义场景存储测试")
    
    # 添加测试场景
    if st.button("➕ 添加测试场景", type="primary"):
        test_scenarios = [
            {
                "scenario_id": "test_scenario_1",
                "title": "测试场景1",
                "type": "crisis",
                "difficulty": "medium",
                "context": "这是一个测试用的危机场景",
                "objectives": ["测试目标1", "测试目标2"],
                "background": "测试背景信息"
            },
            {
                "scenario_id": "test_scenario_2", 
                "title": "测试场景2",
                "type": "negotiation",
                "difficulty": "hard",
                "context": "这是一个测试用的谈判场景",
                "objectives": ["测试目标A", "测试目标B"],
                "background": "测试背景信息B"
            }
        ]
        
        success_count = 0
        for scenario_data in test_scenarios:
            scenario_id = scenario_data.pop("scenario_id")
            if storage.add_scenario(scenario_id, scenario_data):
                success_count += 1
        
        st.success(f"✅ 成功添加 {success_count} 个测试场景")
        st.rerun()
    
    # 显示现有场景
    scenarios = storage.get_scenarios()
    
    if scenarios:
        st.markdown("**现有场景**:")
        for scenario_id, scenario_data in scenarios.items():
            with st.container():
                col1, col2, col3 = st.columns([3, 1, 1])
                
                with col1:
                    st.write(f"**{scenario_data['title']}**")
                    st.caption(f"ID: {scenario_id} | 类型: {scenario_data.get('type', 'unknown')}")
                
                with col2:
                    if st.button("👁️", key=f"view_s_{scenario_id}", help="查看"):
                        st.json(scenario_data)
                
                with col3:
                    if st.button("🗑️", key=f"del_s_{scenario_id}", help="删除"):
                        if storage.delete_scenario(scenario_id):
                            st.success("✅ 已删除")
                            st.rerun()
                
                st.divider()

def test_simulations_storage(storage):
    """测试推演记录存储"""
    st.markdown("#### 🎬 推演记录存储测试")
    
    # 添加测试推演记录
    if st.button("➕ 添加测试推演记录", type="primary"):
        test_simulation = {
            "scenario": {
                "title": "测试推演场景",
                "type": "crisis",
                "context": "测试推演的场景背景"
            },
            "agents": ["trump", "biden", "military_expert"],
            "messages": [
                {
                    "speaker": "trump",
                    "content": "这是特朗普的测试发言",
                    "timestamp": "12:00:00"
                },
                {
                    "speaker": "biden", 
                    "content": "这是拜登的测试发言",
                    "timestamp": "12:01:00"
                },
                {
                    "speaker": "military_expert",
                    "content": "这是军事专家的测试发言",
                    "timestamp": "12:02:00"
                }
            ],
            "config": {
                "max_rounds": 4,
                "turn_order": "默认顺序"
            },
            "current_round": 3,
            "phase": "discussion"
        }
        
        sim_id = f"test_simulation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if storage.save_simulation(sim_id, test_simulation):
            st.success("✅ 测试推演记录添加成功")
            st.rerun()
        else:
            st.error("❌ 添加失败")
    
    # 显示现有推演记录
    simulations = storage.get_simulations()
    
    if simulations:
        st.markdown("**现有推演记录**:")
        for sim_id, sim_data in simulations.items():
            with st.container():
                col1, col2, col3 = st.columns([3, 1, 1])
                
                with col1:
                    scenario_title = sim_data.get('scenario', {}).get('title', '未知')
                    st.write(f"**{scenario_title}**")
                    
                    message_count = len(sim_data.get('messages', []))
                    st.caption(f"ID: {sim_id} | 消息: {message_count} 条")
                
                with col2:
                    if st.button("👁️", key=f"view_sim_{sim_id}", help="查看"):
                        st.json(sim_data)
                
                with col3:
                    if st.button("🗑️", key=f"del_sim_{sim_id}", help="删除"):
                        if storage.delete_simulation(sim_id):
                            st.success("✅ 已删除")
                            st.rerun()
                
                st.divider()

def test_configs_storage(storage):
    """测试配置存储"""
    st.markdown("#### ⚙️ 推演配置存储测试")
    
    # 添加测试配置
    if st.button("➕ 添加测试配置", type="primary"):
        test_config = {
            "scenario": {
                "title": "测试配置场景",
                "type": "crisis"
            },
            "selected_agents": ["trump", "biden", "military_expert", "political_expert"],
            "turn_order": "自定义顺序",
            "max_rounds": 5,
            "round_time": 120,
            "enable_user": True,
            "enable_stream": True
        }
        
        config_id = f"test_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if storage.save_config(config_id, test_config):
            st.success("✅ 测试配置添加成功")
            st.rerun()
        else:
            st.error("❌ 添加失败")
    
    # 显示现有配置
    configs = storage.get_configs()
    
    if configs:
        st.markdown("**现有配置**:")
        for config_id, config_data in configs.items():
            with st.container():
                col1, col2, col3 = st.columns([3, 1, 1])
                
                with col1:
                    scenario_title = config_data.get('scenario', {}).get('title', '未知')
                    st.write(f"**{scenario_title} 配置**")
                    
                    agent_count = len(config_data.get('selected_agents', []))
                    st.caption(f"ID: {config_id} | 智能体: {agent_count} 个")
                
                with col2:
                    if st.button("👁️", key=f"view_cfg_{config_id}", help="查看"):
                        st.json(config_data)
                
                with col3:
                    if st.button("🗑️", key=f"del_cfg_{config_id}", help="删除"):
                        if storage.delete_config(config_id):
                            st.success("✅ 已删除")
                            st.rerun()
                
                st.divider()

def test_import_export(storage):
    """测试导入导出"""
    st.markdown("#### 🔄 导入导出测试")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**📤 导出测试**")
        
        export_file = st.text_input("导出文件名", value=f"test_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        if st.button("📤 执行导出"):
            if storage.export_all_data(export_file):
                st.success(f"✅ 已导出到 {export_file}")
            else:
                st.error("❌ 导出失败")
    
    with col2:
        st.markdown("**📥 导入测试**")
        
        uploaded_file = st.file_uploader("选择导入文件", type=['json'])
        
        if uploaded_file is not None:
            overwrite = st.checkbox("覆盖现有数据", value=False)
            
            if st.button("📥 执行导入"):
                try:
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
                        tmp_file.write(uploaded_file.getvalue().decode('utf-8'))
                        tmp_filename = tmp_file.name
                    
                    if storage.import_all_data(tmp_filename, overwrite):
                        st.success("✅ 导入成功")
                        st.rerun()
                    else:
                        st.error("❌ 导入失败")
                    
                    import os
                    os.unlink(tmp_filename)
                    
                except Exception as e:
                    st.error(f"❌ 导入失败: {e}")

def test_cleanup(storage):
    """测试清理功能"""
    st.markdown("#### 🧹 清理功能测试")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**🧹 清理旧数据**")
        
        days = st.number_input("清理天数", min_value=1, max_value=365, value=1)
        
        if st.button("🧹 清理旧数据"):
            cleaned_count = storage.cleanup_old_data(days)
            if cleaned_count > 0:
                st.success(f"✅ 已清理 {cleaned_count} 条记录")
            else:
                st.info("ℹ️ 没有找到需要清理的数据")
    
    with col2:
        st.markdown("**🗑️ 清理测试数据**")
        
        if st.button("🗑️ 删除所有测试数据"):
            # 删除测试场景
            scenarios = storage.get_scenarios()
            deleted_scenarios = 0
            for scenario_id in list(scenarios.keys()):
                if "test_scenario" in scenario_id:
                    if storage.delete_scenario(scenario_id):
                        deleted_scenarios += 1
            
            # 删除测试推演
            simulations = storage.get_simulations()
            deleted_simulations = 0
            for sim_id in list(simulations.keys()):
                if "test_simulation" in sim_id:
                    if storage.delete_simulation(sim_id):
                        deleted_simulations += 1
            
            # 删除测试配置
            configs = storage.get_configs()
            deleted_configs = 0
            for config_id in list(configs.keys()):
                if "test_config" in config_id:
                    if storage.delete_config(config_id):
                        deleted_configs += 1
            
            st.success(f"✅ 已删除 {deleted_scenarios} 个场景, {deleted_simulations} 个推演, {deleted_configs} 个配置")
            st.rerun()
    
    # 存储详情
    st.markdown("**📋 存储详情**")
    
    storage_info = storage.get_storage_info()
    
    with st.expander("查看详细信息", expanded=False):
        st.json(storage_info)

if __name__ == "__main__":
    main()
