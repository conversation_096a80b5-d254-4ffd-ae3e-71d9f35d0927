#!/usr/bin/env python3
"""
测试动态提示词生成功能
"""

import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_dynamic_prompts():
    """测试动态提示词生成"""
    
    # 模拟不同的场景
    test_scenarios = [
        {
            "title": "台海危机应对策略",
            "context": "情报显示台海周边军事活动增加，美国应如何应对？"
        },
        {
            "title": "中美贸易战升级",
            "context": "中国对美国农产品征收新关税，美国应如何回应？"
        },
        {
            "title": "中东军事干预决策",
            "context": "盟友国家请求美国在地区冲突中提供军事支持，美国是否应该干预？"
        },
        {
            "title": "网络安全威胁应对",
            "context": "发现大规模网络攻击，疑似来自敌对国家，美国应如何应对？"
        }
    ]
    
    # 测试智能体
    test_agents = ["trump", "biden", "military_expert", "political_expert", "moderator"]
    
    print("🧪 开始测试动态提示词生成功能\n")
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"📋 场景 {i}: {scenario['title']}")
        print(f"背景: {scenario['context']}")
        print("-" * 80)
        
        # 测试动态生成智能体提示词
        def generate_agent_prompt(agent_id, scenario_title, scenario_context):
            """动态生成智能体提示词"""
            
            agent_prompts = {
                "trump": f"""你是唐纳德·特朗普，美国第47任总统，现任总统，智囊团最高决策者。

你的特点：
- 直接、强硬的表达方式，商人思维
- 强调"美国优先"，重视实力展示
- 会直接质疑或支持其他人的观点
- 倾向于强硬立场，但考虑成本效益

在关于"{scenario_title}"的智囊团讨论中，你要：
1. 明确回应其他成员的观点（赞同/反对）
2. 从美国利益角度质疑不合理建议
3. 提出强硬但实用的解决方案
4. 与其他观点进行直接辩论

背景情况：{scenario_context}

请积极参与辩论，不要只是重复立场。""",

                "biden": f"""你是乔·拜登，美国第46任总统，前任总统，智囊团资深顾问。

你的特点：
- 温和外交导向，重视盟友合作
- 经验丰富，善于平衡各方观点
- 会寻求妥协和共识
- 强调多边主义和国际法

在关于"{scenario_title}"的智囊团讨论中，你要：
1. 分析其他成员观点的优缺点
2. 提出平衡各方利益的方案
3. 强调外交途径和国际合作
4. 对过于激进的建议提出温和反对

背景情况：{scenario_context}

请积极参与辩论，寻求各方平衡。""",

                "military_expert": f"""你是军事战略专家，五角大楼高级顾问。

你的特点：
- 专业军事分析，数据驱动
- 关注作战可行性和风险评估
- 会用专业知识质疑不现实的军事建议
- 提供具体的军事选项和后果分析

在关于"{scenario_title}"的智囊团讨论中，你要：
1. 从军事角度评估其他人的建议
2. 指出军事行动的风险和代价
3. 提供专业的作战能力分析
4. 对不切实际的军事方案提出质疑

背景情况：{scenario_context}

请用专业军事知识参与辩论。""",

                "political_expert": f"""你是政治学专家，资深分析师。

你的特点：
- 深入的政治分析
- 考虑国内外政治影响
- 平衡多方利益
- 学术性的表达
- 基于历史和理论的分析

在关于"{scenario_title}"的智囊团讨论中，你要：
1. 从政治学角度分析当前形势
2. 评估各种方案的政治可行性
3. 考虑国内外政治反应
4. 提供基于历史经验的建议

背景情况：{scenario_context}

请从政治专家角度分析。""",

                "moderator": f"""你是推演主持人，专业的战略分析师。

在关于"{scenario_title}"的智囊团辩论中，你的职责：
- 客观总结各方观点和分歧
- 指出讨论中的盲点和遗漏
- 提出深入的战略问题
- 推动讨论向更深层次发展
- 确保所有重要因素都被考虑

背景情况：{scenario_context}

请以专业主持人的角度参与讨论。"""
            }
            
            return agent_prompts.get(agent_id, f"""你是{agent_id}，请根据当前讨论的"{scenario_title}"场景进行分析。
            
背景情况：{scenario_context}

请提供专业的分析和建议。""")
        
        # 测试决策问题生成
        def generate_decision_questions(scenario_title, scenario_context):
            """根据场景动态生成决策问题"""
            
            if "台海" in scenario_title or "台湾" in scenario_title:
                return [
                    f"美国是否应该军事干预{scenario_title.replace('应对策略', '').replace('危机', '')}？",
                    "如果干预，采取什么具体方式和程度？",
                    "与盟友的协调策略是什么？",
                    "经济和外交手段如何配合？",
                    "预期结果和风险评估"
                ]
            elif "贸易" in scenario_title:
                return [
                    "是否应该实施新的贸易制裁措施？",
                    "如何平衡经济利益和政治目标？",
                    "与其他贸易伙伴的协调策略？",
                    "对国内经济的影响评估？",
                    "长期贸易政策调整方向？"
                ]
            elif "中东" in scenario_title or "军事干预" in scenario_title:
                return [
                    "是否应该进行军事干预？",
                    "军事行动的规模和持续时间？",
                    "与地区盟友的合作方式？",
                    "人道主义考虑和国际法约束？",
                    "撤出策略和后续安排？"
                ]
            else:
                return [
                    "应该采取什么主要行动？",
                    "行动的具体实施方式和时间表？",
                    "需要哪些资源和支持？",
                    "潜在风险和应对措施？",
                    "预期结果和成功标准？"
                ]
        
        # 测试一个智能体的提示词生成
        agent_id = "trump"
        prompt = generate_agent_prompt(agent_id, scenario['title'], scenario['context'])
        print(f"🤖 {agent_id} 的动态提示词:")
        print(prompt[:200] + "..." if len(prompt) > 200 else prompt)
        print()
        
        # 测试决策问题生成
        questions = generate_decision_questions(scenario['title'], scenario['context'])
        print(f"⚖️ 动态生成的决策问题:")
        for j, question in enumerate(questions, 1):
            print(f"  {j}. {question}")
        print()
        print("=" * 80)
        print()

if __name__ == "__main__":
    test_dynamic_prompts()
