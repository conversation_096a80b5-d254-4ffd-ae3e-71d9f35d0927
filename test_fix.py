#!/usr/bin/env python3
"""
测试修复后的应用
"""

import streamlit as st
import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

st.set_page_config(page_title="修复测试", page_icon="🔧", layout="wide")

def main():
    """测试修复"""
    
    st.title("🔧 修复测试")
    st.markdown("测试 demo_agents 变量修复")
    
    # 模拟智能体数据
    demo_agents = {
        "trump": {
            "name": "唐纳德·特朗普",
            "role": "美国前总统"
        },
        "biden": {
            "name": "乔·拜登", 
            "role": "美国现任总统"
        },
        "military_expert": {
            "name": "军事专家",
            "role": "军事战略顾问"
        }
    }
    
    # 模拟场景
    scenario = {
        "title": "台海危机应对策略",
        "context": "台海局势升级，需要制定应对策略"
    }
    
    # 模拟消息
    test_messages = [
        {"speaker": "trump", "content": "我们需要展现实力"},
        {"speaker": "biden", "content": "外交途径同样重要"},
        {"speaker": "military_expert", "content": "需要评估军事风险"}
    ]
    
    st.markdown("### 🧪 测试结果")
    
    # 测试1: 导入主应用模块
    try:
        import demo_app
        st.success("✅ demo_app 模块导入成功")
    except Exception as e:
        st.error(f"❌ demo_app 模块导入失败: {e}")
        return
    
    # 测试2: 测试generate_agent_response函数
    try:
        response = demo_app.generate_agent_response(
            "trump",
            demo_agents["trump"],
            scenario,
            "请分析当前形势",
            test_messages,
            stream=False
        )
        st.success("✅ generate_agent_response 函数正常")
        st.info(f"测试回应: {response[:100]}...")
    except Exception as e:
        st.error(f"❌ generate_agent_response 函数错误: {e}")
    
    # 测试3: 测试stream_agent_response_simple函数
    try:
        with st.expander("测试流式回应"):
            response = demo_app.stream_agent_response_simple(
                "biden",
                demo_agents["biden"],
                scenario,
                "请回应特朗普的观点",
                test_messages
            )
        st.success("✅ stream_agent_response_simple 函数正常")
    except Exception as e:
        st.error(f"❌ stream_agent_response_simple 函数错误: {e}")
    
    st.markdown("### 📊 修复总结")
    st.info("""
    **修复内容**:
    1. 修复了 generate_agent_response 函数中 demo_agents 未定义错误
    2. 修复了 handle_user_agent_input 函数中的类似问题
    3. 直接使用 msg["speaker"] 而不是通过 demo_agents 查找名称
    
    **现在应该可以正常启动主应用了！**
    """)
    
    if st.button("🚀 启动主应用"):
        st.info("请在命令行中运行: streamlit run demo_app.py")

if __name__ == "__main__":
    main()
