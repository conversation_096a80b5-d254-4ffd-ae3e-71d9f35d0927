#!/usr/bin/env python3
"""
测试智能体知识库功能
"""

import streamlit as st

# 注意：不在这里设置页面配置，避免与主应用冲突

def main():
    """测试知识库功能"""
    
    st.title("📚 智能体知识库功能测试")
    st.markdown("**测试为智能体配置专属知识库的功能**")
    
    # 测试知识库管理器
    try:
        from knowledge_base import get_knowledge_base
        from knowledge_ui import show_agent_knowledge_panel, get_agent_knowledge_context
        
        kb = get_knowledge_base()
        st.success("✅ 知识库模块加载成功")
        
    except ImportError as e:
        st.error(f"❌ 知识库模块加载失败: {e}")
        return
    
    # 测试智能体
    test_agents = {
        "trump": "特朗普",
        "biden": "拜登",
        "military_expert": "军事专家",
        "political_expert": "政治专家"
    }
    
    # 功能测试
    st.markdown("### 🧪 功能测试")
    
    tab1, tab2, tab3 = st.tabs(["📤 上传测试", "📋 管理测试", "🎯 应用测试"])
    
    with tab1:
        st.markdown("#### 📤 知识库上传测试")
        
        # 选择测试智能体
        test_agent = st.selectbox("选择测试智能体", list(test_agents.keys()), 
                                format_func=lambda x: test_agents[x])
        
        # 快速添加测试数据
        if st.button("🚀 添加测试知识", type="primary"):
            test_knowledge = {
                "trump": {
                    "file_name": "商业谈判策略.txt",
                    "content": """商业谈判的核心原则：
1. 信息优势：掌握对方的底线和需求
2. 时间压力：利用时间窗口创造优势
3. 多方案准备：永远有备选方案
4. 强势开局：从有利位置开始谈判
5. 适时妥协：知道何时让步以达成交易

在国际事务中，这些原则同样适用。美国应该从实力地位出发，明确自己的核心利益，同时准备多种应对方案。"""
                },
                "biden": {
                    "file_name": "多边外交经验.txt", 
                    "content": """多边外交的关键要素：
1. 盟友协调：与传统盟友保持密切沟通
2. 国际法框架：在现有国际法体系内寻求解决方案
3. 渐进式缓解：通过对话逐步降低紧张局势
4. 经济手段：利用经济相互依存关系
5. 人道主义考量：关注平民和人道主义影响

历史经验表明，通过耐心的外交努力和国际合作，可以避免军事冲突，实现和平解决争端。"""
                },
                "military_expert": {
                    "file_name": "台海军事态势分析.txt",
                    "content": """台海地区军事态势要点：
1. 地理优势：台湾海峡的地理特征对两岸军事行动的影响
2. 装备对比：双方主要军事装备和技术水平分析
3. 作战模式：可能的军事行动模式和应对策略
4. 国际因素：美军介入的可能性和影响
5. 风险评估：军事冲突的潜在后果和风险

任何军事行动都必须考虑政治目标、国际反应和长期后果。"""
                },
                "political_expert": {
                    "file_name": "国际法与台海问题.txt",
                    "content": """国际法视角下的台海问题：
1. 联合国宪章：关于使用武力的限制
2. 国际海洋法：台湾海峡的法律地位
3. 历史先例：类似争端的国际法解决方案
4. 国际制裁：可能的国际法律后果
5. 和平解决：国际法鼓励的争端解决机制

国际社会普遍支持通过和平方式解决争端，任何单方面改变现状的行为都可能面临国际法律后果。"""
                }
            }
            
            if test_agent in test_knowledge:
                data = test_knowledge[test_agent]
                success = kb.upload_knowledge_file(
                    test_agent, 
                    data["content"], 
                    data["file_name"], 
                    "测试数据"
                )
                
                if success:
                    st.success(f"✅ 已为{test_agents[test_agent]}添加测试知识")
                    st.rerun()
                else:
                    st.error("❌ 添加失败")
        
        # 显示智能体知识库面板
        if test_agent:
            show_agent_knowledge_panel(test_agent, test_agents[test_agent])
    
    with tab2:
        st.markdown("#### 📋 知识库管理测试")
        
        # 显示所有智能体状态
        status = kb.get_all_agents_status()
        
        for agent_id, info in status.items():
            with st.container():
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    status_icon = "🟢" if info["file_count"] > 0 else "⚪"
                    st.write(f"{status_icon} **{info['name']}**")
                    st.caption(info['summary'])
                
                with col2:
                    st.metric("文件数", info["file_count"])
                
                with col3:
                    st.metric("内容量", f"{info['total_size']} 字符")
                
                # 显示文件列表
                files = kb.get_agent_knowledge(agent_id)
                if files:
                    with st.expander(f"📁 {info['name']}的文件", expanded=False):
                        for file_info in files:
                            st.write(f"📄 {file_info['file_name']}")
                            st.caption(f"类型: {file_info['file_type']} | 大小: {file_info['size']} 字符")
                            
                            if st.button(f"🗑️ 删除", key=f"del_{agent_id}_{file_info['file_id']}"):
                                if kb.delete_knowledge_file(agent_id, file_info['file_id']):
                                    st.success("✅ 已删除")
                                    st.rerun()
                
                st.divider()
    
    with tab3:
        st.markdown("#### 🎯 知识库应用测试")
        
        # 测试知识库上下文生成
        test_scenario = "台海危机应对策略分析"
        
        st.write(f"**测试场景**: {test_scenario}")
        
        for agent_id, agent_name in test_agents.items():
            with st.expander(f"🧠 {agent_name}的知识库上下文", expanded=False):
                context = get_agent_knowledge_context(agent_id, test_scenario)
                
                if context:
                    st.text_area(
                        f"{agent_name}的专属知识库上下文",
                        context,
                        height=200,
                        key=f"context_{agent_id}"
                    )
                else:
                    st.info(f"{agent_name}暂无相关知识库内容")
    
    # 性能测试
    st.markdown("### ⚡ 性能测试")
    
    if st.button("🧪 运行性能测试"):
        import time
        
        # 测试知识库读写性能
        start_time = time.time()
        
        # 写入测试
        test_content = "这是一个性能测试文件" * 100
        kb.upload_knowledge_file("trump", test_content, "性能测试.txt", "测试")
        
        # 读取测试
        files = kb.get_agent_knowledge("trump")
        for file_info in files:
            if "性能测试" in file_info['file_name']:
                content = kb.read_knowledge_file("trump", file_info['file_id'])
                break
        
        # 删除测试
        for file_info in files:
            if "性能测试" in file_info['file_name']:
                kb.delete_knowledge_file("trump", file_info['file_id'])
                break
        
        end_time = time.time()
        
        st.success(f"✅ 性能测试完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 集成测试
    st.markdown("### 🔗 集成测试")
    
    if st.button("🧪 测试与主应用集成"):
        st.info("💡 请在主应用中测试以下功能：")
        st.markdown("""
        1. **智能体选择页面**: 查看知识库状态显示
        2. **知识库管理页面**: 测试上传、查看、删除功能
        3. **推演过程**: 验证AI是否使用了知识库内容
        4. **上下文管理**: 检查知识库是否正确集成到AI提示中
        """)
        
        # 显示集成状态
        try:
            # 测试知识库在AI生成中的使用
            context = get_agent_knowledge_context("trump", "台海危机")
            if context:
                st.success("✅ 知识库上下文生成正常")
                st.text_area("生成的上下文示例", context[:500] + "...", height=100)
            else:
                st.warning("⚠️ 特朗普暂无相关知识库内容")

            # 测试文件系统
            import os
            kb_dir = "knowledge_bases"
            if os.path.exists(kb_dir):
                st.success("✅ 知识库目录存在")

                # 统计文件数量
                total_files = 0
                for agent_dir in os.listdir(kb_dir):
                    agent_path = os.path.join(kb_dir, agent_dir)
                    if os.path.isdir(agent_path):
                        files = [f for f in os.listdir(agent_path) if f.endswith('.txt')]
                        total_files += len(files)

                st.info(f"📁 知识库中共有 {total_files} 个文件")
            else:
                st.info("📁 知识库目录将在首次使用时创建")

        except Exception as e:
            st.error(f"❌ 集成测试失败: {e}")
            st.info("💡 这可能是正常的，请在主应用中测试完整功能")
    
    # 使用说明
    st.markdown("### 📖 使用说明")
    
    with st.expander("💡 知识库功能说明", expanded=False):
        st.markdown("""
        **知识库系统架构**:
        
        1. **📁 存储结构**: 
           - `knowledge_bases/` 目录存储所有知识库
           - 每个智能体有独立的子目录
           - `metadata.json` 存储文件元信息
        
        2. **🔧 核心功能**:
           - 文件上传和管理
           - 内容摘要生成
           - 上下文构建
           - 相关性筛选
        
        3. **🎯 AI集成**:
           - 自动加载相关知识库内容
           - 集成到AI提示系统
           - 提升回复的专业性和准确性
        
        4. **📊 管理功能**:
           - 实时状态监控
           - 文件增删改查
           - 批量操作支持
        """)

if __name__ == "__main__":
    main()
