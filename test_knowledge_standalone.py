#!/usr/bin/env python3
"""
独立的知识库功能测试页面
"""

import streamlit as st

# 设置页面配置（独立运行时）
st.set_page_config(page_title="知识库功能测试", page_icon="📚", layout="wide")

def main():
    """测试知识库功能"""
    
    st.title("📚 智能体知识库功能测试")
    st.markdown("**独立测试智能体知识库功能**")
    
    # 测试知识库管理器
    try:
        from knowledge_base import get_knowledge_base
        from knowledge_ui import show_agent_knowledge_panel, get_agent_knowledge_context
        
        kb = get_knowledge_base()
        st.success("✅ 知识库模块加载成功")
        
    except ImportError as e:
        st.error(f"❌ 知识库模块加载失败: {e}")
        st.info("💡 请确保 knowledge_base.py 和 knowledge_ui.py 文件存在")
        return
    
    # 快速功能演示
    st.markdown("### 🚀 快速功能演示")
    
    if st.button("🎯 添加演示数据", type="primary"):
        # 为每个智能体添加演示数据
        demo_data = {
            "trump": {
                "file_name": "商业谈判策略.txt",
                "content": """商业谈判核心原则：
1. 信息优势：掌握对方底线
2. 时间压力：利用时间窗口
3. 多方案准备：永远有备选
4. 强势开局：从有利位置开始
5. 适时妥协：知道何时让步

在台海问题上，美国应该从实力地位出发，明确核心利益，准备多种应对方案。"""
            },
            "biden": {
                "file_name": "多边外交经验.txt",
                "content": """多边外交关键要素：
1. 盟友协调：与传统盟友密切沟通
2. 国际法框架：在现有体系内寻求解决
3. 渐进缓解：通过对话降低紧张
4. 经济手段：利用相互依存关系
5. 人道考量：关注平民影响

历史表明，耐心外交和国际合作可以避免冲突。"""
            },
            "military_expert": {
                "file_name": "台海军事分析.txt",
                "content": """台海军事态势要点：
1. 地理因素：海峡地理对军事行动影响
2. 装备对比：双方主要军事装备分析
3. 作战模式：可能的军事行动模式
4. 国际因素：美军介入可能性
5. 风险评估：军事冲突潜在后果

任何军事行动都必须考虑政治目标和国际反应。"""
            },
            "political_expert": {
                "file_name": "国际法分析.txt",
                "content": """国际法视角下的台海问题：
1. 联合国宪章：使用武力的限制
2. 国际海洋法：台湾海峡法律地位
3. 历史先例：类似争端解决方案
4. 国际制裁：可能的法律后果
5. 和平解决：国际法鼓励的机制

国际社会支持和平解决，单方面改变现状可能面临法律后果。"""
            }
        }
        
        success_count = 0
        for agent_id, data in demo_data.items():
            if kb.upload_knowledge_file(agent_id, data["content"], data["file_name"], "演示数据"):
                success_count += 1
        
        st.success(f"✅ 已为 {success_count} 个智能体添加演示数据")
        st.rerun()
    
    # 显示知识库状态
    st.markdown("### 📊 知识库状态总览")
    
    status = kb.get_all_agents_status()
    
    col1, col2, col3 = st.columns(3)
    
    total_files = sum(s["file_count"] for s in status.values())
    total_size = sum(s["total_size"] for s in status.values())
    active_agents = sum(1 for s in status.values() if s["file_count"] > 0)
    
    with col1:
        st.metric("总文件数", total_files)
    with col2:
        st.metric("总内容量", f"{total_size} 字符")
    with col3:
        st.metric("活跃智能体", f"{active_agents}/5")
    
    # 各智能体详情
    for agent_id, info in status.items():
        with st.container():
            col1, col2, col3 = st.columns([3, 1, 1])
            
            with col1:
                status_icon = "🟢" if info["file_count"] > 0 else "⚪"
                st.write(f"{status_icon} **{info['name']}**")
                st.caption(info['summary'])
            
            with col2:
                st.metric("文件", info["file_count"])
            
            with col3:
                if info["file_count"] > 0:
                    if st.button("📋", key=f"view_{agent_id}", help="查看详情"):
                        st.session_state[f"show_{agent_id}"] = not st.session_state.get(f"show_{agent_id}", False)
            
            # 显示文件详情
            if st.session_state.get(f"show_{agent_id}", False):
                files = kb.get_agent_knowledge(agent_id)
                for file_info in files:
                    with st.expander(f"📄 {file_info['file_name']}", expanded=False):
                        st.write(f"**类型**: {file_info['file_type']}")
                        st.write(f"**大小**: {file_info['size']} 字符")
                        st.write(f"**上传时间**: {file_info['uploaded_at'][:19]}")
                        st.write(f"**摘要**: {file_info['summary']}")
                        
                        content = kb.read_knowledge_file(agent_id, file_info['file_id'])
                        if content:
                            st.text_area("内容", content, height=150, key=f"content_{file_info['file_id']}")
            
            st.divider()
    
    # 上下文生成测试
    st.markdown("### 🧠 上下文生成测试")
    
    test_scenario = st.text_input("测试场景", value="台海危机应对策略")
    
    if test_scenario:
        for agent_id, info in status.items():
            if info["file_count"] > 0:
                with st.expander(f"🎯 {info['name']}的知识库上下文", expanded=False):
                    context = get_agent_knowledge_context(agent_id, test_scenario)
                    if context:
                        st.text_area(
                            f"{info['name']}专属上下文",
                            context,
                            height=200,
                            key=f"context_{agent_id}"
                        )
                    else:
                        st.info("暂无相关内容")
    
    # 清理功能
    st.markdown("### 🧹 清理功能")
    
    if st.button("🗑️ 清空所有演示数据", type="secondary"):
        cleared_count = 0
        for agent_id in status.keys():
            files = kb.get_agent_knowledge(agent_id)
            for file_info in files:
                if "演示数据" in file_info.get('file_type', ''):
                    if kb.delete_knowledge_file(agent_id, file_info['file_id']):
                        cleared_count += 1
        
        if cleared_count > 0:
            st.success(f"✅ 已清理 {cleared_count} 个演示文件")
            st.rerun()
        else:
            st.info("ℹ️ 没有找到演示数据")
    
    # 使用说明
    with st.expander("💡 使用说明", expanded=False):
        st.markdown("""
        **知识库测试功能**:
        
        1. **🎯 添加演示数据**: 为所有智能体添加测试用的知识文件
        2. **📊 状态总览**: 查看所有智能体的知识库状态
        3. **📋 文件详情**: 点击按钮查看具体文件内容
        4. **🧠 上下文测试**: 测试知识库如何转换为AI上下文
        5. **🗑️ 清理功能**: 清除测试数据
        
        **在主应用中的使用**:
        - 进入"📚 知识库管理"页面进行完整管理
        - 在"🚀 开始推演"中查看知识库状态
        - 推演时AI会自动使用相关知识库内容
        """)

if __name__ == "__main__":
    main()
