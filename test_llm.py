#!/usr/bin/env python3
"""
LLM Configuration Test Script

Test the Qwen model configuration with SiliconFlow API
"""

import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from utils.llm_config import llm_config, test_llm_connection, generate_response
    
    def main():
        print("🤖 Qwen/Qwen3-8B 配置测试")
        print("=" * 60)
        
        # Display configuration
        print("📋 当前配置:")
        config_info = llm_config.get_provider_info()
        for key, value in config_info.items():
            print(f"  {key}: {value}")
        
        print(f"\n🔑 API Key: {llm_config.api_key[:20]}..." if llm_config.api_key else "  API Key: 未配置")
        print(f"🌐 Base URL: {llm_config.base_url}")
        print(f"🤖 Model: {llm_config.model_name}")
        
        # Test connection
        print("\n🧪 测试连接...")
        result = test_llm_connection()
        
        if result['success']:
            print("✅ 连接成功!")
            print(f"📝 响应: {result['response']}")
            print(f"🏷️ 模型: {result['model']}")
            
            # Test Chinese conversation
            print("\n🇨🇳 测试中文对话...")
            messages = [
                {"role": "system", "content": "你是一个专业的战略分析师，请用中文回答问题。"},
                {"role": "user", "content": "请简单介绍一下台海局势的复杂性。"}
            ]
            
            chinese_response = generate_response(messages)
            print(f"📝 中文回复: {chinese_response}")
            
            # Test strategic analysis
            print("\n🎯 测试战略分析能力...")
            strategy_messages = [
                {"role": "system", "content": "你是美国前总统特朗普的战略顾问，请以特朗普的风格和观点分析问题。"},
                {"role": "user", "content": "如果中国在台海地区增加军事活动，美国应该如何回应？"}
            ]
            
            strategy_response = generate_response(strategy_messages)
            print(f"📝 战略分析: {strategy_response}")
            
        else:
            print("❌ 连接失败!")
            print(f"🚫 错误: {result['error']}")
            
            # Troubleshooting tips
            print("\n🔧 故障排除建议:")
            print("1. 检查API密钥是否正确")
            print("2. 检查网络连接")
            print("3. 确认SiliconFlow服务状态")
            print("4. 检查模型名称是否正确")
        
        print("\n" + "=" * 60)
        print("测试完成!")

except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装必要的依赖包:")
    print("pip install openai python-dotenv")

except Exception as e:
    print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
