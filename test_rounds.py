#!/usr/bin/env python3
"""
测试轮数计算逻辑
"""

import streamlit as st

st.set_page_config(page_title="轮数计算测试", page_icon="🔢", layout="wide")

def main():
    """测试轮数计算"""
    
    st.title("🔢 轮数计算逻辑测试")
    
    st.markdown("### 📊 轮数计算说明")
    st.info("""
    **正确的轮数计算逻辑**:
    - 用户设置: 每个智能体发言轮数 (例如: 4轮)
    - 智能体数量: 5个
    - 总发言次数 = 5个智能体 × 4轮 = 20次发言
    - 每个智能体都会发言4次
    """)
    
    # 模拟设置
    st.markdown("### ⚙️ 模拟设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        agent_count = st.slider("智能体数量", 2, 10, 5)
        rounds_per_agent = st.slider("每个智能体发言轮数", 1, 10, 4)
    
    with col2:
        current_speech = st.slider("当前发言次数", 0, agent_count * rounds_per_agent, 8)
    
    # 计算结果
    total_speeches = agent_count * rounds_per_agent
    current_round_per_agent = current_speech / agent_count
    progress = current_speech / total_speeches if total_speeches > 0 else 0
    
    st.markdown("### 📈 计算结果")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("智能体数量", agent_count, "个")
    
    with col2:
        st.metric("每个智能体轮数", rounds_per_agent, "轮")
    
    with col3:
        st.metric("总发言次数", total_speeches, "次")
    
    with col4:
        st.metric("当前发言次数", current_speech, "次")
    
    # 进度显示
    st.markdown("### 📊 进度显示")
    
    st.progress(progress, text=f"总体进度: {current_speech}/{total_speeches} 次发言")
    st.info(f"📊 平均每个智能体已发言: {current_round_per_agent:.1f}/{rounds_per_agent} 轮")
    
    # 详细分析
    st.markdown("### 🔍 详细分析")
    
    if current_speech > 0:
        # 计算每个智能体的发言次数
        speeches_per_agent = []
        remaining_speeches = current_speech
        
        for i in range(agent_count):
            # 轮流发言，计算每个智能体的发言次数
            agent_speeches = min(remaining_speeches // agent_count + (1 if remaining_speeches % agent_count > i else 0), rounds_per_agent)
            speeches_per_agent.append(max(0, min(agent_speeches, remaining_speeches)))
            remaining_speeches = max(0, remaining_speeches - 1)
        
        # 重新计算（更准确的方式）
        speeches_per_agent = []
        for i in range(agent_count):
            # 每个智能体在轮流发言中的次数
            agent_speeches = (current_speech + agent_count - 1 - i) // agent_count
            speeches_per_agent.append(min(agent_speeches, rounds_per_agent))
        
        st.markdown("**各智能体发言次数分布:**")
        for i, speeches in enumerate(speeches_per_agent):
            agent_name = f"智能体{i+1}"
            remaining = rounds_per_agent - speeches
            st.write(f"• {agent_name}: {speeches}/{rounds_per_agent} 轮 (还需 {remaining} 轮)")
    
    # 示例场景
    st.markdown("### 🎯 示例场景")
    
    example_scenarios = [
        {"agents": 5, "rounds": 4, "description": "5个智能体，每个发言4轮 = 20次总发言"},
        {"agents": 3, "rounds": 3, "description": "3个智能体，每个发言3轮 = 9次总发言"},
        {"agents": 4, "rounds": 2, "description": "4个智能体，每个发言2轮 = 8次总发言"},
    ]
    
    for scenario in example_scenarios:
        total = scenario["agents"] * scenario["rounds"]
        st.write(f"• {scenario['description']}")
    
    st.markdown("### ✅ 修复总结")
    st.success("""
    **修复内容**:
    1. 用户设置改为"每个智能体发言轮数"而不是"总讨论轮数"
    2. 总发言次数 = 智能体数量 × 每个智能体轮数
    3. 进度显示包含每个智能体的平均发言轮数
    4. 配置摘要显示预计总发言次数
    
    **现在的逻辑**: 设置4轮 + 5个智能体 = 20次总发言，每个智能体发言4次
    """)

if __name__ == "__main__":
    main()
