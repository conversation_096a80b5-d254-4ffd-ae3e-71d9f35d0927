#!/usr/bin/env python3
"""
测试session_state修复
"""

import streamlit as st

# 注意：不在这里设置页面配置，避免与主应用冲突

def main():
    """测试session_state修复"""
    
    st.title("🔧 Session State 修复测试")
    st.markdown("**测试 simulation_messages 初始化问题的修复**")
    
    # 模拟主应用的初始化逻辑
    if 'simulation_messages' not in st.session_state:
        st.session_state.simulation_messages = []
    
    if 'current_round' not in st.session_state:
        st.session_state.current_round = 0
    
    if 'show_context_debug' not in st.session_state:
        st.session_state.show_context_debug = False
    
    # 测试上下文统计功能
    st.markdown("### 📊 上下文统计测试")
    
    col1, col2 = st.columns(2)
    
    with col1:
        show_context_stats = st.checkbox("📊 显示上下文统计", value=False, help="测试上下文统计功能")
        st.session_state.show_context_debug = st.checkbox("🔍 调试模式", value=False, help="显示详细信息")
    
    with col2:
        # 这里模拟原来出错的代码逻辑
        if show_context_stats and hasattr(st.session_state, 'simulation_messages') and st.session_state.simulation_messages:
            try:
                # 尝试导入上下文管理器
                from simple_context_manager import get_simple_context_statistics
                stats = get_simple_context_statistics(st.session_state.simulation_messages)
                
                st.metric("总消息数", stats.get("total_messages", 0))
                ratio = stats.get("estimated_context_ratio", 0)
                st.metric("上下文使用率", f"{ratio:.1%}")
                
                if ratio > 0.8:
                    st.warning("⚠️ 上下文接近限制")
                elif ratio > 0.6:
                    st.info("ℹ️ 上下文使用较多")
                else:
                    st.success("✅ 上下文正常")
                    
            except ImportError:
                st.warning("⚠️ 上下文管理器未加载")
        elif show_context_stats:
            st.info("📝 暂无消息数据，请先添加一些测试消息")
    
    # 测试消息操作
    st.markdown("### 💬 消息操作测试")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("➕ 添加测试消息", type="primary"):
            test_message = {
                "speaker": "test_agent",
                "content": f"这是第{len(st.session_state.simulation_messages) + 1}条测试消息",
                "timestamp": "12:34:56"
            }
            st.session_state.simulation_messages.append(test_message)
            st.success("✅ 添加成功")
            st.rerun()
    
    with col2:
        if st.button("🔄 清空消息"):
            st.session_state.simulation_messages = []
            st.success("✅ 清空成功")
            st.rerun()
    
    with col3:
        st.metric("当前消息数", len(st.session_state.simulation_messages))
    
    # 显示当前消息
    if st.session_state.simulation_messages:
        st.markdown("### 📝 当前消息列表")
        
        for i, msg in enumerate(st.session_state.simulation_messages):
            with st.expander(f"消息 {i+1}: {msg['speaker']}", expanded=False):
                st.write(f"**发言者**: {msg['speaker']}")
                st.write(f"**内容**: {msg['content']}")
                st.write(f"**时间**: {msg['timestamp']}")
    
    # 测试各种session_state访问
    st.markdown("### 🧪 Session State 访问测试")
    
    tests = [
        ("simulation_messages 存在", hasattr(st.session_state, 'simulation_messages')),
        ("simulation_messages 是列表", isinstance(st.session_state.simulation_messages, list)),
        ("current_round 存在", hasattr(st.session_state, 'current_round')),
        ("current_round 是数字", isinstance(st.session_state.current_round, int)),
        ("show_context_debug 存在", hasattr(st.session_state, 'show_context_debug')),
    ]
    
    for test_name, test_result in tests:
        if test_result:
            st.success(f"✅ {test_name}")
        else:
            st.error(f"❌ {test_name}")
    
    # 显示session_state内容
    with st.expander("🔍 Session State 详情", expanded=False):
        st.write("**所有 session_state 变量**:")
        for key in st.session_state:
            value = st.session_state[key]
            if key == 'simulation_messages':
                st.write(f"- {key}: 列表，长度 {len(value)}")
            else:
                st.write(f"- {key}: {type(value).__name__} = {value}")
    
    # 修复验证
    st.markdown("### ✅ 修复验证")
    
    st.info("""
    **修复内容**:
    1. ✅ 在函数开头就初始化所有 session_state 变量
    2. ✅ 使用 hasattr() 检查变量是否存在
    3. ✅ 删除重复的初始化代码
    4. ✅ 确保访问前变量已存在
    
    **测试结果**:
    - 如果上面所有测试都显示 ✅，说明修复成功
    - 可以安全地访问 simulation_messages 等变量
    - 不会再出现 AttributeError 错误
    """)
    
    # 模拟主应用的关键操作
    st.markdown("### 🎯 主应用关键操作模拟")
    
    if st.button("🧪 模拟上下文统计访问"):
        try:
            # 模拟原来出错的代码
            if hasattr(st.session_state, 'simulation_messages') and st.session_state.simulation_messages:
                st.success("✅ 成功访问 simulation_messages（有数据）")
                st.write(f"消息数量: {len(st.session_state.simulation_messages)}")
            else:
                st.info("ℹ️ simulation_messages 存在但为空")
                
        except AttributeError as e:
            st.error(f"❌ 仍然有错误: {e}")
        except Exception as e:
            st.error(f"❌ 其他错误: {e}")
    
    if st.button("🧪 模拟消息显示循环"):
        try:
            # 模拟显示消息的循环
            message_count = 0
            for msg in st.session_state.simulation_messages:
                message_count += 1
            
            st.success(f"✅ 成功遍历 {message_count} 条消息")
            
        except AttributeError as e:
            st.error(f"❌ 遍历错误: {e}")
        except Exception as e:
            st.error(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    main()
