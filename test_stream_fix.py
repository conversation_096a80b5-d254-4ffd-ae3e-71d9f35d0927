#!/usr/bin/env python3
"""
测试流式输出修复
"""

import streamlit as st
import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

st.set_page_config(page_title="流式输出测试", page_icon="🧪")

def test_stream_parameter():
    """测试流式输出参数传递"""
    
    st.title("🧪 流式输出参数测试")
    
    # 模拟参数
    enable_stream = st.checkbox("🌊 启用流式输出", value=True)
    
    st.write(f"enable_stream 参数值: {enable_stream}")
    st.write(f"参数类型: {type(enable_stream)}")
    
    # 模拟函数调用
    def mock_start_simulation(use_stream=True):
        st.write(f"start_simulation 接收到的 use_stream: {use_stream}")
        st.write(f"use_stream 类型: {type(use_stream)}")
        return use_stream
    
    def mock_generate_agent_response(stream=False):
        st.write(f"generate_agent_response 接收到的 stream: {stream}")
        st.write(f"stream 类型: {type(stream)}")
        return f"测试回应 (流式: {stream})"
    
    if st.button("🧪 测试参数传递"):
        st.markdown("### 测试结果:")
        
        # 测试1: 直接传递
        st.markdown("**测试1: 直接传递**")
        result1 = mock_start_simulation(enable_stream)
        
        # 测试2: 函数调用链
        st.markdown("**测试2: 函数调用链**")
        if enable_stream:
            response = mock_generate_agent_response(stream=True)
        else:
            response = mock_generate_agent_response(stream=False)
        
        st.write(f"最终响应: {response}")
        
        # 测试3: 验证布尔值
        st.markdown("**测试3: 布尔值验证**")
        if enable_stream:
            st.success("✅ enable_stream 为 True")
        else:
            st.error("❌ enable_stream 为 False")

def test_import():
    """测试模块导入"""
    
    st.markdown("### 📦 模块导入测试")
    
    try:
        from utils.llm_config import generate_response
        st.success("✅ generate_response 导入成功")
        st.write(f"generate_response 函数: {generate_response}")
    except ImportError as e:
        st.error(f"❌ generate_response 导入失败: {e}")
    
    try:
        # 测试demo_app中的函数
        import importlib.util
        spec = importlib.util.spec_from_file_location("demo_app", "demo_app.py")
        demo_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(demo_module)
        
        st.success("✅ demo_app 模块导入成功")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(demo_module.generate_agent_response)
        st.write(f"generate_agent_response 函数签名: {sig}")
        
        sig2 = inspect.signature(demo_module.start_simulation)
        st.write(f"start_simulation 函数签名: {sig2}")
        
    except Exception as e:
        st.error(f"❌ demo_app 模块导入失败: {e}")

def main():
    """主函数"""
    
    st.sidebar.title("🧪 测试选项")
    
    test_type = st.sidebar.selectbox(
        "选择测试类型:",
        ["参数传递测试", "模块导入测试", "完整流程测试"]
    )
    
    if test_type == "参数传递测试":
        test_stream_parameter()
    elif test_type == "模块导入测试":
        test_import()
    else:
        st.title("🔄 完整流程测试")
        st.info("请使用主应用进行完整流程测试")
        
        if st.button("🚀 启动主应用"):
            st.markdown("""
            请在终端中运行以下命令启动主应用:
            ```bash
            streamlit run demo_app.py
            ```
            
            然后:
            1. 进入"🚀 开始推演"页面
            2. 勾选"🌊 启用流式输出"
            3. 点击"🚀 开始推演"
            4. 点击"🎬 开始推演"
            5. 观察控制台输出中的"流式输出: True"
            """)

if __name__ == "__main__":
    main()
