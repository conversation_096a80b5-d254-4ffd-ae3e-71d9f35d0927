#!/usr/bin/env python3
"""
简化的流式输出测试
"""

import streamlit as st
import time
from datetime import datetime

st.set_page_config(page_title="流式输出测试", page_icon="🌊")

def simulate_typing_effect(text, placeholder):
    """模拟打字效果"""
    
    displayed_text = ""
    words = text.split()
    
    for i, word in enumerate(words):
        displayed_text += word + " "
        # 显示当前文本和光标
        placeholder.markdown(f"**AI智能体** 正在发言...\n\n{displayed_text}▋")
        time.sleep(0.2)  # 控制打字速度
    
    # 完成后移除光标
    placeholder.markdown(f"**AI智能体** - {datetime.now().strftime('%H:%M:%S')}\n\n{displayed_text}")
    
    return displayed_text.strip()

def main():
    st.title("🌊 流式输出效果测试")
    
    # 测试文本
    test_responses = [
        "作为美国前总统，我认为这个问题需要强有力的回应。我们必须展现美国的实力和决心，不能让敌人认为我们软弱。",
        "从军事角度分析，我们需要评估各种选项的风险和收益。建议采取渐进式回应策略，避免过度升级。",
        "这是一个复杂的地缘政治问题，需要综合考虑多方面因素。我们应该与盟友协调，寻求外交解决方案。"
    ]
    
    st.write("点击按钮测试不同的流式输出效果:")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🇺🇸 特朗普发言"):
            placeholder = st.empty()
            response = simulate_typing_effect(test_responses[0], placeholder)
            st.success("✅ 发言完成")
    
    with col2:
        if st.button("🎖️ 军事专家发言"):
            placeholder = st.empty()
            response = simulate_typing_effect(test_responses[1], placeholder)
            st.success("✅ 发言完成")
    
    with col3:
        if st.button("🎓 政治专家发言"):
            placeholder = st.empty()
            response = simulate_typing_effect(test_responses[2], placeholder)
            st.success("✅ 发言完成")
    
    # 聊天式界面测试
    st.subheader("💬 聊天式流式输出")
    
    if 'chat_messages' not in st.session_state:
        st.session_state.chat_messages = []
    
    # 显示聊天历史
    for msg in st.session_state.chat_messages:
        with st.chat_message(msg["role"]):
            st.write(f"**{msg['name']}** - {msg['timestamp']}")
            st.write(msg["content"])
    
    # 添加新消息
    if st.button("🤖 AI发言"):
        # 添加AI消息
        with st.chat_message("assistant"):
            placeholder = st.empty()
            ai_response = simulate_typing_effect(
                "我是AI助手，正在为您分析当前的战略形势。基于现有信息，我建议采取谨慎而坚定的立场。",
                placeholder
            )
        
        # 保存到历史
        st.session_state.chat_messages.append({
            "role": "assistant",
            "name": "AI助手",
            "content": ai_response,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        })
        
        st.rerun()
    
    # 用户输入
    user_input = st.chat_input("输入您的消息...")
    if user_input:
        # 添加用户消息
        user_msg = {
            "role": "user", 
            "name": "用户",
            "content": user_input,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        st.session_state.chat_messages.append(user_msg)
        
        # 显示用户消息
        with st.chat_message("user"):
            st.write(f"**用户** - {user_msg['timestamp']}")
            st.write(user_input)
        
        st.rerun()
    
    # 清除按钮
    if st.button("🗑️ 清除聊天记录"):
        st.session_state.chat_messages = []
        st.rerun()

if __name__ == "__main__":
    main()
