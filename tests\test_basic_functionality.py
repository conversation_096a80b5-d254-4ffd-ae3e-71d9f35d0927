#!/usr/bin/env python3
"""
Basic Functionality Tests

Simple tests to verify core functionality of the strategic simulation system.
"""

import sys
import os
import unittest
from pathlib import Path
import tempfile
import json

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agents.base_agent import BaseStrategicAgent
from agents.custom_agent import CustomAgent
from memory.memory_manager import MemoryManager
from knowledge.rag_system import RAGSystem


class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality of the simulation system."""
    
    def setUp(self):
        """Set up test fixtures."""
        
        # Create temporary directory for test data
        self.test_dir = tempfile.mkdtemp()
        
        # Sample agent configuration
        self.sample_agent_config = {
            "name": "Test Agent",
            "type": "test",
            "role": "Test Role",
            "persona": {
                "age": 35,
                "occupation": {
                    "title": "Test Analyst",
                    "company": "Test Organization",
                    "description": "Test description"
                },
                "personality": {
                    "traits": ["analytical", "strategic"],
                    "communication_style": "professional",
                    "decision_making": "evidence-based",
                    "core_values": ["accuracy", "efficiency"]
                },
                "expertise": ["testing", "analysis"]
            }
        }
    
    def tearDown(self):
        """Clean up test fixtures."""
        
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_custom_agent_creation(self):
        """Test creating a custom agent."""
        
        custom_agent = CustomAgent("Test Agent", self.sample_agent_config)
        
        self.assertEqual(custom_agent.name, "Test Agent")
        self.assertEqual(custom_agent.role, "Test Role")
        self.assertEqual(custom_agent.agent_type, "test")
    
    def test_agent_persona_setup(self):
        """Test agent persona configuration."""
        
        custom_agent = CustomAgent("Test Agent", self.sample_agent_config)
        
        # Check if persona attributes are set
        self.assertEqual(custom_agent.get('age'), 35)
        self.assertEqual(custom_agent.get('occupation'), "Test Analyst")
        self.assertEqual(custom_agent.get('personality_traits'), ["analytical", "strategic"])
        self.assertEqual(custom_agent.get('core_values'), ["accuracy", "efficiency"])
    
    def test_memory_manager_initialization(self):
        """Test memory manager initialization."""
        
        # Use temporary database
        config = {
            'db_path': os.path.join(self.test_dir, 'test_memory.db'),
            'conversation_memory_limit': 10,
            'use_mem0': False  # Disable external memory for testing
        }
        
        memory_manager = MemoryManager(config)
        
        # Test storing a contribution
        memory_manager.store_contribution(
            "test_agent", 
            "test_topic", 
            "test content", 
            1
        )
        
        # Test retrieving conversation history
        history = memory_manager.get_conversation_history("test_agent")
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]['content'], "test content")
    
    def test_memory_storage_and_retrieval(self):
        """Test memory storage and retrieval functionality."""
        
        config = {
            'db_path': os.path.join(self.test_dir, 'test_memory.db'),
            'use_mem0': False
        }
        
        memory_manager = MemoryManager(config)
        
        # Store a memory
        memory_manager.store_memory(
            "test_agent",
            "strategic_insight",
            "This is a test strategic insight",
            importance_score=0.8,
            tags=["strategy", "test"]
        )
        
        # Retrieve memories
        memories = memory_manager.get_relevant_memories("test_agent", "strategic")
        
        self.assertGreater(len(memories), 0)
        self.assertIn("strategic insight", memories[0]['content'])
    
    def test_rag_system_with_text_content(self):
        """Test RAG system with simple text content."""
        
        # Create a temporary text file
        test_content = """
        Strategic Planning Principles
        
        Strategic planning is essential for organizational success.
        It involves setting objectives, analyzing the environment,
        and developing action plans to achieve goals.
        
        Key components include:
        - Vision and mission definition
        - Environmental analysis
        - Strategy formulation
        - Implementation planning
        - Performance monitoring
        """
        
        test_file = os.path.join(self.test_dir, 'test_knowledge.txt')
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # Initialize RAG system
        config = {
            'chunk_size': 100,
            'chunk_overlap': 20,
            'vector_db_type': 'text_search'  # Use simple text search for testing
        }
        
        rag_system = RAGSystem(test_file, config)
        
        # Test retrieval
        results = rag_system.retrieve("strategic planning", top_k=3)
        
        self.assertGreater(len(results), 0)
        self.assertIn("strategic", results[0]['content'].lower())
    
    def test_agent_knowledge_integration(self):
        """Test agent integration with knowledge base."""
        
        # Create test knowledge base
        test_content = "Strategic analysis requires careful consideration of multiple factors."
        test_file = os.path.join(self.test_dir, 'agent_knowledge.txt')
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # Create agent with knowledge base
        memory_config = {
            'db_path': os.path.join(self.test_dir, 'agent_memory.db'),
            'use_mem0': False
        }
        
        memory_manager = MemoryManager(memory_config)
        
        custom_agent = CustomAgent(
            "Test Agent", 
            self.sample_agent_config,
            knowledge_base_path=test_file,
            memory_manager=memory_manager
        )
        
        # Test knowledge retrieval
        knowledge = custom_agent.retrieve_relevant_knowledge("strategic analysis")
        
        self.assertGreater(len(knowledge), 0)
        self.assertIn("strategic", knowledge[0].lower())
    
    def test_agent_contribution_generation(self):
        """Test agent contribution generation."""
        
        memory_config = {
            'db_path': os.path.join(self.test_dir, 'contrib_memory.db'),
            'use_mem0': False
        }
        
        memory_manager = MemoryManager(memory_config)
        
        custom_agent = CustomAgent(
            "Test Agent",
            self.sample_agent_config,
            memory_manager=memory_manager
        )
        
        # Test contribution generation
        topic = "Strategic planning for crisis response"
        previous_contributions = ["We need to assess the situation carefully"]
        
        try:
            contribution = custom_agent.contribute_to_discussion(topic, previous_contributions)
            
            # Basic checks
            self.assertIsInstance(contribution, str)
            self.assertGreater(len(contribution), 10)  # Should be substantial
            
        except Exception as e:
            # If LLM is not available, this test might fail
            # In that case, we just check that the method exists and is callable
            self.assertTrue(hasattr(custom_agent, 'contribute_to_discussion'))
            self.assertTrue(callable(getattr(custom_agent, 'contribute_to_discussion')))
    
    def test_agent_summary_generation(self):
        """Test agent summary generation."""
        
        custom_agent = CustomAgent("Test Agent", self.sample_agent_config)
        
        summary = custom_agent.get_agent_summary()
        
        self.assertIsInstance(summary, dict)
        self.assertIn('name', summary)
        self.assertIn('type', summary)
        self.assertIn('role', summary)
        self.assertEqual(summary['name'], "Test Agent")
    
    def test_custom_agent_templates(self):
        """Test custom agent template creation."""
        
        # Test general template
        general_agent = CustomAgent.create_from_template("General Advisor", "general")
        self.assertEqual(general_agent.name, "General Advisor")
        self.assertEqual(general_agent.role, "Strategic Advisor")
        
        # Test business template
        business_agent = CustomAgent.create_from_template("Business Expert", "business")
        self.assertEqual(business_agent.name, "Business Expert")
        self.assertEqual(business_agent.role, "Business Strategy Expert")
        
        # Test academic template
        academic_agent = CustomAgent.create_from_template("Research Analyst", "academic")
        self.assertEqual(academic_agent.name, "Research Analyst")
        self.assertEqual(academic_agent.role, "Academic Researcher")
    
    def test_memory_statistics(self):
        """Test memory system statistics."""
        
        config = {
            'db_path': os.path.join(self.test_dir, 'stats_memory.db'),
            'use_mem0': False
        }
        
        memory_manager = MemoryManager(config)
        
        # Add some test data
        memory_manager.store_contribution("agent1", "topic1", "content1", 1)
        memory_manager.store_contribution("agent2", "topic2", "content2", 1)
        memory_manager.store_memory("agent1", "insight", "test insight", 0.7)
        
        # Get statistics
        stats = memory_manager.get_memory_stats()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('total_conversations', stats)
        self.assertIn('runtime_stats', stats)
        self.assertEqual(stats['total_conversations'], 2)


class TestSystemIntegration(unittest.TestCase):
    """Test system integration functionality."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up integration test fixtures."""
        
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_agent_memory_integration(self):
        """Test integration between agents and memory system."""
        
        memory_config = {
            'db_path': os.path.join(self.test_dir, 'integration_memory.db'),
            'use_mem0': False
        }
        
        memory_manager = MemoryManager(memory_config)
        
        agent_config = {
            "name": "Integration Test Agent",
            "type": "test",
            "role": "Test Integration",
            "persona": {
                "expertise": ["integration", "testing"]
            }
        }
        
        agent = CustomAgent("Integration Agent", agent_config, memory_manager=memory_manager)
        
        # Test that agent can store and retrieve memories
        agent.memory_manager.store_contribution("Integration Agent", "test topic", "test content", 1)
        
        history = agent.memory_manager.get_conversation_history("Integration Agent")
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]['content'], "test content")


def run_tests():
    """Run all tests."""
    
    print("🧪 Running Strategic Simulation System Tests")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestBasicFunctionality))
    suite.addTests(loader.loadTestsFromTestCase(TestSystemIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All tests passed!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
