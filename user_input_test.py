#!/usr/bin/env python3
"""
用户输入测试 - 确保用户智能体输入框正常显示
"""

import streamlit as st
import time
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

st.set_page_config(page_title="用户输入测试", page_icon="👤", layout="wide")

def main():
    """主函数"""
    
    st.title("👤 用户智能体输入测试")
    st.markdown("**测试用户智能体输入框的显示和功能**")
    
    # 初始化
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'agents' not in st.session_state:
        st.session_state.agents = ["特朗普", "张三", "军事专家", "李四", "政治专家"]
    
    if 'current_agent_index' not in st.session_state:
        st.session_state.current_agent_index = 0
    
    if 'user_agents' not in st.session_state:
        st.session_state.user_agents = ["张三", "李四"]
    
    # 侧边栏
    with st.sidebar:
        st.header("🎮 控制面板")
        
        st.markdown("**智能体列表:**")
        for i, agent in enumerate(st.session_state.agents):
            is_user = agent in st.session_state.user_agents
            agent_type = "👤 用户" if is_user else "🤖 AI"
            
            if i == st.session_state.current_agent_index:
                st.write(f"👉 **{agent}** {agent_type} (当前)")
            else:
                st.write(f"• {agent} {agent_type}")
        
        if st.button("➡️ 下一位"):
            st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
            st.rerun()
        
        if st.button("🔄 重置"):
            st.session_state.messages = []
            st.session_state.current_agent_index = 0
            st.rerun()
    
    # 显示当前发言者
    current_agent = st.session_state.agents[st.session_state.current_agent_index]
    is_user_agent = current_agent in st.session_state.user_agents
    
    st.markdown(f"### 当前发言者: {current_agent}")
    
    if is_user_agent:
        st.success(f"👤 用户智能体: {current_agent}")
    else:
        st.info(f"🤖 AI智能体: {current_agent}")
    
    # 显示消息历史
    st.markdown("### 💬 发言记录")
    
    for msg in st.session_state.messages:
        is_user_msg = msg["speaker"] in st.session_state.user_agents
        with st.chat_message("user" if is_user_msg else "assistant"):
            st.write(f"**{msg['speaker']}** - {msg['timestamp']}")
            st.write(msg['content'])
    
    # 用户输入区域
    st.markdown("---")
    
    if is_user_agent:
        # 用户智能体输入
        st.markdown(f"### 💬 {current_agent} 的发言")
        st.info(f"请以 **{current_agent}** 的身份发言")
        
        user_input = st.text_area(
            f"请输入 {current_agent} 的观点:",
            height=120,
            key=f"input_{current_agent}_{st.session_state.current_agent_index}",
            placeholder=f"请输入 {current_agent} 对台海危机的看法..."
        )
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📤 发送", type="primary", use_container_width=True):
                if user_input and user_input.strip():
                    # 添加用户发言
                    new_msg = {
                        "speaker": current_agent,
                        "content": user_input.strip(),
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    }
                    st.session_state.messages.append(new_msg)
                    
                    # 移动到下一个智能体
                    st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
                    st.success(f"✅ {current_agent} 发言已提交")
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error("⚠️ 请输入发言内容")
        
        with col2:
            if st.button("🤖 AI代发", use_container_width=True):
                # AI代为发言
                ai_response = f"我是{current_agent}，我认为台海问题需要谨慎处理，避免冲突升级。"
                
                new_msg = {
                    "speaker": current_agent,
                    "content": f"[AI代发] {ai_response}",
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(new_msg)
                
                # 移动到下一个智能体
                st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
                st.success(f"✅ AI已为 {current_agent} 代发")
                time.sleep(1)
                st.rerun()
        
        with col3:
            if st.button("⏭️ 跳过", use_container_width=True):
                # 跳过当前用户智能体
                st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
                st.warning(f"⏭️ 已跳过 {current_agent}")
                time.sleep(1)
                st.rerun()
    
    else:
        # AI智能体
        st.markdown(f"### 🤖 {current_agent} (AI智能体)")
        st.info("AI智能体会自动发言，无需手动输入")
        
        if st.button("🎤 AI发言", type="primary", use_container_width=True):
            # 模拟AI发言
            ai_responses = {
                "特朗普": "我们必须展现美国的实力，但也要避免不必要的冲突。美国优先！",
                "军事专家": "从军事角度分析，我们需要评估各种选项的风险和收益。",
                "政治专家": "这是一个复杂的地缘政治问题，需要综合考虑多方面因素。"
            }
            
            ai_response = ai_responses.get(current_agent, f"我是{current_agent}，我认为需要谨慎分析当前形势。")
            
            new_msg = {
                "speaker": current_agent,
                "content": ai_response,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(new_msg)
            
            # 移动到下一个智能体
            st.session_state.current_agent_index = (st.session_state.current_agent_index + 1) % len(st.session_state.agents)
            st.success(f"✅ {current_agent} 发言完成")
            time.sleep(1)
            st.rerun()
    
    # 状态显示
    st.markdown("### 📊 状态信息")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("当前发言者", current_agent)
    
    with col2:
        st.metric("智能体类型", "👤 用户" if is_user_agent else "🤖 AI")
    
    with col3:
        st.metric("发言记录", len(st.session_state.messages))
    
    # 说明
    st.markdown("### 📝 测试说明")
    st.info("""
    **测试目标**: 确保用户智能体输入框正常显示和工作
    
    **操作步骤**:
    1. 点击"➡️ 下一位"切换到用户智能体（张三或李四）
    2. 确认输入框正常显示
    3. 输入文字并点击"📤 发送"
    4. 确认发言被正确记录
    5. 自动切换到下一个智能体
    
    **预期结果**: 用户智能体时显示输入框，AI智能体时显示发言按钮
    """)

if __name__ == "__main__":
    main()
