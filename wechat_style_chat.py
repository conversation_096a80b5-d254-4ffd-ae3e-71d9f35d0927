#!/usr/bin/env python3
"""
微信风格的流式聊天室
"""

import streamlit as st
import time
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 设置页面配置
st.set_page_config(
    page_title="战略推演群聊",
    page_icon="💬",
    layout="wide"
)

# 导入AI功能
try:
    from utils.llm_config import generate_response
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False


def get_agent_avatar(agent_id: str) -> str:
    """获取智能体头像"""
    avatars = {
        "trump": "🇺🇸", "biden": "🏛️", "moderator": "🎯",
        "military_expert": "🎖️", "political_expert": "🎓"
    }
    return avatars.get(agent_id, "👤" if agent_id.startswith("user_") else "🤖")


def render_message_bubble(speaker: str, content: str, timestamp: str, agents: dict, is_streaming: bool = False):
    """渲染消息气泡"""
    
    # 获取发言者信息
    if speaker in agents:
        display_name = agents[speaker]["name"]
        is_user_msg = False
    elif speaker.startswith("user_"):
        display_name = speaker.replace("user_", "").replace("_", " ")
        is_user_msg = True
    elif speaker == "user":
        display_name = "您"
        is_user_msg = True
    else:
        display_name = speaker
        is_user_msg = False
    
    avatar = get_agent_avatar(speaker)
    
    # 样式设置
    if is_user_msg:
        # 用户消息 - 右对齐蓝色
        bubble_style = """
        background: linear-gradient(135deg, #007AFF, #0051D5);
        color: white;
        margin-left: auto;
        margin-right: 10px;
        border-radius: 18px 18px 4px 18px;
        """
        container_style = "justify-content: flex-end;"
        avatar_pos = "order: 2; margin-left: 10px; margin-right: 0;"
    else:
        # AI消息 - 左对齐灰色
        bubble_style = """
        background: #F0F0F0;
        color: #333;
        margin-left: 10px;
        margin-right: auto;
        border-radius: 18px 18px 18px 4px;
        """
        container_style = "justify-content: flex-start;"
        avatar_pos = "order: 1; margin-right: 10px; margin-left: 0;"
    
    # 流式输出光标
    cursor = "<span style='animation: blink 1s infinite; color: #666;'>|</span>" if is_streaming else ""
    
    # 渲染消息
    st.markdown(f"""
    <div style="display: flex; {container_style} margin: 15px 0; align-items: flex-end;">
        <div style="{avatar_pos} width: 45px; height: 45px; background: linear-gradient(135deg, #E5E5EA, #D1D1D6); 
                    border-radius: 50%; display: flex; align-items: center; justify-content: center; 
                    font-size: 22px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            {avatar}
        </div>
        <div style="max-width: 65%; order: {'1' if is_user_msg else '2'};">
            {f'<div style="font-size: 13px; color: #666; margin-bottom: 5px; font-weight: 500; {("text-align: right;" if is_user_msg else "")}">{display_name}</div>' if not is_user_msg else ''}
            <div style="{bubble_style} padding: 14px 18px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); max-width: 100%;">
                <div style="font-size: 15px; line-height: 1.4; word-wrap: break-word;">
                    {content}{cursor}
                </div>
                <div style="font-size: 11px; {'color: rgba(255,255,255,0.8);' if is_user_msg else 'color: #999;'} 
                           margin-top: 6px; {'text-align: right;' if is_user_msg else ''}">
                    {timestamp}
                </div>
            </div>
        </div>
    </div>
    
    <style>
    @keyframes blink {{
        0%, 50% {{ opacity: 1; }}
        51%, 100% {{ opacity: 0; }}
    }}
    </style>
    """, unsafe_allow_html=True)


def handle_next_speaker(show_thinking: bool):
    """处理下一位发言者"""
    if st.session_state.selected_agents and st.session_state.messages:
        # 检查是否需要进入总结阶段
        if st.session_state.current_round >= st.session_state.max_rounds:
            st.session_state.simulation_phase = "summary"
            st.info("🎯 讨论轮次已满，自动进入总结阶段")
            st.rerun()
            return

        # 确定下一个发言者（跳过导调）
        available_speakers = [agent for agent in st.session_state.selected_agents if agent != "moderator"]

        if available_speakers:
            # 从非导调智能体中选择
            speaker_index = (st.session_state.current_round - 1) % len(available_speakers)
            next_speaker = available_speakers[speaker_index]

            if next_speaker.startswith("user_"):
                st.info(f"轮到: {next_speaker.replace('user_', '')}")
            else:
                # AI发言 - 使用简化版流式输出
                response = stream_ai_response_simple(
                    next_speaker,
                    st.session_state.agents[next_speaker],
                    st.session_state.scenario,
                    st.session_state.messages,
                    show_thinking
                )

                # 添加到消息列表
                new_msg = {
                    "speaker": next_speaker,
                    "content": response,
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(new_msg)
                st.session_state.current_round += 1
                st.rerun()
        else:
            st.warning("请至少选择一个非导调智能体参与讨论")


def generate_summary():
    """生成讨论总结"""
    if AI_AVAILABLE and generate_response:
        # 收集所有发言
        discussion_content = "\n\n".join([
            f"{msg['speaker']}: {msg['content']}"
            for msg in st.session_state.messages
        ])

        summary_prompt = f"""请作为推演主持人，对以下讨论进行总结：

{discussion_content}

请从以下角度进行总结：
1. 各方主要观点
2. 争议焦点
3. 共识点
4. 待决策问题

总结要客观、全面，为最终决策提供参考。"""

        messages = [
            {"role": "system", "content": "你是推演主持人，需要客观总结讨论内容。"},
            {"role": "user", "content": summary_prompt}
        ]

        try:
            with st.spinner("正在生成讨论总结..."):
                summary = generate_response(messages, max_tokens=500, temperature=0.3)
        except Exception as e:
            summary = "总结生成失败，请手动整理讨论要点。"
    else:
        summary = "各位专家就中东军事干预问题进行了深入讨论，现在需要做出最终决策。"

    # 添加总结消息
    summary_msg = {
        "speaker": "moderator",
        "content": f"📋 **讨论总结**\n\n{summary}\n\n现在请决策者做出最终决定。",
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }
    st.session_state.messages.append(summary_msg)
    st.session_state.simulation_phase = "decision"
    st.rerun()


def generate_final_decision():
    """生成最终决策"""
    decision_maker = st.session_state.decision_maker

    if AI_AVAILABLE and generate_response:
        # 收集所有讨论内容
        discussion_content = "\n\n".join([
            f"{msg['speaker']}: {msg['content']}"
            for msg in st.session_state.messages
        ])

        decision_prompt = f"""基于以下完整讨论，请作为{st.session_state.agents[decision_maker]['name']}做出最终决策：

{discussion_content}

请明确回答：
1. 是否进行军事干预？
2. 如果干预，采取什么方式？
3. 主要考虑因素是什么？
4. 预期结果和风险评估

请给出明确、具体的决策方案。"""

        # 获取决策者的人格提示
        decision_prompts = {
            "trump": "你是特朗普，以美国优先、强硬但实用的风格做决策。",
            "biden": "你是拜登，以多边合作、谨慎但坚定的风格做决策。"
        }

        system_prompt = decision_prompts.get(decision_maker, f"你是{st.session_state.agents[decision_maker]['name']}，请做出最终决策。")

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": decision_prompt}
        ]

        try:
            with st.spinner(f"{st.session_state.agents[decision_maker]['name']} 正在做最终决策..."):
                decision = generate_response(messages, max_tokens=400, temperature=0.5)
        except Exception as e:
            decision = f"基于各方讨论，我决定采取谨慎的军事干预策略，优先考虑外交解决方案。"
    else:
        decision = f"基于各方讨论，我决定采取谨慎的军事干预策略。"

    # 添加决策消息
    decision_msg = {
        "speaker": decision_maker,
        "content": f"⚖️ **最终决策**\n\n{decision}\n\n推演结束。",
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }
    st.session_state.messages.append(decision_msg)
    st.success("🎉 推演完成！最终决策已生成。")
    st.rerun()


def stream_ai_response_simple(agent_id: str, agent_info: dict, scenario: dict, context: list,
                             show_thinking: bool = False, typing_speed: float = 0.05):
    """简化版流式显示AI回应"""

    # 生成回应
    if AI_AVAILABLE and generate_response:
        agent_prompts = {
            "trump": "你是唐纳德·特朗普。特点：直接、自信、强调美国优先。",
            "biden": "你是乔·拜登。特点：温和、重视合作、经验丰富。",
            "military_expert": "你是军事专家。特点：专业、客观、注重实用性。",
            "political_expert": "你是政治专家。特点：深入分析、考虑多方面因素。",
            "moderator": "你是推演主持人。特点：中立、引导讨论、总结要点。"
        }

        system_prompt = agent_prompts.get(agent_id, f"你是{agent_info['name']}。")

        messages = [
            {"role": "system", "content": system_prompt + "请用中文回应，保持角色特色，控制在150字以内。"},
            {"role": "user", "content": f"场景：{scenario['context']}\n\n请基于讨论分析并提出建议。"}
        ]

        try:
            response = generate_response(messages, max_tokens=200, temperature=0.8)
        except Exception as e:
            response = f"我是{agent_info['name']}，针对当前情况，我认为需要综合考虑各方面因素。"
    else:
        response = f"[演示] 我是{agent_info['name']}，针对当前情况，我认为需要综合考虑各方面因素。"

    # 显示思考过程
    if show_thinking:
        with st.spinner(f"{agent_info['name']} 正在思考..."):
            time.sleep(1)

    # 简化的流式显示
    placeholder = st.empty()
    displayed_content = ""
    timestamp = datetime.now().strftime("%H:%M:%S")

    # 按字符流式显示
    for char in response:
        displayed_content += char

        # 使用简单的markdown显示
        placeholder.markdown(f"""
        <div style="background: #f0f0f0; padding: 12px 16px; border-radius: 18px; margin: 10px 0; max-width: 70%;">
            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">
                <strong>{agent_info['name']}</strong> 正在发言...
            </div>
            <div style="font-size: 14px; line-height: 1.4;">
                {displayed_content}<span style="animation: blink 1s infinite;">|</span>
            </div>
        </div>

        <style>
        @keyframes blink {{
            0%, 50% {{ opacity: 1; }}
            51%, 100% {{ opacity: 0; }}
        }}
        </style>
        """, unsafe_allow_html=True)

        time.sleep(typing_speed)

    # 最终显示
    placeholder.markdown(f"""
    <div style="background: #f0f0f0; padding: 12px 16px; border-radius: 18px; margin: 10px 0; max-width: 70%;">
        <div style="font-size: 12px; color: #666; margin-bottom: 4px;">
            <strong>{agent_info['name']}</strong> - {timestamp}
        </div>
        <div style="font-size: 14px; line-height: 1.4;">
            {displayed_content}
        </div>
    </div>
    """, unsafe_allow_html=True)

    return displayed_content


def stream_ai_response(agent_id: str, agent_info: dict, scenario: dict, context: list,
                      show_thinking: bool = False, typing_speed: float = 0.03):
    """流式显示AI回应"""
    
    # 生成回应
    if AI_AVAILABLE and generate_response:
        # 构建提示
        agent_prompts = {
            "trump": "你是唐纳德·特朗普。特点：直接、自信、强调美国优先。用简洁有力的语言。",
            "biden": "你是乔·拜登。特点：温和、重视合作、经验丰富。语言稳重。",
            "military_expert": "你是军事专家。特点：专业、客观、注重实用性。",
            "political_expert": "你是政治专家。特点：深入分析、考虑多方面因素。",
            "moderator": "你是推演主持人。特点：中立、引导讨论、总结要点。"
        }
        
        system_prompt = agent_prompts.get(agent_id, f"你是{agent_info['name']}。")
        
        messages = [
            {"role": "system", "content": system_prompt + "请用中文回应，保持角色特色，控制在150字以内。"},
            {"role": "user", "content": f"场景：{scenario['context']}\n\n请基于讨论分析并提出建议。"}
        ]
        
        try:
            response = generate_response(messages, max_tokens=200, temperature=0.8)
        except Exception as e:
            response = f"我是{agent_info['name']}，针对当前情况，我认为需要综合考虑各方面因素，制定合适的应对策略。"
    else:
        response = f"[演示] 我是{agent_info['name']}，针对当前情况，我认为需要综合考虑各方面因素。"
    
    # 显示思考过程
    if show_thinking:
        thinking_placeholder = st.empty()
        thinking_placeholder.markdown(f"""
        <div style="display: flex; justify-content: flex-start; margin: 10px 0; align-items: center;">
            <div style="width: 45px; height: 45px; background: #E5E5EA; border-radius: 50%; 
                        display: flex; align-items: center; justify-content: center; font-size: 22px; margin-right: 10px;">
                {get_agent_avatar(agent_id)}
            </div>
            <div style="background: #F0F0F0; color: #666; padding: 10px 16px; border-radius: 18px; 
                        font-size: 14px; font-style: italic;">
                {agent_info['name']} 正在思考
                <span style="animation: dots 1.5s infinite;">...</span>
            </div>
        </div>
        
        <style>
        @keyframes dots {{
            0%, 20% {{ content: '.'; }}
            40% {{ content: '..'; }}
            60%, 100% {{ content: '...'; }}
        }}
        </style>
        """, unsafe_allow_html=True)
        time.sleep(1.5)
        thinking_placeholder.empty()
    
    # 流式显示消息
    message_placeholder = st.empty()
    displayed_content = ""
    timestamp = datetime.now().strftime("%H:%M:%S")

    # 按词语流式显示（更自然的效果）
    words = response.split()
    for i, word in enumerate(words):
        displayed_content += word + " "

        # 使用占位符更新显示
        with message_placeholder.container():
            render_message_bubble(agent_id, displayed_content.strip(), timestamp,
                                st.session_state.agents, is_streaming=True)

        # 控制显示速度
        time.sleep(typing_speed * 10)  # 按词显示，速度稍慢

    # 最终显示（移除光标）
    with message_placeholder.container():
        render_message_bubble(agent_id, displayed_content.strip(), timestamp, st.session_state.agents)

    return displayed_content.strip()


def main():
    """主函数"""
    
    # 页面标题
    st.markdown("""
    <div style="text-align: center; padding: 20px 0; border-bottom: 1px solid #E5E5EA; margin-bottom: 20px;">
        <h1 style="color: #333; margin: 0;">💬 战略推演群聊</h1>
        <p style="color: #666; margin: 5px 0 0 0;">微信风格的AI多人讨论</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 初始化数据
    if 'agents' not in st.session_state:
        st.session_state.agents = {
            "trump": {"name": "特朗普", "role": "美国前总统"},
            "biden": {"name": "拜登", "role": "美国现任总统"},
            "military_expert": {"name": "军事专家", "role": "军事战略专家"},
            "political_expert": {"name": "政治专家", "role": "政治学专家"},
            "moderator": {"name": "推演导调", "role": "主持人"}
        }
    
    if 'scenario' not in st.session_state:
        st.session_state.scenario = {
            "title": "中东军事干预决策",
            "context": "盟友请求美国在地区冲突中提供军事支持，美国应如何回应？"
        }
    
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'selected_agents' not in st.session_state:
        st.session_state.selected_agents = ["trump", "military_expert", "political_expert"]
    
    if 'user_agents' not in st.session_state:
        st.session_state.user_agents = []
    
    if 'current_round' not in st.session_state:
        st.session_state.current_round = 0

    if 'simulation_phase' not in st.session_state:
        st.session_state.simulation_phase = "discussion"  # discussion, summary, decision

    if 'max_rounds' not in st.session_state:
        st.session_state.max_rounds = 6  # 每个智能体发言2轮

    if 'decision_maker' not in st.session_state:
        st.session_state.decision_maker = "trump"  # 默认特朗普做最终决策
    
    # 侧边栏设置
    with st.sidebar:
        st.markdown("### 🎮 群聊设置")
        
        # 场景信息
        st.info(f"**当前场景**\n{st.session_state.scenario['title']}")

        # 推演阶段显示
        phase_map = {
            "discussion": "🗣️ 讨论阶段",
            "summary": "📋 总结阶段",
            "decision": "⚖️ 决策阶段"
        }
        current_phase = phase_map.get(st.session_state.simulation_phase, "🗣️ 讨论阶段")
        st.markdown(f"**当前阶段**: {current_phase}")

        if st.session_state.simulation_phase == "discussion":
            progress = min(st.session_state.current_round / st.session_state.max_rounds, 1.0)
            st.progress(progress, text=f"讨论进度: {st.session_state.current_round}/{st.session_state.max_rounds}")

        # 参与者选择
        st.markdown("**👥 群成员**")
        
        # AI智能体
        for agent_id, agent in st.session_state.agents.items():
            if st.checkbox(f"{agent['name']}", value=agent_id in st.session_state.selected_agents, key=f"ai_{agent_id}"):
                if agent_id not in st.session_state.selected_agents:
                    st.session_state.selected_agents.append(agent_id)
            else:
                if agent_id in st.session_state.selected_agents:
                    st.session_state.selected_agents.remove(agent_id)
        
        # 用户智能体
        st.markdown("**👤 用户角色**")
        for user_agent in st.session_state.user_agents:
            agent_id = f"user_{user_agent}"
            if st.checkbox(f"👤 {user_agent}", value=agent_id in st.session_state.selected_agents, key=f"user_{user_agent}"):
                if agent_id not in st.session_state.selected_agents:
                    st.session_state.selected_agents.append(agent_id)
            else:
                if agent_id in st.session_state.selected_agents:
                    st.session_state.selected_agents.remove(agent_id)
        
        # 添加用户角色
        new_user = st.text_input("添加用户角色:", placeholder="例如: 张三")
        if st.button("➕ 添加") and new_user:
            if new_user not in st.session_state.user_agents:
                st.session_state.user_agents.append(new_user)
                st.rerun()
        
        # 设置选项
        st.markdown("**⚙️ 推演设置**")
        show_thinking = st.checkbox("🧠 显示思考过程", value=False)
        enable_stream = st.checkbox("🌊 流式输出", value=True)
        auto_mode = st.checkbox("🔄 自动轮流", value=False)

        # 推演参数设置
        st.session_state.max_rounds = st.slider("讨论轮数", 3, 12, st.session_state.max_rounds)

        # 决策者选择
        decision_makers = [agent_id for agent_id in st.session_state.selected_agents
                          if agent_id in ["trump", "biden"] and not agent_id.startswith("user_")]
        if decision_makers:
            st.session_state.decision_maker = st.selectbox(
                "最终决策者",
                decision_makers,
                format_func=lambda x: st.session_state.agents[x]["name"],
                index=decision_makers.index(st.session_state.decision_maker) if st.session_state.decision_maker in decision_makers else 0
            )
        
        # 控制按钮
        st.markdown("**🎮 控制面板**")
        
        if st.button("🎬 开始讨论", use_container_width=True):
            if not st.session_state.messages:
                # 确保导调在选择的智能体中
                if "moderator" not in st.session_state.selected_agents:
                    st.session_state.selected_agents.insert(0, "moderator")

                # 导调开场发言
                moderator_response = f"""各位参与者，欢迎进入{st.session_state.scenario['title']}推演。

当前场景：{st.session_state.scenario['context']}

请各位基于以下维度展开分析：
1. 战略目标与风险评估
2. 国际法框架与合法性
3. 地缘政治影响
4. 国内政治因素

请依次发言，深入分析各自观点。"""

                opening_msg = {
                    "speaker": "moderator",
                    "content": moderator_response,
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                st.session_state.messages.append(opening_msg)

                # 重置轮次，确保从导调后开始
                st.session_state.current_round = 1
                st.rerun()
        
        # 根据阶段显示不同的按钮
        if st.session_state.simulation_phase == "discussion":
            if st.button("➡️ 下一位发言", use_container_width=True):
                handle_next_speaker(show_thinking)

        elif st.session_state.simulation_phase == "summary":
            if st.button("📋 生成总结", use_container_width=True):
                generate_summary()

        elif st.session_state.simulation_phase == "decision":
            if st.button("⚖️ 最终决策", use_container_width=True):
                generate_final_decision()

        # 手动阶段控制
        st.markdown("**🎮 阶段控制**")
        if st.button("📋 进入总结阶段", use_container_width=True):
            st.session_state.simulation_phase = "summary"
            st.rerun()

        if st.button("⚖️ 进入决策阶段", use_container_width=True):
            st.session_state.simulation_phase = "decision"
            st.rerun()
        
        if st.button("🗑️ 重新开始", use_container_width=True):
            st.session_state.messages = []
            st.session_state.current_round = 0
            st.session_state.simulation_phase = "discussion"
            st.success("✅ 推演已重置")
            st.rerun()
    
    # 主聊天区域
    chat_container = st.container()
    
    with chat_container:
        # 显示所有消息
        for message in st.session_state.messages:
            render_message_bubble(
                message["speaker"],
                message["content"], 
                message["timestamp"],
                st.session_state.agents
            )
    
    # 用户输入区域
    st.markdown("---")
    
    # 检查当前发言者（仅针对用户智能体显示输入界面）
    if st.session_state.selected_agents and st.session_state.messages:
        available_speakers = [agent for agent in st.session_state.selected_agents if agent != "moderator"]

        if available_speakers:
            speaker_index = (st.session_state.current_round - 1) % len(available_speakers)
            current_speaker = available_speakers[speaker_index]

            if current_speaker.startswith("user_"):
                # 用户智能体输入
                user_name = current_speaker.replace("user_", "")
                st.markdown(f"### 💬 轮到 {user_name} 发言")
                st.info(f"请以 {user_name} 的身份参与讨论")

                user_input = st.text_area(f"以 {user_name} 的身份发言:", height=100, key="user_input")

                col1, col2 = st.columns(2)
                with col1:
                    if st.button("📤 发送", type="primary", use_container_width=True):
                        if user_input:
                            new_msg = {
                                "speaker": current_speaker,
                                "content": user_input,
                                "timestamp": datetime.now().strftime("%H:%M:%S")
                            }
                            st.session_state.messages.append(new_msg)
                            st.session_state.current_round += 1
                            st.rerun()

                with col2:
                    if st.button("🤖 AI代发", use_container_width=True):
                        temp_agent = {"name": user_name, "role": f"用户角色-{user_name}"}
                        with st.spinner(f"{user_name} (AI代发) 正在思考..."):
                            response = stream_ai_response(current_speaker, temp_agent, st.session_state.scenario, st.session_state.messages, show_thinking)

                        new_msg = {
                            "speaker": current_speaker,
                            "content": response,
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        }
                        st.session_state.messages.append(new_msg)
                        st.session_state.current_round += 1
                        st.rerun()

                return  # 如果是用户智能体轮次，就不显示普通输入框
    
    # 普通用户输入
    st.markdown("### 💬 参与讨论")
    user_input = st.text_area("输入您的观点:", height=100, key="general_input")
    
    if st.button("📤 发送消息", type="primary"):
        if user_input:
            new_msg = {
                "speaker": "user",
                "content": user_input,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.messages.append(new_msg)
            st.rerun()


if __name__ == "__main__":
    main()
